#pragma once
#include <d3d11.h>
#include <DirectXMath.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include "../Vectors/Vector2.h"
#include "../../../imgui/imgui.h"

namespace DMARender {

    // Vertex structure for SDF rendering
    struct SDFVertex {
        DirectX::XMFLOAT2 position;
        DirectX::XMFLOAT2 texCoord;
        DirectX::XMFLOAT4 color;
    };

    // Simple glyph info structure
    struct SimpleGlyphInfo {
        int width;
        int height;
        int offsetX;
        int offsetY;
        float advanceX;
    };

    // Glyph information for SDF rendering
    struct SDFGlyph {
        char character;
        float x0, y0, x1, y1;  // Atlas coordinates (normalized 0-1)
        float xoff, yoff;      // Offset from baseline
        float xadvance;        // Horizontal advance
        int width, height;     // Glyph dimensions in pixels
        float bearingX, bearingY; // Glyph bearing
    };

    // SDF Font Atlas structure
    struct SDFFontAtlas {
        std::string fontName;
        float fontSize;
        int atlasWidth;
        int atlasHeight;
        float distanceRange;
        ID3D11Texture2D* atlasTexture = nullptr;
        ID3D11ShaderResourceView* atlasSRV = nullptr;
        std::unordered_map<char, SDFGlyph> glyphs;
        float lineHeight;
        float ascender;
        float descender;
        
        ~SDFFontAtlas() {
            if (atlasSRV) atlasSRV->Release();
            if (atlasTexture) atlasTexture->Release();
        }
    };

    // Constant buffer structure for SDF shaders
    struct SDFConstants {
        DirectX::XMMATRIX projection;
        DirectX::XMFLOAT4 textColor;
        DirectX::XMFLOAT4 outlineColor;
        float outlineThickness;
        float shadowOffset;
        float glowRadius;
        float distanceRange;
        DirectX::XMFLOAT2 screenSize;
        float padding[2]; // Align to 16 bytes
    };

    // Text rendering batch for performance
    struct TextRenderBatch {
        std::vector<SDFVertex> vertices;
        std::vector<uint16_t> indices;
        size_t vertexCount = 0;
        size_t indexCount = 0;
        
        void clear() {
            vertices.clear();
            indices.clear();
            vertexCount = 0;
            indexCount = 0;
        }
        
        void reserve(size_t numGlyphs) {
            vertices.reserve(numGlyphs * 4);
            indices.reserve(numGlyphs * 6);
        }
    };

    class SDFFontRenderer {
    private:
        // DirectX resources
        ID3D11Device* d3dDevice = nullptr;
        ID3D11DeviceContext* d3dContext = nullptr;
        ID3D11VertexShader* vertexShader = nullptr;
        ID3D11PixelShader* pixelShader = nullptr;
        ID3D11InputLayout* inputLayout = nullptr;
        ID3D11Buffer* constantBuffer = nullptr;
        ID3D11Buffer* vertexBuffer = nullptr;
        ID3D11Buffer* indexBuffer = nullptr;
        ID3D11BlendState* blendState = nullptr;
        ID3D11RasterizerState* rasterizerState = nullptr;
        ID3D11SamplerState* samplerState = nullptr;
        ID3D11DepthStencilState* depthStencilState = nullptr;
        
        // Font management
        std::vector<std::unique_ptr<SDFFontAtlas>> fontAtlases;
        size_t currentFontIndex = 0;
        
        // Rendering state
        TextRenderBatch renderBatch;
        SDFConstants constants = {};
        bool initialized = false;
        
        // Settings
        float fontAlpha = 1.0f;
        float outlineThickness = 0.0f;
        float shadowOffset = 0.0f;
        float glowRadius = 0.0f;
        float distanceRange = 4.0f;
        
        // Buffer sizes
        static const size_t MAX_VERTICES = 65536;
        static const size_t MAX_INDICES = 98304; // 65536 * 1.5

        // Helper methods
        bool CreateShaders();
        bool CreateBuffersAndStates();
        void GenerateSDFFromBitmap(const unsigned char* bitmap, int width, int height, 
                                 unsigned char* sdfOutput, float distanceRange);
        bool LoadFontFromImGui(float fontSize, 
                             std::unordered_map<char, SDFGlyph>& glyphs, 
                             std::vector<unsigned char>& atlasData, 
                             int& atlasWidth, int& atlasHeight,
                             float& lineHeight, float& ascender, float& descender);
        void PackGlyphsIntoAtlas(const std::vector<std::pair<char, std::vector<unsigned char>>>& glyphBitmaps,
                               const std::vector<std::pair<char, SimpleGlyphInfo>>& glyphInfos,
                               std::unordered_map<char, SDFGlyph>& glyphs,
                               std::vector<unsigned char>& atlasData,
                               int& atlasWidth, int& atlasHeight);
        void AddGlyphToRenderBatch(char c, float x, float y, float scale, ImU32 color);
        void FlushRenderBatch();
        void UpdateConstantBuffer();

    public:
        SDFFontRenderer() = default;
        ~SDFFontRenderer();

        // Initialization and shutdown
        bool Initialize(ID3D11Device* device, ID3D11DeviceContext* context);
        void Shutdown();
        bool IsInitialized() const { return initialized; }

        // Font atlas management
        bool CreateSDFFontAtlas(const std::string& fontPath, float fontSize, int atlasSize = 1024);
        void SetCurrentFont(size_t index) { if (index < fontAtlases.size()) currentFontIndex = index; }
        size_t GetFontCount() const { return fontAtlases.size(); }
        std::string GetFontName(int index) const;

        // Text rendering
        void RenderText(const std::string& text, const Vector2& position, float size, ImU32 color);
        void RenderTextList(const std::vector<std::string>& strings, const Vector2& position, float size, ImU32 color);
        Vector2 CalcTextSize(const std::string& text, float size);

        // Settings
        void SetFontAlpha(float alpha) { fontAlpha = alpha; }
        void SetOutlineThickness(float thickness) { outlineThickness = thickness; }
        void SetShadowOffset(float offset) { shadowOffset = offset; }
        void SetGlowRadius(float radius) { glowRadius = radius; }
        void SetDistanceRange(float range) { distanceRange = range; }
        
        float GetFontAlpha() const { return fontAlpha; }
        float GetOutlineThickness() const { return outlineThickness; }
        float GetShadowOffset() const { return shadowOffset; }
        float GetGlowRadius() const { return glowRadius; }
        float GetDistanceRange() const { return distanceRange; }
    };

} // namespace DMARender 