# CheatEngine-DMA
Cheat Engine Plugin for DMA users ADD MORE STUFF HERE, BRIEF EXPLANATION OF WHAT IT IS IG?

# Features
* Attach to a Process
* Read Memory
* Write Memory
* Search Memory
* Browse Memory
* Module Iteration
* Process Iteration
* Thread Iteration
* Pointer Scans
* Pointer Map Generation


Functions not mentioned here may or may not work, I don't have any plans to support the other features unless I personally need them.

# Building
You can easily compile this source using Visual Studio, if you use another IDE you'll have to figure it out yourself.

# Usage

1. Add the binary (that you compiled) to the same folder as Cheat Engine.

2. Open CheatEngine, click on Edit (Top left) click on Plugins -> Add New -> Add the compiled binary & Activate it.

# License
See the License file on the root of this repository for more info.
