#pragma once
#include "DMARender/includes.h"
#include "../MemoryUpdater/MemoryUpdater.h"
#include "../../Structs/SkeletonESP.h"
#include "../../Structs/HealthBarESP.h"
#include "../../Structs/PersonalHealthDisplay.h"
#include <fstream>
#include <set>
#include <unordered_map>
#include <chrono>
#include <map>

// Forward declarations for ESP debug logging functions
void logSuspiciousEntity(std::shared_ptr<DayZ::Entity> entity, const std::string& reason, bool loggingEnabled);
void logAllEntities(std::shared_ptr<DayZ::Entity> entity, const std::string& category);

namespace DayZ {
	// Selective cache entry structure for player names only
	struct PlayerNameCacheEntry {
		std::chrono::steady_clock::time_point timestamp;
		std::string playerName;
		std::string steamId;
		bool isValid;
	};

	// Cache entry structure for zombie and animal names
	struct EntityNameCacheEntry {
		std::chrono::steady_clock::time_point timestamp;
		std::string entityName;
		bool isValid;
	};

	// Position cache entry (for temporary position caching during frame processing)
	struct PositionCacheEntry {
		DMARender::Vector3 position;
		DMARender::Vector2 screenPos;
		float distance;
		bool isVisible;
		std::chrono::steady_clock::time_point timestamp;
	};

	class OverlayAdapter : public DMARender::IOverlay {

		//DayZ::MemoryUpdater* memUpdater;


		std::shared_ptr<DayZ::MemoryUpdater> memUpdater; // Shared Pointer
		std::shared_ptr<DMARender::RenderBridge> renderBridge; // Shared Pointer
		//std::set<std::string> favoriteSteamIDs; // SteamIDs from textfile
		//void loadFavoriteSteamIDs(const std::string& filePath); // function to load SteamIDs from textfile

		// Selective caching system - cache names but not positions
		std::unordered_map<uint32_t, PlayerNameCacheEntry> playerNameCache;
		std::unordered_map<uint32_t, EntityNameCacheEntry> zombieNameCache;
		std::unordered_map<uint32_t, EntityNameCacheEntry> animalNameCache;
		std::unordered_map<uint32_t, PositionCacheEntry> positionCache; // Temporary frame-based position cache
		const std::chrono::milliseconds NAME_CACHE_DURATION{1800000}; // Cache player names for 30 minutes (names almost never change)
		const std::chrono::milliseconds ENTITY_NAME_CACHE_DURATION{1800000}; // Cache zombie/animal names for 30 minutes (names never change)
		const std::chrono::milliseconds POSITION_CACHE_DURATION{3}; // Cache positions for only 1 frame (16ms at 60fps)
		const float CACHE_DISTANCE_THRESHOLD = 10.0f; // Distance threshold for cache invalidation
		const float MIN_RENDER_DISTANCE = 2.0f; // Minimum render distance
		std::vector<std::shared_ptr<DayZ::Entity>> batchEntities;

		// Multithreaded skeleton processing
		std::vector<std::shared_ptr<DayZ::Entity>> skeletonProcessingEntities;

		bool WorldToScreenDayZ(DayZ::Camera* camera, const DMARender::Vector3& position, DMARender::Vector2& outVector);
		
		// WorldToScreen batching optimization - Phase 1
		struct BatchedScreenPosition {
			DMARender::Vector2 screenPos;
			bool isVisible;
			float distance;
			size_t originalIndex;
		};
		
		std::vector<BatchedScreenPosition> WorldToScreenBatch(DayZ::Camera* camera, const std::vector<DMARender::Vector3>& positions);
		
		// Helper function for batching
		std::vector<DMARender::Vector3> extractEntityPositions(const std::vector<std::shared_ptr<DayZ::Entity>>& entities);
		
		// Phase 2: Distance-based batching optimization
		struct EntityDistanceInfo {
			std::shared_ptr<DayZ::Entity> entity;
			float distance;
			size_t originalIndex;
			BatchedScreenPosition originScreen;
			BatchedScreenPosition topScreen;
		};
		
		enum class RenderQuality {
			HIGH_DETAIL,    // 0-100m: Full rendering with all features
			MEDIUM_DETAIL,  // 100-300m: Reduced features, basic rendering
			LOW_DETAIL,     // 300-500m: Minimal rendering, distance only
			SKIP_RENDER     // 500m+: Skip rendering entirely
		};
		
		std::vector<EntityDistanceInfo> createDistanceSortedBatch(const std::vector<std::shared_ptr<DayZ::Entity>>& entities, 
																  const std::vector<BatchedScreenPosition>& batchedResults);
		RenderQuality determineRenderQuality(float distance, bool isPlayer, bool isZombie, bool isAnimal);
	
	// Phase 3: Entity consolidation optimization
	struct ConsolidatedEntity {
		std::shared_ptr<DayZ::Entity> entity;
		std::string sourceTable;
		uint32_t entityId;
		bool isLootItem;
		bool isAliveEntity;
	};
	
	std::vector<ConsolidatedEntity> consolidateEntityTables();
	std::vector<std::shared_ptr<DayZ::Entity>> extractUniqueEntities(const std::vector<ConsolidatedEntity>& consolidated, 
																	  bool includeLoot, bool includeAlive);
	
	// Phase 4: Frustum culling optimization
	struct FrustumPlane {
		DMARender::Vector3 normal;
		float distance;
	};
	
	struct CameraFrustum {
		FrustumPlane left, right, top, bottom, near_plane, far_plane;
		float fov;
		float aspect;
		float near_dist;
		float far_dist;
	};
	
	CameraFrustum calculateCameraFrustum(DayZ::Camera* camera);
	bool isPointInFrustum(const DMARender::Vector3& point, const CameraFrustum& frustum);
	bool isEntityInFrustum(const std::shared_ptr<DayZ::Entity>& entity, const CameraFrustum& frustum);
	std::vector<std::shared_ptr<DayZ::Entity>> frustumCullEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities, 
																   const CameraFrustum& frustum);
	
	// Phase 5: Type-based rendering optimization
	enum class EntityRenderType {
		PLAYER,
		ZOMBIE,
		ANIMAL,
		VEHICLE,
		BOAT,
		WEAPON,
		CLOTHING,
		BACKPACK,
		FOOD,
		AMMO,
		RARE_ITEM,
		GROUND_ITEM,
		DEAD_PLAYER,
		DEAD_ANIMAL,
		OPTIC,
		BASE_BUILDING,
		MELEE,
		EXPLOSIVE,
		PROXY_MAGAZINE,
		CONTAINER,  // New: For storage containers
		COOKING,    // New: For cooking items
		CAMPING,    // New: For camping items
		STASH,      // New: For stash containers
		CUSTOM_CONTAINER, // New: For custom/modded containers
		OTHER
	};
	
	struct TypedEntity {
		std::shared_ptr<DayZ::Entity> entity;
		EntityRenderType type;
		float distance;
		ImU32 color;
		std::string displayText;
		DMARender::Vector2 screenPos;
		bool isVisible;
	};
	
	std::vector<TypedEntity> categorizeEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities, 
												DayZ::Camera* camera, bool isLootMode);
	EntityRenderType determineEntityType(const std::shared_ptr<DayZ::Entity>& entity);
	ImU32 getEntityColor(const std::shared_ptr<DayZ::Entity>& entity, EntityRenderType type);
	void renderEntitiesByType(const std::vector<TypedEntity>& typedEntities);
	
	void drawLoot(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities);
	void drawAliveEntities(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities, Scoreboard* scoreboard);
	void drawDebugInformation(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities);

	// Player List functionality for ESP overlay
	void drawPlayerList(DayZ::Camera* camera, Scoreboard* scoreboard);
	void drawServerPlayerList(std::shared_ptr<DayZ::Scoreboard> scoreboard);

		// Performance optimization methods
		bool isEntityCached(uint32_t entityId, float currentDistance);
		void updateEntityCache(uint32_t entityId, float distance, bool isVisible);
		void clearExpiredCache();
		void batchProcessEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities);
		bool shouldRenderEntity(const DMARender::Vector3& position, const DMARender::Vector3& cameraPos, float maxDistance);

		// PHASE 2: Batch processing optimization methods
		bool isEntityInSimpleFrustum(DayZ::Camera* camera, const DMARender::Vector3& position);
		DMARender::Vector2 getApproximateScreenPos(DayZ::Camera* camera, const DMARender::Vector3& position);

		// PHASE 2: Performance monitoring structure
		struct PerformanceMonitor {
			bool performanceMode = false;
			int entitiesProcessedLastFrame = 0;
			int entitiesSkippedLastFrame = 0;
			std::chrono::steady_clock::time_point lastUpdate = std::chrono::steady_clock::now();
		} perfMonitor;

		// Selective caching methods - cache names but not positions
		bool isPlayerNameCached(uint32_t entityId);
		std::string getCachedPlayerName(uint32_t entityId);
		void cachePlayerName(uint32_t entityId, const std::string& playerName, const std::string& steamId);
		void clearExpiredNameCache();
		void clearAllNameCache(); // Manual cache clearing if needed
		
		// Zombie name caching methods
		bool isZombieNameCached(uint32_t entityId);
		std::string getCachedZombieName(uint32_t entityId);
		void cacheZombieName(uint32_t entityId, const std::string& zombieName);
		void clearExpiredZombieNameCache();
		void clearAllZombieNameCache();
		
		// Animal name caching methods
		bool isAnimalNameCached(uint32_t entityId);
		std::string getCachedAnimalName(uint32_t entityId);
		void cacheAnimalName(uint32_t entityId, const std::string& animalName);
		void clearExpiredAnimalNameCache();
		void clearAllAnimalNameCache();
		
		// Position caching (temporary, frame-based only)
		bool isPositionCached(uint32_t entityId);
		DayZ::PositionCacheEntry getCachedPosition(uint32_t entityId);
		void cachePosition(uint32_t entityId, const DMARender::Vector3& position, const DMARender::Vector2& screenPos, float distance, bool isVisible);
		void clearExpiredPositionCache();

		//Fonts
		ImFont* lootFontFar;

		// DayZ2-style text overlap prevention
		std::map<std::pair<float, float>, bool> textPositions;
		void clearTextPositions() { textPositions.clear(); }
		void clearTextPositionsInArea(const DMARender::Vector2& center, float radius);
		DMARender::Vector2 getAvailableTextPosition(const DMARender::Vector2& basePosition, float lineHeight = 20.0f, bool useOverlapPrevention = true);

		// DayZ2-style enhanced font system helpers (simplified)
		enum class FontType { PLAYER_NAME, PLAYER_DISTANCE, PLAYER_HAND, LOOT_NEAR, LOOT_FAR, ZOMBIE_TEXT, ANIMAL_TEXT, UI_TEXT, DEBUG_TEXT };
		void drawEnhancedText(const std::string& text, const DMARender::Vector2& position,
		                     FontType fontType, ImU32 color, bool useStroke = true);
		void drawEnhancedTextWithSize(const std::string& text, const DMARender::Vector2& position,
		                             float fontSize, ImU32 color, bool useStroke = true);
		void drawCenteredTextWithSize(const std::string& text, const DMARender::Vector2& position,
		                             float fontSize, ImU32 color, bool useStroke = true);

		// Aim point calculation for crosshair
	

	private:
		// Performance optimization: Frame counter for caching
		uint32_t currentFrame = 0;
		
		// Font management
		ImFont* lootFont = nullptr;
		ImFont* playerFont = nullptr;

	public:
		OverlayAdapter(std::shared_ptr<DayZ::MemoryUpdater> memUpdater, std::shared_ptr<DMARender::RenderBridge> renderBridge);
		~OverlayAdapter();
		// Inherited via IOverlay
		virtual void DrawOverlay() override;
		virtual void createFonts() override;
	};
}