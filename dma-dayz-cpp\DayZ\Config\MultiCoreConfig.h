#pragma once
#include <thread>
#include <atomic>
#include <fstream>
#include <string>

namespace DayZ {
    /// <summary>
    /// Multi-core optimization configuration for DayZ DMA application
    /// Automatically adapts to system capabilities while allowing manual tuning
    /// </summary>
    class MultiCoreConfig {
    public:
        // Core detection and utilization
        static constexpr int MAX_WORKER_THREADS = 16;
        static constexpr int MIN_CORES_FOR_PARALLEL = 6;
        static constexpr int MIN_CORES_FOR_AFFINITY = 8;
        
        // Timing configurations for different core counts
        struct TimingProfile {
            int nearWorkerMs;      // Near entity update frequency
            int farWorkerMs;       // Far entity update frequency  
            int itemWorkerMs;      // Item update frequency
            int slowWorkerMs;      // Slow entity update frequency
            int scoreboardMs;      // Scoreboard update frequency
            int cameraUpdateMs;    // Camera update frequency
        };
        
        // Predefined timing profiles for different system configurations
        static const TimingProfile MANY_CORE_PROFILE;    // 16+ cores
        static const TimingProfile MEDIUM_CORE_PROFILE;  // 8-15 cores
        static const TimingProfile STANDARD_PROFILE;     // 4-7 cores
        static const TimingProfile MINIMAL_PROFILE;      // <4 cores
        
        // Runtime configuration
        std::atomic<bool> enableParallelProcessing{true};
        std::atomic<bool> enableThreadAffinity{true};
        std::atomic<bool> adaptiveTimings{true};
        std::atomic<int> maxWorkerThreads{8};
        std::atomic<int> selectedProfile{AUTO_DETECT};
        

        
        // Constructor - auto-detects optimal settings
        MultiCoreConfig();
        
        // Get optimal timing profile for current system
        TimingProfile getCurrentProfile() const;
        
        // Get optimal thread count for entity processing
        int getOptimalEntityThreadCount() const;
        
        // Check if parallel processing should be used
        bool shouldUseParallelProcessing() const;
        
        // Check if thread affinity should be set
        bool shouldUseThreadAffinity() const;
        
        // Load/save configuration
        void loadFromFile(const std::string& filename);
        void saveToFile(const std::string& filename) const;
        
        // System information
        int getHardwareCoreCount() const { return hardwareCores; }
        std::string getSystemInfo() const;

        // Performance testing
        void runPerformanceTest();

        // Profile selection
        enum ProfileType { AUTO_DETECT, MANY_CORE, MEDIUM_CORE, STANDARD, MINIMAL };
        void setProfile(ProfileType profile);
        ProfileType getCurrentProfileType() const;
        
    private:
        int hardwareCores;
        TimingProfile currentProfile;
        
        void detectOptimalSettings();
        TimingProfile selectProfileForCoreCount(int cores) const;
    };
    
    // Global configuration instance
    extern MultiCoreConfig g_multiCoreConfig;
}
