#pragma once
#include "../VmmManager/VmmManager.h"
#include <unordered_map>
#include <string>

namespace DMAMem {
    /**
     * \brief BF2042-specific signature scanning and offset management
     * Integrates with the forum-confirmed patterns and provides dynamic offset resolution
     */
    class BF2042Signatures {
    private:
        VmmManager* vmmManager;
        DWORD targetPID;
        std::string processName;
        
        // Cached addresses to avoid repeated scanning
        std::unordered_map<std::string, QWORD> cachedAddresses;
        
        // BF2042 signature patterns from forum analysis
        struct SignaturePattern {
            const char* name;
            const char* pattern;
            const char* module;
            QWORD offset; // Additional offset to apply after finding pattern
        };

        // Forum-confirmed signature patterns
        static const SignaturePattern BF2042_PATTERNS[];

    public:
        /**
         * \brief Constructor
         * \param vmm Pointer to VmmManager instance
         * \param pid Target process ID
         * \param procName Process name (e.g., "bf2042.exe")
         */
        BF2042Signatures(VmmManager* vmm, DWORD pid, const std::string& procName);

        /**
         * \brief Initialize signature scanning and find base addresses
         * \return true if critical signatures were found
         */
        BOOL Initialize();

        /**
         * \brief Find GameContext using forum-confirmed signature
         * \return GameContext address, 0 if not found
         */
        QWORD FindGameContext();

        /**
         * \brief Find AimAssist data structure
         * \return AimAssist address, 0 if not found
         */
        QWORD FindAimAssistData();

        /**
         * \brief Validate that found addresses point to valid structures
         * \return true if validation passes
         */
        BOOL ValidateFoundAddresses();

        /**
         * \brief Get cached address by name
         * \param name Address identifier (e.g., "GameContext", "AimAssist")
         * \return cached address, 0 if not found
         */
        QWORD GetCachedAddress(const std::string& name);

        /**
         * \brief Force re-scan of all signatures (use after game update)
         * \return true if re-scan successful
         */
        BOOL RescanSignatures();

        /**
         * \brief Get all found addresses for debugging
         * \return map of name->address pairs
         */
        std::unordered_map<std::string, QWORD> GetAllAddresses() const;

        /**
         * \brief TypeInfo bruteforce implementation from forum
         * \param className Name of the class to find (e.g., "clientsoldierentity")
         * \param maxSeeds Maximum number of seeds to try
         * \return seed value if found, -1 if not found
         */
        int BruteForceTypeInfoSeed(const std::string& className, int maxSeeds = 10000);

        /**
         * \brief Calculate TypeInfo hash using forum method
         * \param className Class name
         * \param seed Seed value
         * \return SHA256 hash result
         */
        DWORD CalculateTypeInfoHash(const std::string& className, int seed);

        /**
         * \brief Find class by TypeInfo hash
         * \param targetHash Target hash to find
         * \return address of class, 0 if not found
         */
        QWORD FindClassByTypeInfo(DWORD targetHash);

        /**
         * \brief Comprehensive BF2042 structure validation
         * Tests known offsets against found base addresses
         * \return true if structures appear valid
         */
        BOOL ValidateBF2042Structures();

        /**
         * \brief Get module base and size for signature scanning
         * \param moduleName Name of module
         * \param baseAddress Output: module base address
         * \param moduleSize Output: module size
         * \return true if module found
         */
        BOOL GetModuleInfo(const std::string& moduleName, QWORD& baseAddress, SIZE_T& moduleSize);

        /**
         * \brief Scan for pattern with multiple validation attempts
         * Uses different scanning strategies if first attempt fails
         * \param pattern Signature pattern
         * \param moduleName Module to scan in
         * \return address if found, 0 if not found
         */
        QWORD RobustPatternScan(const char* pattern, const std::string& moduleName);

        /**
         * \brief Print diagnostic information about found signatures
         */
        void PrintDiagnostics();

        /**
         * \brief Check if CR3/DTB bypass is needed and attempt fix
         * \return true if memory access is working
         */
        BOOL EnsureMemoryAccess();

        /**
         * \brief Extract GameContext address from instruction at function address
         * \param functionAddr Address of function containing GameContext reference
         * \param pattern The pattern that was matched
         * \return GameContext address if extracted successfully, 0 otherwise
         */
        QWORD ExtractGameContextAddress(QWORD functionAddr, const std::string& pattern);
    };
}
