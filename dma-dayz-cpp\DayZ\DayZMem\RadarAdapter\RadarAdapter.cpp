#pragma once
#include "RadarAdapter.h";
#include "../../Structs/Scoreboard.h"
#include <unordered_set>
#include <chrono>
#include <cmath>
#include "../../../DMALib/includes/DMARender/LootList/LootList.h"

extern ImVec2 g_mainPlayerScreenPos;
extern ImVec2 g_mainPlayerScreenPosBuffered;

// Constructor
DayZ::RadarAdapter::RadarAdapter(std::shared_ptr<DayZ::MemoryUpdater> memUpdater, std::shared_ptr<DMARender::RenderBridge> renderBridge)
	: memUpdater(memUpdater), renderBridge(renderBridge) {
	loadFavoriteSteamIDs("steamids.txt"); // Read "steamids.txt" for your favorite admins

	// Sync Entity system with LootListManager (LootListManager is the master)
	auto lootManager = renderBridge->getLootListManager();
	if (lootManager) {
		// Sync Entity system with LootListManager rare items
		DayZ::Entity::setRareItems(lootManager->getRareItems());
	}
}

// draw loot and dead entities with filters
void DayZ::RadarAdapter::drawLoot(DayZ::Camera* camera, DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform, const std::vector<std::shared_ptr<DayZ::Entity>>& entities)
{
	
	if (!renderBridge) return; // If the render bridge is not initialized, return

	bool showVehicles = renderBridge->shouldShowVehicles();
	bool showBoats = renderBridge->shouldShowBoats();
	bool showGrounditems = renderBridge->shouldShowGrounditems();
	bool showDeadAnimals = renderBridge->shouldShowDeadAnimals();
	bool showDeadPlayers = renderBridge->shouldShowDeadPlayers();
	bool showWeapons = renderBridge->shouldShowWeapons();
	bool showClothing = renderBridge->shouldShowClothing();
	bool showBackpacks = renderBridge->shouldShowBackpacks();
	bool showProxyMagazines = renderBridge->shouldShowProxyMagazines();
	bool showFood = renderBridge->shouldShowFood();
	bool showAmmo = renderBridge->shouldShowAmmo();
	bool showRare = renderBridge->shouldShowRare();
	bool showOptics = renderBridge->shouldShowOptics();
	bool showBase = renderBridge->shouldShowBase();
	bool showMelee = renderBridge->shouldShowMelee();
	bool showExplosives = renderBridge->shouldShowExplosives();
	bool showContainer = renderBridge->shouldShowContainer();
	bool showCooking = renderBridge->shouldShowCooking();
	bool showCamping = renderBridge->shouldShowCamping();
	bool showStash = renderBridge->shouldShowStash();
	bool showCustomContainer = renderBridge->shouldShowCustomContainer();
	int RadarFont2 = renderBridge->shouldRadarFont2();
	int blipSize2 = renderBridge->shouldBlipSize2();
	int LootDistanceDeadzone = renderBridge->shouldLootDistanceDeadzone();



	for (auto const item : entities) {
		if (!item->isValid())
			continue;

		std::string postFix = "";

		ImU32 textCol;

		float dist = camera->InvertedViewTranslation.Dist(item->FutureVisualStatePtr->position);

		// Color for Items and Dead players/animals
		if (item->isPlayer() && item->isDead && showDeadPlayers) {
			textCol = IM_COL32(0, 255, 255, 255);
			postFix = " (Dead)";
		} else if (item->isAnimal() && item->isDead && showDeadAnimals) {
			textCol = IM_COL32(0, 255, 0, 255);
			postFix = " (Dead)";
		} else if (item->isCar() && showVehicles) {
			textCol = IM_COL32(255, 0, 245, 255);
		} else if (item->isBoat() && showBoats) {
			textCol = IM_COL32(255, 0, 245, 255);
		} else if (item->isRare() && showRare) {
			// Get custom color from LootListManager
			auto lootManager = renderBridge->getLootListManager();
			if (lootManager && item->EntityTypePtr->TypeName) {
				std::string itemName = item->EntityTypePtr->TypeName->value;
				textCol = lootManager->getItemColor(itemName);
			} else {
				textCol = IM_COL32(255, 0, 255, 255); // Fallback purple
			}
		} else if (item->isBackpack() && showBackpacks) {
			textCol = IM_COL32(175, 255, 0, 255);
		} else if (item->isClothing() && showClothing) {
			textCol = IM_COL32(255, 255, 0, 255);
		} else if (item->isWeapon() && showWeapons) {
			textCol = IM_COL32(255, 0, 100, 255);
		} else if (item->isProxyMagazines() && showProxyMagazines) {
			textCol = IM_COL32(255, 117, 50, 255);
		} else if (item->isFood() && showFood) {
			textCol = IM_COL32(50, 140, 50, 255);
		} else if (item->isAmmo() && showAmmo) {
			textCol = IM_COL32(255, 117, 50, 255);
		} else if (item->isGroundItem() && showGrounditems) {
			textCol = IM_COL32(255, 255, 255, 255);
		} else if (item->isOptic() && showOptics) {
			textCol = IM_COL32(0, 150, 255, 255);
		} else if (item->isBase() && showBase) {
			textCol = IM_COL32(0, 150, 255, 255);
		} else if (item->isMelee() && showMelee) {
			textCol = IM_COL32(0, 150, 255, 255);
		} else if (item->isExplosives() && showExplosives) {
			textCol = IM_COL32(255, 0, 120, 255);
		} else if (item->isContainer() && showContainer) {
			textCol = IM_COL32(255, 165, 0, 255); // Orange for containers
		} else if (item->isCooking() && showCooking) {
			textCol = IM_COL32(255, 140, 0, 255); // Dark orange for cooking
		} else if (item->isCamping() && showCamping) {
			textCol = IM_COL32(34, 139, 34, 255); // Forest green for camping
		} else if (item->isStash() && showStash) {
			textCol = IM_COL32(139, 69, 19, 255); // Saddle brown for stash
		} else if (item->isCustomContainer() && showCustomContainer) {
			textCol = IM_COL32(255, 140, 0, 255); // Bright orange for custom containers
		}
		else
		{
			continue;
		}
		

		if (dist < LootDistanceDeadzone)
			continue;
		
		auto screenPos = WorldToRadar(curMap, mTransform, item->FutureVisualStatePtr->position);

		std::string displayName;

		// Special handling for custom containers - use TypeName only (no fallbacks)
		if (item->isCustomContainer()) {
			if (item->EntityTypePtr->TypeName &&
				item->EntityTypePtr->TypeName->length > 0 &&
				item->EntityTypePtr->TypeName->length < 400 &&
				item->EntityTypePtr->TypeName->value) {
				displayName = item->EntityTypePtr->TypeName->value;
			}
		}
		else {
			// For all other items, use the best available name
			auto bestName = item->EntityTypePtr->getBestString();
			if (bestName) {
				displayName = bestName->value;

				// Use custom name for rare items if available
				auto lootManager = renderBridge->getLootListManager();
				if (lootManager && item->EntityTypePtr->TypeName) {
					std::string itemName = item->EntityTypePtr->TypeName->value;
					if (lootManager->isRareItem(itemName)) {
						displayName = lootManager->getDisplayName(itemName);
					}
				}
			}
		}

		if (!displayName.empty()) {

			// Check for right-click on the blip area
			ImVec2 mousePos = ImGui::GetMousePos();
			float clickRadius = blipSize2 + 10; // Add some padding for easier clicking

			// Calculate distance from mouse to blip center
			float mouseDist = sqrt(pow(mousePos.x - screenPos.x, 2) + pow(mousePos.y - screenPos.y, 2));

			// Check if mouse is over the blip area
			if (mouseDist <= clickRadius) {
				auto lootManager = renderBridge->getLootListManager();
				bool isCurrentlyRare = false;
				std::string itemName;

				if (lootManager && item->EntityTypePtr->TypeName) {
					itemName = item->EntityTypePtr->TypeName->value;
					isCurrentlyRare = lootManager->isRareItem(itemName);
				}

				// Show different indicators based on current state
				ImU32 saveIndicatorColor = IM_COL32(255, 255, 0, 255);
				std::string indicator = isCurrentlyRare ? " [R]" : " [S]"; // [R]emove or [S]ave
				drawBlip(screenPos, blipSize2, saveIndicatorColor, RadarFont2, 1, { displayName + postFix + indicator });

				// Check for right mouse click to toggle rare status
				if (ImGui::IsMouseClicked(ImGuiMouseButton_Right)) {
					if (lootManager && !itemName.empty()) {
						if (isCurrentlyRare) {
							// Remove from rare items
							lootManager->removeRareItem(itemName);
						} else {
							// Add to rare items with default color and original name as custom name
							lootManager->addRareItem(itemName, IM_COL32(255, 0, 255, 255), itemName);
						}
						// Update Entity system to match LootListManager
						DayZ::Entity::setRareItems(lootManager->getRareItems());
						// Auto-save changes
						lootManager->saveToFile();
					}
				}
			} else {
				// Normal blip drawing
				drawBlip(screenPos, blipSize2, textCol, RadarFont2, 1, { displayName + postFix });
			}
		}

	}
}

void DayZ::RadarAdapter::drawAliveEntities(DayZ::Camera* camera, DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform, const std::vector<std::shared_ptr<DayZ::Entity>>& entities, Scoreboard* scoreboard)
{

	// neutralTransform for my followplayer fuction
	DMARender::MapTransform neutralTransform = mTransform;
	neutralTransform.dragOffsetX = 0;
	neutralTransform.dragOffsetY = 0;
	static ImVec2 lastValidScreenPos = ImVec2(0, 0);

	if (!renderBridge) return; // If the render bridge is not initialized, return

	bool showZombies = renderBridge->shouldShowZombies();
	bool showAnimals = renderBridge->shouldShowAnimals();
	std::string PlayerNameYES = renderBridge->shouldPlayerName();
	int BlipSize = renderBridge->shouldBlipSize();
	int Aimlinelength = renderBridge->shouldAimlinelength();
	int RadarFont = renderBridge->shouldRadarFont();
	int ZombiesBlipSize = renderBridge->shouldZombiesBlipSize();
	int AnimalsBlipSize = renderBridge->shouldAnimalsBlipSize();
	bool showPlayerNameRadar = renderBridge->shouldShowPlayerNameRadar();
	bool showPlayerDistanceRadar = renderBridge->shouldShowPlayerDistanceRadar();
	bool showPlayerHandRadar = renderBridge->shouldShowPlayerHandRadar();


	std::vector<std::string> MainFollowPlayer = { PlayerNameYES };


	for (auto ent : entities) {
		if (!ent->isValid())
			continue;
		if (ent->isDead)
			continue;

		ImU32 blipColor;

		bool ismainplayPlayer = false;
		std::vector<std::string> infoText;


		// neutral MainPlayerPos
		ImVec2 neutralPos = WorldToRadar(curMap, neutralTransform, ent->FutureVisualStatePtr->position);
		// final "MainPlayerScreenPos"
		ImVec2 screenPos = WorldToRadar(curMap, mTransform, ent->FutureVisualStatePtr->position);

		if (ent->isPlayer()) {
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->PlayerName) {
				ismainplayPlayer = std::find(MainFollowPlayer.begin(), MainFollowPlayer.end(), ident->PlayerName->value) != MainFollowPlayer.end();
			}

			// Skip excluded players (but not the main player)
			if (!ismainplayPlayer && ident && ident->SteamID && ident->SteamID->value && renderBridge) {
				if (renderBridge->isPlayerExcluded(std::string(ident->SteamID->value))) {
					continue; // Skip this excluded player
				}
			}

			// Skip bots if bot display is disabled (but not the main player)
			if (!ismainplayPlayer && ent->isBot() && renderBridge && !renderBridge->shouldShowBots()) {
				continue; // Skip this bot
			}
		}

		// ALIFE ENTITIES colors
		if (ismainplayPlayer) {
			blipColor = IM_COL32(0, 255, 0, 255);

			// update global (static) Pos if MainPlayer
			ImVec2 newPos = WorldToRadar(curMap, mTransform, ent->FutureVisualStatePtr->position);
			// try for smooth interpolation
			float smoothFactor = 0.5f;  // Anpassbar
			g_mainPlayerScreenPos = ImVec2(
				lastValidScreenPos.x + (neutralPos.x - lastValidScreenPos.x) * smoothFactor,
				lastValidScreenPos.y + (neutralPos.y - lastValidScreenPos.y) * smoothFactor
			);
			lastValidScreenPos = g_mainPlayerScreenPos;


		}
		else if (ent->isPlayer()) {
			// Check if player is admin for special coloring
			bool isAdmin = false;
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->SteamID && ident->SteamID->value && renderBridge) {
				isAdmin = renderBridge->isPlayerAdmin(std::string(ident->SteamID->value));
			}

			// Use green color for admin players, red for regular players
			if (isAdmin) {
				blipColor = IM_COL32(0, 255, 0, 255); // Green for admin players
			} else {
				blipColor = IM_COL32(255, 0, 0, 255); // Red for regular players
			}
		}
		else if (ent->isZombie() && showZombies) {
			blipColor = IM_COL32(255, 255, 0, 255);
		}
		else if (ent->isAnimal() && showAnimals) {
			blipColor = IM_COL32(0, 255, 0, 130);
		}
		else {
			continue;
		}


		float dist = camera->InvertedViewTranslation.Dist(ent->FutureVisualStatePtr->position);


		if (ent->isAnimal()) {
			auto entBestStr = ent->EntityTypePtr->getBestString();
			if (entBestStr) {
				auto entName = std::string(ent->EntityTypePtr->getBestString()->value);
				infoText.push_back(entName);
			}
			//infoText.push_back(std::format("{:.0f}m", dist)); //show distance for animals
		}
		if (ent->isPlayer()) {
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->PlayerName && !ismainplayPlayer && showPlayerNameRadar) {
				infoText.push_back(ident->PlayerName->value);
			}
			//Add ArmaStringID
			//if (ident->SteamID && !ismainplayPlayer) {
			//infoText.push_back(std::format("SteamID: {}", ident->SteamID->value));
			//}

					if (!ismainplayPlayer && showPlayerHandRadar) {
			// Use cached hand item reading for better performance
			std::string handItemName = ent->getHandItemCached(memUpdater->getVMM(), memUpdater->getPid(), 0); // Use frame 0 for radar (no caching needed)
			if (!handItemName.empty()) {
				infoText.push_back(handItemName);
			}
			// Don't add anything to infoText if no hand item - keeps radar display clean
		}
			if (!ismainplayPlayer && showPlayerDistanceRadar) {
				infoText.push_back(std::format("{:.0f}m", dist));
			}
		}

		if (ent->isPlayer()) {
			drawBlipDirectional(screenPos, BlipSize, blipColor, RadarFont, 1, infoText, ent->FutureVisualStatePtr->getRotationCorrected(), Aimlinelength);
		}
		if (ent->isZombie()) {
			drawBlip(screenPos, ZombiesBlipSize, blipColor, RadarFont, 1, infoText);
		}
		if (ent->isAnimal()) {
			drawBlip(screenPos, AnimalsBlipSize, blipColor, RadarFont, 1, infoText);
		}
		else {
			continue;
			//drawBlip(screenPos, BlipSize, blipColor, RadarFont, 1, infoText);
		}
	}
}

void DayZ::RadarAdapter::drawPlayerList(DayZ::Camera* camera, Scoreboard* scoreboard) {
	static bool includeSlowEntities = false; // Checkbox-Status

	// load small table font
	//ImGui::PushFont(tableFont);
	ImGui::Begin("Local Player List", nullptr, ImGuiWindowFlags_AlwaysAutoResize);

	// add checkbox for SlowEntityTable, experimenal to find invis admins
	ImGui::Checkbox("Include SlowEntityTable", &includeSlowEntities);

	// slow entity table
	std::set<uint32_t> slowEntityIDs;
	if (includeSlowEntities) {
		for (const auto& slowEntity : memUpdater->getSlowEntityTable()->resolvedEntities) {
			if (slowEntity->isPlayer() && slowEntity->isValid()) {
				slowEntityIDs.insert(slowEntity->NetworkID);
			}
		}
	}

	// Imgui table setup
	if (ImGui::BeginTable("PlayerTable", 2, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg)) {
		ImGui::TableSetupColumn("Distance");
		ImGui::TableSetupColumn("Player Name");
		//ImGui::TableSetupColumn("Steam ID");
		//ImGui::TableSetupColumn("Network ID");
		//ImGui::TableSetupColumn("Model Name");
		ImGui::TableHeadersRow();

		// different approach to iterate over entities tables
		auto processEntities = [&](const std::vector<std::shared_ptr<DayZ::Entity>>& entities) {
			for (auto& entity : entities) {
				if (!entity->isPlayer() || !entity->isValid()) continue;

				// Skip bots if bot display is disabled
				if (entity->isBot() && renderBridge && !renderBridge->shouldShowBots()) {
					continue; // Skip this bot
				}

				auto ident = entity->getPlayerIdentity(scoreboard);
				if (!ident) continue;

				// bad working validation
				const char* playerName = (ident->PlayerName && ident->PlayerName->value)
					? ident->PlayerName->value
					: "Unknown";
				const char* steamID = (ident->SteamID && ident->SteamID->value)
					? ident->SteamID->value
					: "Unknown";

				uint32_t networkID = entity->NetworkID;

				// Add Modelname
				//const char* modelName = (entity->EntityTypePtr && entity->EntityTypePtr->ModelName && entity->EntityTypePtr->ModelName->value)
				//	? entity->EntityTypePtr->ModelName->value
				//	: "Unknown";

				float distance = camera->InvertedViewTranslation.Dist(entity->FutureVisualStatePtr->position);

				// dont show self
				//if (distance < 3.1)
					//continue;

				// different color for SlowEntityTable
				bool isSlowEntity = slowEntityIDs.count(networkID) > 0;
				ImU32 rowColor = isSlowEntity ? IM_COL32(255, 255, 0, 255) : IM_COL32(255, 255, 255, 255);

				ImGui::TableNextRow(ImGuiTableRowFlags_None, 0);
				ImGui::PushStyleColor(ImGuiCol_Text, rowColor);

				// Distance
				ImGui::TableNextColumn();
				ImGui::Text("%dm", static_cast<int>(distance)); // Ohne Nachkommastelle

				// Player Name
				ImGui::TableNextColumn();
				ImGui::Text("%s", playerName);

				// Network ID
				//ImGui::TableNextColumn();
				//ImGui::Text("%u", networkID);

				// Model Name
				//ImGui::TableNextColumn();
				//ImGui::Text("%s", modelName);

				//ImGui::PopStyleColor();
			}
			};

		
		processEntities(memUpdater->getNearEntityTable()->resolvedEntities);
		processEntities(memUpdater->getFarEntityTable()->resolvedEntities);

		ImGui::EndTable();
	}
	//ImGui::PopFont(); // back to default font
	ImGui::End();
}

void DayZ::RadarAdapter::drawServerPlayerList(std::shared_ptr<DayZ::Scoreboard> scoreboard) {

	std::unordered_set<int> seenNetworkIDs;

		// table setup
	//ImGui::PushFont(tableFont);
	ImGui::Begin("Server Player List", nullptr, ImGuiWindowFlags_AlwaysAutoResize);

	ImGui::Text("Click to Copy Profile Link to Clipboard");
	ImGui::Text("Right-click player to exclude/include from ESP/Radar");
	ImGui::Text("Middle-click player to add/remove from Admin List");
	ImGui::Spacing();
	if (ImGui::BeginTable("ServerPlayerTable", 3, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg)) {
		ImGui::TableSetupColumn("#"); 
		ImGui::TableSetupColumn("Player Name");
		ImGui::TableSetupColumn("Steam ID");
		/*ImGui::TableSetupColumn("Network ID");*/
		ImGui::TableHeadersRow();

		int rowIndex = 1; // start from 1

		for (const auto& identity : scoreboard->resolvedIdentities) {
			if (!identity) continue;

			// bad working validation (also filters out bots with NetworkID = 1)
			if (identity->NetworkID <= 9999 ||
				!identity->SteamID || !identity->SteamID->value ||
				strncmp(identity->SteamID->value, "7656", 4) != 0 ||
				!identity->PlayerName || !identity->PlayerName->value || !seenNetworkIDs.insert(identity->NetworkID).second) {
				continue;
			}

			const char* playerName = identity->PlayerName->value;
			const char* steamID = identity->SteamID->value;
			uint32_t networkID = identity->NetworkID;

			// Check for admin status and exclusion status
			bool isAdmin = renderBridge && renderBridge->isPlayerAdmin(std::string(steamID));
			bool isExcluded = renderBridge && renderBridge->isPlayerExcluded(std::string(steamID));

			// Set row color: red for excluded, green for admin, white for normal
			ImU32 rowColor = isExcluded ? IM_COL32(255, 100, 100, 255) :
							(isAdmin ? IM_COL32(0, 255, 0, 255) : IM_COL32(255, 255, 255, 255));

			ImGui::TableNextRow();
			ImGui::PushStyleColor(ImGuiCol_Text, rowColor);

			// numbering
			ImGui::TableNextColumn();
			ImGui::Text("%d", rowIndex++);

			// Player Name with exclusion and admin indicators
			ImGui::TableNextColumn();
			if (isExcluded) {
				ImGui::Text("[EXCLUDED] %s", playerName);
			} else if (isAdmin) {
				ImGui::Text("[ADMIN] %s", playerName);
			} else {
				ImGui::Text("%s", playerName);
			}

			// Steam ID with clickable functionality
			ImGui::TableNextColumn();
			if (ImGui::Selectable(steamID, false, ImGuiSelectableFlags_SpanAllColumns)) {
				std::string steamLink = "https://steamcommunity.com/profiles/";
				steamLink += steamID;
				ImGui::SetClipboardText(steamLink.c_str()); // copy to clipboard
			}
			if (ImGui::IsItemHovered()) {
				ImGui::SetTooltip("Left-click to copy Steam profile URL\nRight-click to exclude/include from ESP/Radar\nMiddle-click to add/remove from Admin List");
			}

			// Handle right-click context menu (exclusion)
			if (ImGui::IsItemClicked(ImGuiMouseButton_Right)) {
				if (renderBridge) {
					if (isExcluded) {
						renderBridge->removeExcludedPlayer(std::string(steamID));
					} else {
						renderBridge->addExcludedPlayer(std::string(steamID));
					}
				}
			}

			// Handle middle-click context menu (admin list)
			if (ImGui::IsItemClicked(ImGuiMouseButton_Middle)) {
				if (renderBridge) {
					if (isAdmin) {
						renderBridge->removeAdminPlayer(std::string(steamID));
					} else {
						renderBridge->addAdminPlayer(std::string(steamID));
					}
				}
			}
			ImGui::PopStyleColor();
			// Network ID
			//ImGui::TableNextColumn();
			//ImGui::Text("%u", networkID);
		}

		//ImGui::PopFont();
		ImGui::EndTable();
	}

	ImGui::End();
}

bool DayZ::RadarAdapter::isEntityCached(uint32_t entityId) {
	auto now = std::chrono::steady_clock::now();
	auto it = entityCache.find(entityId);
	if (it != entityCache.end()) {
		if (now - it->second < CACHE_DURATION) {
			return true;
		}
		entityCache.erase(it);
	}
	return false;
}

void DayZ::RadarAdapter::updateEntityCache(uint32_t entityId) {
	entityCache[entityId] = std::chrono::steady_clock::now();
}

void DayZ::RadarAdapter::clearExpiredCache() {
	auto now = std::chrono::steady_clock::now();
	for (auto it = entityCache.begin(); it != entityCache.end();) {
		if (now - it->second >= CACHE_DURATION) {
			it = entityCache.erase(it);
		} else {
			++it;
		}
	}
}

void DayZ::RadarAdapter::batchProcessEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities) {
	batchEntities.clear();
	batchEntities.reserve(entities.size());
	
	for (const auto& entity : entities) {
		if (entity && entity->isValid()) {
			batchEntities.push_back(entity);
		}
	}
}

bool DayZ::RadarAdapter::shouldRenderEntity(const DMARender::Vector3& position, const DMARender::Vector3& cameraPos, float maxDistance) {
	float distance = cameraPos.Dist(position);
	return distance >= MIN_RENDER_DISTANCE && distance <= maxDistance;
}

void DayZ::RadarAdapter::DrawOverlay(DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform) {
	auto camera = memUpdater->getCamera();
	if (!camera || !curMap) return;

	// Sync LootListManager changes to Entity system periodically
	static auto lastSync = std::chrono::steady_clock::now();
	auto now = std::chrono::steady_clock::now();
	if (std::chrono::duration_cast<std::chrono::seconds>(now - lastSync).count() >= 5) {
		auto lootManager = renderBridge->getLootListManager();
		if (lootManager) {
			// Update Entity system with current rare items from LootListManager
			DayZ::Entity::setRareItems(lootManager->getRareItems());
		}
		lastSync = now;
	}

	// Clear expired cache entries
	clearExpiredCache();

	// Create persistent sets for deduplication
	static std::set<uint32_t> processedLootEntities;
	static std::set<uint32_t> processedAliveEntities;
	processedLootEntities.clear();
	processedAliveEntities.clear();

	// Batch process entities for better performance
	batchProcessEntities(memUpdater->getSlowEntityTable()->resolvedEntities);
	drawLoot(camera.get(), curMap, mTransform, batchEntities);

	batchProcessEntities(memUpdater->getItemTable()->resolvedEntities);
	drawLoot(camera.get(), curMap, mTransform, batchEntities);

	batchProcessEntities(memUpdater->getNearEntityTable()->resolvedEntities);
	drawLoot(camera.get(), curMap, mTransform, batchEntities);
	drawAliveEntities(camera.get(), curMap, mTransform, batchEntities, memUpdater->getScoreboard().get());

	batchProcessEntities(memUpdater->getFarEntityTable()->resolvedEntities);
	drawLoot(camera.get(), curMap, mTransform, batchEntities);
	drawAliveEntities(camera.get(), curMap, mTransform, batchEntities, memUpdater->getScoreboard().get());



	// Draw player list if enabled
	if (renderBridge->shouldShowPlayerList()) {
		drawPlayerList(camera.get(), memUpdater->getScoreboard().get());
	}

	// Draw server player list if enabled
	if (renderBridge->shouldShowServerPlayerList()) {
		drawServerPlayerList(memUpdater->getScoreboard());
	}
}

void DayZ::RadarAdapter::createFonts()
{
	// QUALITY-ENHANCED FONT SETTINGS: Clean with improved quality
	int dynamicOversampleH = 2;   // Quality mode - minimal oversampling for smoothness
	int dynamicOversampleV = 1;   // Quality mode - minimal vertical oversampling
	float dynamicRasterizerMultiply = 1.1f; // Quality mode - slight brightness boost for clarity
	bool dynamicPixelSnap = true; // Quality mode - pixel perfect alignment
	int dynamicAtlasSize = 2048; // Quality mode - higher resolution atlas for better detail

	// Get font quality settings from bridge if available - with safety checks
	if (renderBridge) {
		try {
			int bridgeOversampleH = renderBridge->getFontOversampleH();
			int bridgeOversampleV = renderBridge->getFontOversampleV();
			float bridgeRasterizerMultiply = renderBridge->getFontRasterizerMultiply();
			bool bridgePixelSnap = renderBridge->getFontPixelSnap();
			int bridgeAtlasSize = renderBridge->getFontAtlasSize();
			
			// Safety validation - only use values if they're in safe ranges
			if (bridgeOversampleH >= 1 && bridgeOversampleH <= 32) {
				dynamicOversampleH = bridgeOversampleH;
			}
			if (bridgeOversampleV >= 1 && bridgeOversampleV <= 16) {
				dynamicOversampleV = bridgeOversampleV;
			}
			if (bridgeRasterizerMultiply >= 0.5f && bridgeRasterizerMultiply <= 2.0f) {
				dynamicRasterizerMultiply = bridgeRasterizerMultiply;
			}
			
			// Apply pixel snap and atlas size settings
			dynamicPixelSnap = bridgePixelSnap;
			if (bridgeAtlasSize == 1024 || bridgeAtlasSize == 2048 || bridgeAtlasSize == 4096) {
				dynamicAtlasSize = bridgeAtlasSize;
			}
		}
		catch (...) {
			// Use defaults if bridge access fails
		}
	}

	// QUALITY-ENHANCED FONT CONFIGURATION: Clean with improved visual quality
		ImFontConfig config;
	config.OversampleH = dynamicOversampleH;     // 2 - controlled oversampling for smooth edges
	config.OversampleV = dynamicOversampleV;     // 1 - minimal vertical oversampling
	config.GlyphExtraSpacing.x = 0.0f;          // Natural spacing for better readability
	config.GlyphOffset.y = 0.0f;                // Perfect alignment
	config.PixelSnapH = dynamicPixelSnap;       // Pixel perfect alignment
	config.RasterizerMultiply = dynamicRasterizerMultiply; // Enhanced brightness for clarity
	config.FontDataOwnedByAtlas = false; // Better memory management for quality
	
	// Add Cyrillic support to fix ?????? issue with Russian/Cyrillic text
	config.GlyphRanges = ImGui::GetIO().Fonts->GetGlyphRangesCyrillic();
	
	// QUALITY-ENHANCED FONT ATLAS BUILD: Higher quality with clean rendering
	ImGuiIO& io = ImGui::GetIO();
	io.Fonts->TexDesiredWidth = dynamicAtlasSize;  // Higher resolution atlas for better detail
	io.Fonts->TexGlyphPadding = 1;                 // Minimal padding for quality without artifacts
	io.Fonts->Flags |= ImFontAtlasFlags_NoPowerOfTwoHeight; // Allow non-power-of-2 for better packing
	io.Fonts->Flags |= ImFontAtlasFlags_NoMouseCursors; // Optimize atlas for text only
	
	// DYNAMIC FONT SELECTION for Radar: Use same font as ESP for consistency
	std::string selectedFontFile = "arial.ttf"; // Default fallback
	std::string selectedFontName = "Arial";
	
	if (renderBridge) {
		try {
			selectedFontFile = renderBridge->getSelectedFontFile();
			selectedFontName = renderBridge->getSelectedFontName();
			std::cout << "[RADAR FONT] Using selected font: " << selectedFontName << " (" << selectedFontFile << ")" << std::endl;
		}
		catch (...) {
			std::cout << "[RADAR FONT] Failed to get selected font, using Arial fallback" << std::endl;
		}
	}
	
	// Build radar font paths with fallbacks
	std::vector<std::string> radarFontPaths;
	
	// Try to get the full path to the selected font first
	if (renderBridge) {
		try {
			std::string selectedFontPath = renderBridge->getSelectedFontFullPath();
			radarFontPaths.push_back(selectedFontPath);
			std::cout << "[RADAR FONT] Using selected font path: " << selectedFontPath << std::endl;
		}
		catch (...) {
			// Fallback to Windows Fonts directory if the new method fails
			radarFontPaths.push_back("C:\\Windows\\Fonts\\" + selectedFontFile);
			std::cout << "[RADAR FONT] Failed to get full path, using fallback: C:\\Windows\\Fonts\\" << selectedFontFile << std::endl;
		}
	} else {
		radarFontPaths.push_back("C:\\Windows\\Fonts\\" + selectedFontFile);
	}
	
	// Add essential fallback fonts
	radarFontPaths.insert(radarFontPaths.end(), {
		"C:\\Windows\\Fonts\\arial.ttf",
		"C:\\Windows\\Fonts\\calibri.ttf", 
		"C:\\Windows\\Fonts\\tahoma.ttf"
	});
	
	// Try to load radar font with fallbacks and crash protection
	radarFont = nullptr;
	for (const auto& fontPath : radarFontPaths) {
		try {
			std::cout << "[RADAR FONT] Attempting to load radar font: " << fontPath << std::endl;
			
			// Validate the font file first if we have bridge access
			bool fontIsValid = true;
			if (renderBridge) {
				try {
					fontIsValid = renderBridge->isValidFontFile(fontPath);
					if (!fontIsValid) {
						std::cout << "[RADAR FONT] Font validation failed, skipping: " << fontPath << std::endl;
						continue;
					}
				}
				catch (...) {
					std::cout << "[RADAR FONT] Font validation threw exception, skipping: " << fontPath << std::endl;
					continue;
				}
			}
			
			ImFont* tempFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 14, &config);
			if (tempFont) {
				radarFont = tempFont;
				std::cout << "[RADAR FONT] Successfully loaded radar font: " << fontPath << std::endl;
				break;
			} else {
				std::cout << "[RADAR FONT] Font loading returned null: " << fontPath << std::endl;
			}
		}
		catch (const std::exception& e) {
			std::cout << "[RADAR FONT] Exception loading font " << fontPath << ": " << e.what() << std::endl;
			continue;
		}
		catch (...) {
			std::cout << "[RADAR FONT] Unknown exception loading font: " << fontPath << std::endl;
			continue;
		}
	}
	
	// Fallback to default if all font loading failed
	if (!radarFont) {
		radarFont = io.Fonts->AddFontDefault();
		std::cout << "[RADAR FONT] All font loading failed, using default radar font" << std::endl;
	}

	// add small font with quality-enhanced settings
	ImFontConfig configSmall;
	configSmall.OversampleH = dynamicOversampleH;     // 2 - controlled oversampling for smooth edges
	configSmall.OversampleV = dynamicOversampleV;     // 1 - minimal vertical oversampling
	configSmall.GlyphExtraSpacing.x = 0.0f;          // Natural spacing for better readability
	configSmall.GlyphOffset.y = 0.0f;                // Perfect alignment
	configSmall.PixelSnapH = dynamicPixelSnap;       // Pixel perfect alignment
	configSmall.RasterizerMultiply = dynamicRasterizerMultiply; // Enhanced brightness for clarity
	configSmall.FontDataOwnedByAtlas = false; // Better memory management for quality
	configSmall.SizePixels = 16.0f; // Increased from 14 to 16 for better quality
	
	// Add Cyrillic support to fix ?????? issue with Russian/Cyrillic text
	configSmall.GlyphRanges = ImGui::GetIO().Fonts->GetGlyphRangesCyrillic();
	
	// Try to load table font with same fallback system and crash protection
	tableFont = nullptr;
	for (const auto& fontPath : radarFontPaths) {
		try {
			std::cout << "[RADAR FONT] Attempting to load table font: " << fontPath << std::endl;
			
			// Validate the font file first if we have bridge access
			bool fontIsValid = true;
			if (renderBridge) {
				try {
					fontIsValid = renderBridge->isValidFontFile(fontPath);
					if (!fontIsValid) {
						std::cout << "[RADAR FONT] Table font validation failed, skipping: " << fontPath << std::endl;
						continue;
					}
				}
				catch (...) {
					std::cout << "[RADAR FONT] Table font validation threw exception, skipping: " << fontPath << std::endl;
					continue;
				}
			}
			
			ImFont* tempFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 16, &configSmall);
			if (tempFont) {
				tableFont = tempFont;
				std::cout << "[RADAR FONT] Successfully loaded table font: " << fontPath << std::endl;
				break;
			} else {
				std::cout << "[RADAR FONT] Table font loading returned null: " << fontPath << std::endl;
			}
		}
		catch (const std::exception& e) {
			std::cout << "[RADAR FONT] Exception loading table font " << fontPath << ": " << e.what() << std::endl;
			continue;
		}
		catch (...) {
			std::cout << "[RADAR FONT] Unknown exception loading table font: " << fontPath << std::endl;
			continue;
		}
	}
	
	// Fallback to default if all font loading failed
	if (!tableFont) {
		tableFont = io.Fonts->AddFontDefault();
		std::cout << "[RADAR FONT] All font loading failed, using default table font" << std::endl;
	}
}

void DayZ::RadarAdapter::loadFavoriteSteamIDs(const std::string& filePath) {
	std::ifstream file(filePath);
	if (!file.is_open()) {
		std::cerr << "Error: SteamID file could not be opened: " << filePath << std::endl;
		return;
	}

	std::string line;
	while (std::getline(file, line)) {
		if (!line.empty()) {
			favoriteSteamIDs.insert(line); // Add "Admin"SteamIDs
		}
	}

	file.close();
}

void DayZ::RadarAdapter::saveFavoriteSteamIDs(const std::string& filePath) {
	std::ofstream file(filePath);
	if (!file.is_open()) {
		std::cerr << "Error: Could not save SteamID file: " << filePath << std::endl;
		return;
	}

	for (const auto& steamID : favoriteSteamIDs) {
		file << steamID << std::endl;
	}

	file.close();
	fflush(stdout);
}

void DayZ::RadarAdapter::addFavoriteSteamID(const std::string& steamID) {
	if (favoriteSteamIDs.insert(steamID).second) {
		saveFavoriteSteamIDs("steamids.txt");
		fflush(stdout);
	} else {
		fflush(stdout);
	}
}

void DayZ::RadarAdapter::removeFavoriteSteamID(const std::string& steamID) {
	if (favoriteSteamIDs.erase(steamID) > 0) {
		saveFavoriteSteamIDs("steamids.txt");
		fflush(stdout);
	} else {
		fflush(stdout);
	}
}

bool DayZ::RadarAdapter::isFavoriteSteamID(const std::string& steamID) const {
	return favoriteSteamIDs.count(steamID) > 0;
}


