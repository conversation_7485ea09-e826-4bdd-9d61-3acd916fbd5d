#pragma once
#include "DMARender/includes.h"
#include "../MemoryUpdater/MemoryUpdater.h"
#include "../../Structs/ClientPlayer.h"

namespace BF2042 {
    class RadarAdapter : public DMARender::IRadar {
        std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater;
        std::shared_ptr<DMARender::RenderBridge> bridge;

        // Radar settings
        float radarRange = 500.0f;
        bool showPlayers = true;
        bool showVehicles = true;
        bool showLocalPlayer = true;

        // Helper methods
        DMARender::Vector2 worldToRadar(const DMARender::Vector3& worldPos, const DMARender::Vector3& localPos, float mapScale) const;
        ImU32 getPlayerColor(const std::shared_ptr<ClientPlayer>& player, uint32_t localTeamId) const;

    public:
        RadarAdapter(std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater, std::shared_ptr<DMARender::RenderBridge> bridge);
        void DrawOverlay(DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform) override;
        void DrawRadar();  // Internal method
        
        // Settings
        void setRadarRange(float range) { radarRange = range; }
        void setShowPlayers(bool show) { showPlayers = show; }
        void setShowVehicles(bool show) { showVehicles = show; }
        void setShowLocalPlayer(bool show) { showLocalPlayer = show; }
    };
}
