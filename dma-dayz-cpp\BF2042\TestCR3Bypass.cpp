#include "BF2042Mem/BF2042Mem.h"
#include "BF2042Mem/MemoryUpdater/MemoryUpdater.h"
#include <iostream>
#include <chrono>
#include <thread>

// DebugLog is already defined in the main project

int main() {
    std::cout << "=== BF2042 CR3 Bypass Test ===" << std::endl;
    std::cout << "This test demonstrates manual CR3/PML4 bypass against EA's anti-cheat" << std::endl;
    std::cout << "========================================\n" << std::endl;

    try {
        // Initialize DMA system first
        std::cout << "[TEST] Initializing DMA system..." << std::endl;
        auto vmmManager = std::make_unique<DMAMem::VmmManager>();

        if (!vmmManager->getVmm()) {
            std::cerr << "[TEST] Failed to initialize DMA system" << std::endl;
            return -1;
        }

        // Initialize BF2042 memory system
        std::cout << "[TEST] Initializing BF2042 memory system..." << std::endl;
        auto bf2042Mem = std::make_unique<BF2042::Mem>(vmmManager.get());

        std::cout << "[TEST] BF2042 memory system initialized successfully" << std::endl;
        std::cout << "[TEST] Target PID: " << bf2042Mem->getPid() << std::endl;

        // Create memory updater
        auto memoryUpdater = std::make_unique<BF2042::MemoryUpdater>(bf2042Mem.get());
        
        std::cout << "\n[TEST] === Standard Memory Access Test ===" << std::endl;
        std::cout << "[TEST] Testing normal memory access (likely to fail due to EA anti-cheat)..." << std::endl;
        
        // Try standard memory access first
        memoryUpdater->beginUpdateLoop();
        std::this_thread::sleep_for(std::chrono::seconds(2));

        auto players = memoryUpdater->getAllPlayers();
        std::cout << "[TEST] Standard access found " << players.size() << " players" << std::endl;

        if (players.empty()) {
            std::cout << "[TEST] ❌ Standard memory access failed (expected due to EA anti-cheat)" << std::endl;
        } else {
            std::cout << "[TEST] ✅ Standard memory access working" << std::endl;
        }

        memoryUpdater->endUpdateLoop();

        std::cout << "\n[TEST] === CR3 Bypass Test ===" << std::endl;
        std::cout << "[TEST] Initializing CR3 bypass to defeat EA's protection..." << std::endl;
        
        // Initialize CR3 bypass
        if (!memoryUpdater->initializeCR3Bypass()) {
            std::cerr << "[TEST] ❌ Failed to initialize CR3 bypass" << std::endl;
            return -1;
        }
        
        std::cout << "[TEST] ✅ CR3 bypass initialized successfully" << std::endl;
        
        // Enable CR3 bypass
        memoryUpdater->enableCR3Bypass(true);
        std::cout << "[TEST] CR3 bypass enabled: " << (memoryUpdater->isCR3BypassEnabled() ? "YES" : "NO") << std::endl;
        
        // Run comprehensive CR3 bypass test
        std::cout << "\n[TEST] Running comprehensive CR3 bypass test..." << std::endl;
        memoryUpdater->testCR3Bypass();
        
        // Print detailed statistics
        std::cout << "\n[TEST] === CR3 Bypass Statistics ===" << std::endl;
        memoryUpdater->printCR3Statistics();
        
        std::cout << "\n[TEST] === Performance Comparison ===" << std::endl;
        
        // Test performance with CR3 bypass
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // Simulate multiple memory reads
        for (int i = 0; i < 10; i++) {
            // This would use CR3 bypass internally if enabled
            auto testPlayers = memoryUpdater->getAllPlayers();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "[TEST] CR3 bypass performance: " << duration.count() << "ms for 10 reads" << std::endl;
        
        std::cout << "\n[TEST] === Final Results ===" << std::endl;
        
        if (memoryUpdater->isCR3BypassEnabled()) {
            std::cout << "[TEST] ✅ CR3 bypass is active and operational" << std::endl;
            std::cout << "[TEST] ✅ Manual page table translation working" << std::endl;
            std::cout << "[TEST] ✅ EA's CR3/PML4 manipulation bypassed" << std::endl;
            std::cout << "[TEST] 🎯 BF2042 ESP should now work against anti-cheat!" << std::endl;
        } else {
            std::cout << "[TEST] ❌ CR3 bypass failed to activate" << std::endl;
            std::cout << "[TEST] ❌ EA's anti-cheat is still blocking access" << std::endl;
            std::cout << "[TEST] 💡 May need more advanced techniques" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[TEST] Exception: " << e.what() << std::endl;
        return -1;
    }

    std::cout << "\n[TEST] === Test Complete ===" << std::endl;
    std::cout << "[TEST] Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
