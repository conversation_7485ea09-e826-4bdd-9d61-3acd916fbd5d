#include "MultiCoreConfig.h"
#include <iostream>
#include <sstream>
#include <fstream>

namespace BF6 {
    
    // Define timing profiles for different system configurations
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MANY_CORE_PROFILE = {
        16,   // playerWorkerMs - Ultra-fast for 16+ cores (60 FPS player updates)
        8,    // cameraUpdateMs - Ultra-smooth camera (120 FPS)
        8     // renderUpdateMs - Ultra-smooth rendering (120 FPS)
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MEDIUM_CORE_PROFILE = {
        33,   // playerWorkerMs - Fast for 8-15 cores (30 FPS player updates)
        16,   // cameraUpdateMs - Smooth camera (60 FPS)
        16    // renderUpdateMs - Smooth rendering (60 FPS)
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::STANDARD_PROFILE = {
        50,   // playerWorkerMs - Standard for 4-7 cores (20 FPS player updates)
        16,   // cameraUpdateMs - Standard camera (60 FPS)
        16    // renderUpdateMs - Standard rendering (60 FPS)
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MINIMAL_PROFILE = {
        100,  // playerWorkerMs - Conservative for <4 cores (10 FPS player updates)
        33,   // cameraUpdateMs - Conservative camera (30 FPS)
        33    // renderUpdateMs - Conservative rendering (30 FPS)
    };
    
    // Global configuration instance
    MultiCoreConfig g_multiCoreConfig;
    
    MultiCoreConfig::MultiCoreConfig() {
        hardwareCores = std::thread::hardware_concurrency();
        detectOptimalSettings();
    }
    
    MultiCoreConfig::TimingProfile MultiCoreConfig::getCurrentProfile() const {
        return currentProfile;
    }
    
    int MultiCoreConfig::getOptimalEntityThreadCount() const {
        if (!shouldUseParallelProcessing()) {
            return 1;
        }
        
        // For BF6, we use fewer threads since we only have players (not complex entity tables)
        int optimalThreads = std::min(hardwareCores / 2, 4); // Max 4 threads for player processing
        return std::max(1, optimalThreads);
    }
    
    bool MultiCoreConfig::shouldUseParallelProcessing() const {
        return enableParallelProcessing.load() && hardwareCores >= MIN_CORES_FOR_PARALLEL;
    }
    
    bool MultiCoreConfig::shouldUseThreadAffinity() const {
        return enableThreadAffinity.load() && hardwareCores >= MIN_CORES_FOR_AFFINITY;
    }
    
    void MultiCoreConfig::loadFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cout << "[BF6] MultiCore config file not found, using defaults" << std::endl;
            return;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            if (line.find("enableParallelProcessing=") == 0) {
                enableParallelProcessing = (line.substr(25) == "true");
            } else if (line.find("enableThreadAffinity=") == 0) {
                enableThreadAffinity = (line.substr(21) == "true");
            } else if (line.find("maxWorkerThreads=") == 0) {
                maxWorkerThreads = std::stoi(line.substr(17));
            } else if (line.find("selectedProfile=") == 0) {
                selectedProfile = std::stoi(line.substr(16));
            }
        }
        
        detectOptimalSettings();
    }
    
    void MultiCoreConfig::saveToFile(const std::string& filename) const {
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "[BF6] Failed to save MultiCore config" << std::endl;
            return;
        }
        
        file << "enableParallelProcessing=" << (enableParallelProcessing.load() ? "true" : "false") << std::endl;
        file << "enableThreadAffinity=" << (enableThreadAffinity.load() ? "true" : "false") << std::endl;
        file << "maxWorkerThreads=" << maxWorkerThreads.load() << std::endl;
        file << "selectedProfile=" << selectedProfile.load() << std::endl;
    }
    
    std::string MultiCoreConfig::getSystemInfo() const {
        std::stringstream ss;
        ss << "Hardware Cores: " << hardwareCores << std::endl;
        ss << "Parallel Processing: " << (shouldUseParallelProcessing() ? "Enabled" : "Disabled") << std::endl;
        ss << "Thread Affinity: " << (shouldUseThreadAffinity() ? "Enabled" : "Disabled") << std::endl;
        ss << "Optimal Entity Threads: " << getOptimalEntityThreadCount() << std::endl;
        ss << "Current Profile: ";
        
        switch (getCurrentProfileType()) {
            case MANY_CORE: ss << "Many-Core (16+ cores)"; break;
            case MEDIUM_CORE: ss << "Medium-Core (8-15 cores)"; break;
            case STANDARD: ss << "Standard (4-7 cores)"; break;
            case MINIMAL: ss << "Minimal (<4 cores)"; break;
            default: ss << "Auto-Detect"; break;
        }
        
        return ss.str();
    }
    
    void MultiCoreConfig::runPerformanceTest() {
        std::cout << "[BF6] Running performance test..." << std::endl;
        // Simplified performance test for BF6
        std::cout << "[BF6] Performance test completed" << std::endl;
    }
    
    void MultiCoreConfig::setProfile(ProfileType profile) {
        selectedProfile = profile;
        detectOptimalSettings();
    }
    
    MultiCoreConfig::ProfileType MultiCoreConfig::getCurrentProfileType() const {
        return static_cast<ProfileType>(selectedProfile.load());
    }
    
    void MultiCoreConfig::detectOptimalSettings() {
        ProfileType profileType = getCurrentProfileType();
        
        if (profileType == AUTO_DETECT) {
            currentProfile = selectProfileForCoreCount(hardwareCores);
        } else {
            switch (profileType) {
                case MANY_CORE: currentProfile = MANY_CORE_PROFILE; break;
                case MEDIUM_CORE: currentProfile = MEDIUM_CORE_PROFILE; break;
                case STANDARD: currentProfile = STANDARD_PROFILE; break;
                case MINIMAL: currentProfile = MINIMAL_PROFILE; break;
                default: currentProfile = selectProfileForCoreCount(hardwareCores); break;
            }
        }
        
        std::cout << "[BF6] MultiCore config detected: " << hardwareCores << " cores" << std::endl;
        std::cout << "[BF6] Player update: " << currentProfile.playerWorkerMs << "ms" << std::endl;
        std::cout << "[BF6] Camera update: " << currentProfile.cameraUpdateMs << "ms" << std::endl;
        std::cout << "[BF6] Render update: " << currentProfile.renderUpdateMs << "ms" << std::endl;
    }
    
    MultiCoreConfig::TimingProfile MultiCoreConfig::selectProfileForCoreCount(int cores) const {
        if (cores >= 16) {
            return MANY_CORE_PROFILE;
        } else if (cores >= 8) {
            return MEDIUM_CORE_PROFILE;
        } else if (cores >= 4) {
            return STANDARD_PROFILE;
        } else {
            return MINIMAL_PROFILE;
        }
    }
}
