#pragma once
#include "DMARender/includes.h"
#include "../MemoryUpdater/MemoryUpdater.h"
#include "../../Structs/ClientPlayer.h"
#include "../../Structs/ClientSoldierEntity.h"
#include "../../Structs/HealthComponent.h"
#include "../../Structs/Camera.h"
#include <fstream>
#include <set>
#include <unordered_map>
#include <chrono>
#include <map>

namespace BF2042 {
    // Player cache entry structure
    struct PlayerCacheEntry {
        std::chrono::steady_clock::time_point timestamp;
        std::string playerName;
        uint32_t teamId;
        bool isValid;
    };

    // Position cache entry (for temporary position caching during frame processing)
    struct PositionCacheEntry {
        DMARender::Vector3 position;
        DMARender::Vector2 screenPos;
        float distance;
        bool isVisible;
        std::chrono::steady_clock::time_point timestamp;
    };

    class OverlayAdapter : public DMARender::IOverlay {
        std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater;
        std::shared_ptr<DMARender::RenderBridge> bridge;

        // Performance caching system
        std::unordered_map<uint32_t, PlayerCacheEntry> playerCache;
        std::unordered_map<uint32_t, PositionCacheEntry> positionCache;
        const std::chrono::milliseconds PLAYER_CACHE_DURATION{30000}; // Cache player data for 30 seconds
        const std::chrono::milliseconds POSITION_CACHE_DURATION{16}; // Cache positions for 1 frame (16ms at 60fps)

        // Performance tracking
        std::chrono::steady_clock::time_point lastFrameTime;
        float frameTime = 0.0f;
        uint32_t frameCount = 0;
        float avgFrameTime = 0.0f;

        // ESP settings
        bool showPlayerESP = true;
        bool showHealthBars = true;
        bool showPlayerNames = true;
        bool showPlayerDistance = true;
        bool showTeamInfo = true;
        bool showVehicleESP = true;
        float maxPlayerDistance = 500.0f;
        float maxVehicleDistance = 1000.0f;

        // Team colors
        ImU32 friendlyTeamColor = IM_COL32(0, 255, 0, 255);   // Green
        ImU32 enemyTeamColor = IM_COL32(255, 0, 0, 255);      // Red
        ImU32 neutralTeamColor = IM_COL32(255, 255, 0, 255);  // Yellow
        ImU32 localPlayerColor = IM_COL32(0, 255, 255, 255);  // Cyan

        // Helper methods
        void clearExpiredCache();
        bool isPlayerCached(uint32_t playerId) const;
        PlayerCacheEntry getPlayerFromCache(uint32_t playerId) const;
        void cachePlayer(uint32_t playerId, const std::shared_ptr<ClientPlayer>& player);
        
        void renderPlayerESP(const std::shared_ptr<BF2042::ClientPlayer>& player, const std::shared_ptr<BF2042::Camera>& camera);
        void renderHealthBar(const DMARender::Vector2& screenPos, float healthPercentage, float maxHealth);
        void renderPlayerInfo(const DMARender::Vector2& screenPos, const std::shared_ptr<BF2042::ClientPlayer>& player, float distance);
        void renderBoundingBox(const DMARender::Vector2& screenPos, const std::shared_ptr<BF2042::ClientPlayer>& player);
        void renderDayZStyleBoundingBox(const DMARender::Vector2& top, const DMARender::Vector2& bottom, float width, ImU32 color);

        // Basic rendering methods (same as DayZ)
        void drawFPSCounter();
        void drawDebugInfo();

        ImU32 getTeamColor(uint32_t teamId, uint32_t localTeamId, bool isLocalPlayer) const;
        std::string formatDistance(float distance) const;
        bool isPlayerVisible(const std::shared_ptr<BF2042::ClientPlayer>& player, const std::shared_ptr<BF2042::Camera>& camera) const;

    public:
        OverlayAdapter(std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater, std::shared_ptr<DMARender::RenderBridge> bridge);
        void DrawOverlay() override;
        void createFonts() override;
        
        // Settings methods
        void setShowPlayerESP(bool show) { showPlayerESP = show; }
        void setShowHealthBars(bool show) { showHealthBars = show; }
        void setShowPlayerNames(bool show) { showPlayerNames = show; }
        void setShowPlayerDistance(bool show) { showPlayerDistance = show; }
        void setShowTeamInfo(bool show) { showTeamInfo = show; }
        void setMaxPlayerDistance(float distance) { maxPlayerDistance = distance; }
        
        // Team color settings
        void setFriendlyTeamColor(ImU32 color) { friendlyTeamColor = color; }
        void setEnemyTeamColor(ImU32 color) { enemyTeamColor = color; }
        void setNeutralTeamColor(ImU32 color) { neutralTeamColor = color; }
        void setLocalPlayerColor(ImU32 color) { localPlayerColor = color; }
    };
}
