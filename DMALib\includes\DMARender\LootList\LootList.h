#pragma once
#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <fstream>
#include <set>
#include <map>
#include <algorithm>
#include <imgui.h>
//#include <json/json.h>  // Disabled for now
#include "../Vectors/Vector3.h"

namespace DMARender {

    struct RareItemData {
        std::string itemName;     // Original TypeName (e.g., "CarBattery")
        std::string customName;   // User-friendly name (e.g., "Car Battery")
        ImU32 color;

        RareItemData() : itemName(""), customName(""), color(IM_COL32(255, 0, 255, 255)) {} // Default purple
        RareItemData(const std::string& name, ImU32 col = IM_COL32(255, 0, 255, 255), const std::string& custom = "")
            : itemName(name), customName(custom.empty() ? name : custom), color(col) {}
    };

    class LootListManager {
    private:
        std::map<std::string, RareItemData> rareItems; // Changed from set to map
        std::string saveFilePath;
        bool isDirty;

    public:
        LootListManager();
        ~LootListManager();

        // Rare item management
        void addRareItem(const std::string& itemName, ImU32 color = IM_COL32(255, 0, 255, 255), const std::string& customName = "");
        void removeRareItem(const std::string& itemName);
        void clearAll();
        bool isRareItem(const std::string& itemName) const;
        void setItemColor(const std::string& itemName, ImU32 color);
        ImU32 getItemColor(const std::string& itemName) const;
        void setItemCustomName(const std::string& itemName, const std::string& customName);
        std::string getItemCustomName(const std::string& itemName) const;
        std::string getDisplayName(const std::string& itemName) const; // Returns custom name if set, otherwise original name

        // Access
        std::set<std::string> getRareItems() const; // Returns just the names for Entity system compatibility
        const std::map<std::string, RareItemData>& getRareItemsWithColors() const { return rareItems; }
        size_t getItemCount() const { return rareItems.size(); }
        bool isEmpty() const { return rareItems.empty(); }

        // Search and filter
        std::vector<std::string> findItemsByPartialName(const std::string& partialName) const;

        // Persistence
        bool saveToFile();
        bool loadFromFile();
        void setSaveFilePath(const std::string& path) { saveFilePath = path; }

        // Utility
        std::vector<std::string> getSortedItems() const;

        // Synchronization with Entity system
        void syncWithEntitySystem();
        void syncFromEntitySystem();
    };

} // namespace DMARender
