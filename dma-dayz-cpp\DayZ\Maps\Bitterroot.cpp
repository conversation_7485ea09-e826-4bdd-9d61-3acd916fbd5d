#include "Bitterroot.h"

ImVec2 DayZ::Bitterroot::translatePoint(const DMARender::Vector3& gamePoint)
{
    // Bitterroot map - fine-tune scaling factor for position accuracy
    // Current: 2048.0f / 12800.0f = 0.16
    // Adjust these values to fix the slight right/down offset:

    // Try slightly different scaling ratios:
    // float mult = 2048.0f / 12800.0f;  // Current = 0.16
    //float mult = 2130.0f / 12000.0f;  // = 0.164 (try if still too small)
    // float mult = 2000.0f / 12800.0f;  // = 0.156 (try if too big)
    float mult = 2048.0f / 12283.0f;  // ≈ 0.1645

    return ImVec2(gamePoint.x * mult, (12283.0f - gamePoint.z) * mult);
}
