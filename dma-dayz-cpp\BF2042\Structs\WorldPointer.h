#pragma once
#include "DMAMemoryManagement/includes.h"
#include "GameContext.h"

namespace BF2042 {
    struct WorldPointer : public DMAMem::MemoryObject {
        std::shared_ptr<GameContext> GameContextPtr;

        WorldPointer() {
            GameContextPtr = std::shared_ptr<GameContext>(new GameContext());
            this->registerPointer(0x0, GameContextPtr.get());
        }
        
        // Helper methods
        bool isValid() const {
            if (!GameContextPtr) return false;
            return GameContextPtr->isValid();
        }
        
        std::vector<std::shared_ptr<ClientPlayer>> getAllPlayers() const {
            if (GameContextPtr) {
                return GameContextPtr->getAllPlayers();
            }
            return {};
        }
        
        std::shared_ptr<ClientPlayer> getLocalPlayer() const {
            if (GameContextPtr) {
                return GameContextPtr->getLocalPlayer();
            }
            return nullptr;
        }
    };
}
