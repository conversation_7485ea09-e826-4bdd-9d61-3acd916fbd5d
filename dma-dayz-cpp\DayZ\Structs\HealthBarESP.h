#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"
#include "Entity.h"
#include "Camera.h"
#include <memory>

namespace DayZ {

    // ===== HEALTH BAR ESP CLASS =====
    class HealthBarESP {
    public:
        // Main health bar rendering functions
        static void DrawPlayerHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                      float width = 40.0f, float height = 6.0f, float offsetY = -10.0f,
                                      float maxDistance = 100.0f,
                                      ImU32 highColor = IM_COL32(0, 255, 0, 255),
                                      ImU32 mediumColor = IM_COL32(255, 255, 0, 255),
                                      ImU32 lowColor = IM_COL32(255, 0, 0, 255),
                                      ImU32 backgroundColor = IM_COL32(32, 32, 32, 180),
                                      int healthType = 0, bool showBar = true, bool showNumbers = true, bool showAllStats = false);

        // Enhanced version with memory access for real health reading
        static void DrawPlayerHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                      DMAMem::VmmManager* vmm, DWORD pid,
                                      float width = 40.0f, float height = 6.0f, float offsetY = -10.0f,
                                      float maxDistance = 100.0f,
                                      ImU32 highColor = IM_COL32(0, 255, 0, 255),
                                      ImU32 mediumColor = IM_COL32(255, 255, 0, 255),
                                      ImU32 lowColor = IM_COL32(255, 0, 0, 255),
                                      ImU32 backgroundColor = IM_COL32(32, 32, 32, 180),
                                      int healthType = 0, bool showBar = true, bool showNumbers = true, bool showAllStats = false);

        static void DrawZombieHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                      float width = 40.0f, float height = 6.0f, float offsetY = -10.0f,
                                      float maxDistance = 100.0f,
                                      ImU32 highColor = IM_COL32(0, 255, 0, 255),
                                      ImU32 mediumColor = IM_COL32(255, 255, 0, 255),
                                      ImU32 lowColor = IM_COL32(255, 0, 0, 255),
                                      ImU32 backgroundColor = IM_COL32(32, 32, 32, 180),
                                      int healthType = 0, bool showBar = true, bool showNumbers = true, bool showAllStats = false);

        // Enhanced version with memory access for real health reading
        static void DrawZombieHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                      DMAMem::VmmManager* vmm, DWORD pid,
                                      float width = 40.0f, float height = 6.0f, float offsetY = -10.0f,
                                      float maxDistance = 100.0f,
                                      ImU32 highColor = IM_COL32(0, 255, 0, 255),
                                      ImU32 mediumColor = IM_COL32(255, 255, 0, 255),
                                      ImU32 lowColor = IM_COL32(255, 0, 0, 255),
                                      ImU32 backgroundColor = IM_COL32(32, 32, 32, 180),
                                      int healthType = 0, bool showBar = true, bool showNumbers = true, bool showAllStats = false);

        // Utility functions
        static float GetEntityHealth(std::shared_ptr<DayZ::Entity> entity);
        static float GetEntityHealth(std::shared_ptr<DayZ::Entity> entity, DMAMem::VmmManager* vmm, DWORD pid); // Enhanced version
        static float GetEntityHealthByType(std::shared_ptr<DayZ::Entity> entity, int healthType);
        static float GetEntityHealthByType(std::shared_ptr<DayZ::Entity> entity, int healthType, DMAMem::VmmManager* vmm, DWORD pid); // Enhanced version
        static ImU32 GetHealthBarColor(float healthPercent, ImU32 highColor, ImU32 mediumColor, ImU32 lowColor);
        static bool WorldToScreen(DayZ::Camera* camera, const DMARender::Vector3& worldPos, DMARender::Vector2* screenPos);

        // Text rendering functions
        static void DrawHealthText(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                 std::shared_ptr<DayZ::Entity> entity, int healthType, bool showAllStats,
                                 ImU32 textColor = IM_COL32(255, 255, 255, 255));
        static void DrawHealthText(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                 std::shared_ptr<DayZ::Entity> entity, int healthType, bool showAllStats,
                                 DMAMem::VmmManager* vmm, DWORD pid, // Enhanced version
                                 ImU32 textColor = IM_COL32(255, 255, 255, 255));
        static std::string GetHealthTypeString(int healthType);
        static std::string FormatHealthValue(float value, int healthType);

    public:
        // DayZ SDK-style health bar positioning (left side of bounding box)
        static void DrawHealthBarSDKStyle(DayZ::Camera* camera, float left, float top, float bottom,
                                        float healthPercent, ImU32 healthColor, ImU32 backgroundColor);

    private:
        // Helper functions
        static void DrawHealthBar(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                float healthPercent, float width, float height,
                                ImU32 healthColor, ImU32 backgroundColor);

        static DMARender::Vector3 GetEntityHeadPosition(std::shared_ptr<DayZ::Entity> entity);
        static bool IsValidHealthBarDistance(DayZ::Camera* camera, const DMARender::Vector3& entityPos, float maxDistance);
    };

} // namespace DayZ
