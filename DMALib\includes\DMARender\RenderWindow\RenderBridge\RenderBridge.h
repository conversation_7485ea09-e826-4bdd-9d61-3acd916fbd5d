#pragma once
#include <memory>
#include <set>
#include "../../IOverlay/IOverlay.h"
#include "../../IRadar/IRadar.h"
#include "MapManager/MapManager.h"
#include "../../LootList/LootList.h"

#include <d3d11.h>

// Forward declaration
namespace DMARender {
    class SettingsManager;
}

namespace DMARender {
	class RenderBridge {
		std::shared_ptr<IOverlay> overlayPtr = nullptr;
		std::shared_ptr<IRadar> radarPtr = nullptr;
		std::shared_ptr<MapManager> mapManager;
		std::shared_ptr<SettingsManager> settingsManager;
		std::shared_ptr<LootListManager> lootListManager;
		ID3D11Device** g_pd3dDevicePtr;

		// Admin list management
		std::set<std::string> adminSteamIDs;
		void loadAdminSteamIDs(const std::string& filePath);
		void saveAdminSteamIDs(const std::string& filePath);

		// fonts

		int FontRADAR = 13;
		int FontRADAR2 = 13;


		bool showDeadPlayers = true;
		bool showVehicles = true;
		bool showGrounditems = false;
		bool showBoats = true;
		bool showDeadAnimals = false;
		bool showClothing = false;
		bool showWeapons = true;
		bool showProxyMagazines = true;
		bool showBackpacks = true;
		bool showRare = true;
		bool showFood = true;
		bool showAmmo = true;
		bool showPlayerList = true;
		bool showServerPlayerList = false;
		bool showZombies = false;
		bool showRadar = true;  // BF6 radar display toggle
		bool showPerformanceInfo = true;  // BF6 performance info display
		bool showBoundingBoxes = true;  // BF6 bounding boxes display
		bool showAnimals = false;
		bool showBots = false;                  // Show bots on radar (default: false)
		std::string playername =  "Survivor";
		int BlipSize = 7;
		int BlipSize2 = 4;
		int Aimlinelength = 80;
		bool showOptics = true;
		bool showBase = true;
		bool showMelee = true;
		bool showContainer = true;      // Show storage containers on radar
		bool showCooking = true;        // Show cooking items on radar
		bool showCamping = true;        // Show camping items on radar
		bool showStash = true;          // Show stash containers on radar
		bool showCustomContainer = true; // Show custom/modded containers on radar
		int LootDistanceDeadzone = 0;
		bool showExplosives = true;
		int ZombiesBlipSize = 4;
		int AnimalsBlipSize = 5;
		bool showPlayerNameRadar = true;
		bool showPlayerDistanceRadar = true;
		bool showPlayerHandRadar = true;



		// fuser
		bool showDeadPlayersFUSER = true;
		bool showVehiclesFUSER = true;
		bool showGrounditemsFUSER = true;
		bool showBoatsFUSER = true;
		bool showDeadAnimalsFUSER = false;
		bool showClothingFUSER = false;
		bool showWeaponsFUSER = true;
		bool showProxyMagazinesFUSER = true;
		bool showBackpacksFUSER = true;
		bool showRareFUSER = true;
		bool showFoodFUSER = true;
		bool showAmmoFUSER = true;
		bool showZombiesFUSER = false;
		bool showAnimalsFUSER = false;
		int lootDistanceFUSER = 70;
		int ZombieDistanceFUSER = 100;
		bool showPlayerInfoesp = true;
		bool showPlayerNameFUSER = true;
		bool showPlayerDistanceFUSER = true;
		bool showPlayerHandFUSER = true;
		bool showPlayerInventoryFUSER = false; // Show player inventory contents
		bool showPlayerBoxFUSER = true;
		bool showBotsFUSER = false;             // Show bots in ESP (default: false)
		bool showAnimalBoxFUSER = true;        // Show animal bounding boxes
		bool showAnimalNameFUSER = true;       // Show animal names
		bool showZombieBoxFUSER = true;        // Show zombie bounding boxes
		bool showZombieNameFUSER = true;       // Show zombie names
		bool showZombieDistanceFUSER = true;   // Show zombie distance
		bool showOpticsFUSER = true;
		bool showDebugFUSER = false;

		// Entity distance settings
		int animalMaxDistance = 200;           // Max distance for animals
		int vehicleMaxDistance = 500;          // Max distance for vehicles/cars
		int boatMaxDistance = 500;             // Max distance for boats
		int deadPlayerMaxDistance = 100;       // Max distance for dead players
		int deadAnimalMaxDistance = 100;       // Max distance for dead animals
		int playerMaxDistance = 500;           // Max distance for players
		bool showBaseFUSER = true;
		bool showMeleeFUSER = true;
		bool showExplosivesFUSER = true;
		bool showContainerFUSER = true;      // Show storage containers
		bool showCookingFUSER = true;        // Show cooking items
		bool showCampingFUSER = true;        // Show camping items
		bool showStashFUSER = true;          // Show stash containers
		bool showCustomContainerFUSER = true; // Show custom/modded containers
		int LootDebugDistance = 70;
		bool enableESPDebugLogging = false;
		float espTextSize = 1.0f; // ESP text size multiplier (0.5 to 2.0)
		float baseFontSize = 28.0f; // Base font pixel size when loading fonts (12-48px)

		// Additional font size options for different ESP elements
		float playerNameFontSize = 1.0f;  // Player name text size multiplier
		float itemFontSize = 0.9f;        // Item text size multiplier
		float distanceFontSize = 0.8f;    // Distance text size multiplier

		// Font settings
		int selectedFontIndex = 1; // Default to Calibri (index 1)
		bool fontBold = false;
		bool fontOutline = true;
		bool fontShadow = false;
		float fontAlpha = 1.0f; // Font alpha transparency (0.0 = transparent, 1.0 = opaque)
		float fontShadowOffset = 1.0f; // Font shadow offset distance (0.0 = no offset, 5.0 = large offset)
		float fontOutlineSize = 1.5f; // Font outline thickness (0.0 = no outline, 5.0 = thick outline)
		float fontOutlineColor[4] = {0.0f, 0.0f, 0.0f, 1.0f}; // Font outline color (RGBA: default black)

		// Dynamic font detection system
		mutable std::vector<std::string> availableFontNames; // Display names (e.g., "Arial", "Calibri")
		mutable std::vector<std::string> availableFontFiles; // File names (e.g., "arial.ttf", "calibri.ttf")
		mutable bool fontListCached = false; // Whether font list has been scanned

		// Counter font sizes
		float fpsCounterFontSize = 20.0f; // FPS and player counter font size
		
		// FPS Counter position settings
		float fpsCounterHorizontalPadding = 485.0f; // Distance from right edge (increase to move left)
		float fpsCounterVerticalPadding = 27.0f;    // Distance from top edge (increase to move down)
		
		// Font quality settings for fuser optimization (optimized for small fonts)
		int fontOversampleH = 3; // Horizontal oversampling (reduced for small font clarity)
		int fontOversampleV = 2;  // Vertical oversampling (reduced for small font clarity)
		float fontRasterizerMultiply = 1.1f; // Font brightness multiplier (slightly brighter for small fonts)
		bool fontPixelSnap = true; // Enable pixel snapping for crisp text (especially small fonts)
		int fontAtlasSize = 1024; // Font atlas texture size (1024, 2048, 4096) - Conservative default
		float fontUIBias = 0.0f; // UI bias for font rendering (-1.0 to 1.0) - Adjusts font positioning

		// Bounding Box settings
		float boundingBoxThickness = 1.5f;
		bool boundingBoxAntiAliasing = true;
		bool boundingBoxRounded = false;
		float boundingBoxRounding = 2.0f;
		int boundingBoxType = 0;  // 0: Normal, 1: Slim, 2: Corner, 3: 3D
		float boundingBoxMaxDistance = 500.0f; // Maximum distance for bounding box rendering

		// Keybind settings
		int itemESPToggleKey = 0x04; // Middle Mouse (scroll wheel click) by default
		bool itemESPEnabled = true;  // Item ESP state

		// Container Contents settings
		bool showContainerContents = true;  // Enable/disable container contents display
		int containerContentsMaxItems = 15;  // Maximum items to show per container
		float containerContentsMaxDistance = 100.0f; // Maximum distance to show container contents

		// Skeleton ESP settings (DayZ2-style)
		bool playerSkeletonEnabled = false;
		bool zombieSkeletonEnabled = false;
		float skeletonLineThickness = 0.5f; // DayZ2 default thickness
	float playerSkeletonMaxDistance = 1000.0f; // Default 1000m for players
	float zombieSkeletonMaxDistance = 300.0f; // Default 300m for zombies
	int skeletonDetailLevel = 2; // Default: Medium detail (0=Minimal, 1=Reduced, 2=Medium, 3=Full)
		bool headCircleEnabled = true; // Enable head circle drawing (DayZ mod.txt style)
		float headCircleSize = 0.3f; // Head circle size multiplier (0.05f to 2.0f)

		// Health Bar ESP settings
		bool playerHealthBarEnabled = false;
		bool zombieHealthBarEnabled = false;
		float healthBarWidth = 40.0f;       // Width of health bar in pixels
		float healthBarHeight = 6.0f;       // Height of health bar in pixels
		float healthBarOffsetY = -10.0f;    // Vertical offset from entity position
		float healthBarMaxDistance = 100.0f; // Maximum distance for health bar rendering
		int healthBarType = 0;               // 0=Health, 1=Blood, 2=Shock
		bool showHealthNumbers = true;       // Show numerical health values
		bool showHealthBar = true;           // Show health bar visual
		bool showAllHealthStats = false;     // Show all health stats (Health, Blood, Shock)

		// Personal Health Display settings
		bool showPersonalHealth = true;      // Show your own health stats on screen
		int personalHealthPosition = 0;      // 0=Top Left, 1=Top Right, 2=Bottom Left, 3=Bottom Right
		float personalHealthScale = 1.0f;    // Text scale for personal health display
		bool personalHealthBackground = true; // Show background behind personal health text

		// Individual health indicator positioning (0-100% of screen)
		bool useIndividualPositioning = false; // Enable individual positioning mode
		float healthIndicatorX = 5.0f;        // Health indicator X position (0-100%)
		float healthIndicatorY = 5.0f;        // Health indicator Y position (0-100%)
		float bloodIndicatorX = 5.0f;         // Blood indicator X position (0-100%)
		float bloodIndicatorY = 8.0f;         // Blood indicator Y position (0-100%)
		float shockIndicatorX = 5.0f;         // Shock indicator X position (0-100%)
		float shockIndicatorY = 11.0f;        // Shock indicator Y position (0-100%)
		float statusIndicatorX = 5.0f;        // Status indicator X position (0-100%)
		float statusIndicatorY = 14.0f;       // Status indicator Y position (0-100%)

		// Individual health indicator toggles
		bool showHealthIndicator = true;       // Show health percentage indicator
		bool showBloodIndicator = true;        // Show blood level indicator
		bool showShockIndicator = true;        // Show shock level indicator
		bool showStatusIndicator = true;       // Show status text indicator

		// Health display formatting options
		bool showHealthLabels = true;          // Show "Health:", "Blood:", etc. labels
		bool useCustomFont = true;             // Use custom font system instead of default

		// Crosshair Settings
		bool crosshairEnabled = false;         // Enable/disable crosshair
		float crosshairSize = 10.0f;          // Size of crosshair lines
		float crosshairThickness = 2.0f;      // Thickness of crosshair lines
		float crosshairGap = 5.0f;            // Gap from center to start of lines
		float crosshairAlpha = 1.0f;          // Alpha transparency (0.0-1.0)
		bool crosshairOutline = true;         // Enable outline around crosshair
		float crosshairOutlineThickness = 1.0f; // Outline thickness
		bool crosshairDot = false;            // Enable center dot
		float crosshairDotSize = 2.0f;        // Size of center dot

		float crosshairVerticalOffset = 0.0f; // Vertical position offset (-100 to +100 pixels)
		float crosshairHorizontalOffset = 0.0f; // Horizontal position offset (-100 to +100 pixels)
		int crosshairColorR = 255;            // Red component (0-255)
		int crosshairColorG = 255;            // Green component (0-255)
		int crosshairColorB = 255;            // Blue component (0-255)
		int crosshairColorA = 255;            // Alpha component (0-255)
		int crosshairOutlineColorR = 0;       // Outline red component (0-255)
		int crosshairOutlineColorG = 0;       // Outline green component (0-255)
		int crosshairOutlineColorB = 0;       // Outline blue component (0-255)
		int crosshairOutlineColorA = 255;     // Outline alpha component (0-255)



	public:
		RenderBridge(ID3D11Device** g_pd3dDevice);
		void createFonts();
		void setOverlay(std::shared_ptr<IOverlay> ptr);
		std::shared_ptr<IOverlay> getOverlay();
		void setRadar(std::shared_ptr<IRadar> ptr);
		std::shared_ptr<IRadar> getRadar();
		void addMap(std::shared_ptr<IGameMap> map);
		std::shared_ptr<MapManager> getMapManager();
		std::shared_ptr<LootListManager> getLootListManager();
		std::shared_ptr<SettingsManager> getSettingsManager();

		// Settings management
		void saveSettings();
		void loadSettings();

		// Player exclusion management
		bool isPlayerExcluded(const std::string& steamID) const;
		void addExcludedPlayer(const std::string& steamID);
		void removeExcludedPlayer(const std::string& steamID);
		const std::vector<std::string>& getExcludedPlayers() const;

		// Admin list management
		bool isPlayerAdmin(const std::string& steamID) const;
		void addAdminPlayer(const std::string& steamID);
		void removeAdminPlayer(const std::string& steamID);

		//fonts

		int shouldRadarFont() const { return FontRADAR; }
		int shouldRadarFont2() const { return FontRADAR2; }

		// getter and setter methods for visibility settings
		bool shouldShowDeadPlayers() const { return showDeadPlayers; }
		bool shouldShowVehicles() const { return showVehicles; }
		bool shouldShowBoats() const { return showBoats; }
		bool shouldShowGrounditems() const { return showGrounditems; }
		bool shouldShowDeadAnimals() const { return showDeadAnimals; }
		bool shouldShowClothing() const { return showClothing; }
		bool shouldShowWeapons() const { return showWeapons; }
		bool shouldShowProxyMagazines() const { return showProxyMagazines; }
		bool shouldShowBackpacks() const { return showBackpacks; }
		bool shouldShowRare() const { return showRare; }
		bool shouldShowFood() const { return showFood; }
		bool shouldShowAmmo() const { return showAmmo; }
		bool shouldShowPlayerList() const { return showPlayerList; }
		bool shouldShowServerPlayerList() const { return showServerPlayerList; }
		bool shouldShowZombies() const { return showZombies; }
		bool shouldShowAnimals() const { return showAnimals; }
		bool shouldShowBots() const { return showBots; }
		std::string shouldPlayerName() const { return playername; }
		int shouldBlipSize() const { return BlipSize; }
		int shouldBlipSize2() const { return BlipSize2; }
		int shouldAimlinelength() const { return Aimlinelength; }
		bool shouldShowOptics() const { return showOptics; }
		bool shouldShowBase() const { return showBase; }
		bool shouldShowMelee() const { return showMelee; }
		bool shouldShowContainer() const { return showContainer; }
		bool shouldShowCooking() const { return showCooking; }
		bool shouldShowCamping() const { return showCamping; }
		bool shouldShowStash() const { return showStash; }
		bool shouldShowCustomContainer() const { return showCustomContainer; }
		int shouldLootDistanceDeadzone() const { return LootDistanceDeadzone; }
		bool shouldShowExplosives() const { return showExplosives; }
		int shouldZombiesBlipSize() const { return ZombiesBlipSize; }
		int shouldAnimalsBlipSize() const { return AnimalsBlipSize; }
		bool shouldShowPlayerNameRadar() const { return showPlayerNameRadar; }
		bool shouldShowPlayerDistanceRadar() const { return showPlayerDistanceRadar; }
		bool shouldShowPlayerHandRadar() const { return showPlayerHandRadar; }
		bool shouldShowRadar() const { return showRadar; }
		bool shouldShowPerformanceInfo() const { return showPerformanceInfo; }
		bool shouldShowBoundingBoxes() const { return showBoundingBoxes; }


		//fuser
		bool shouldShowDeadPlayersFUSER() const { return showDeadPlayersFUSER; }
		bool shouldShowVehiclesFUSER() const { return showVehiclesFUSER; }
		bool shouldShowBoatsFUSER() const { return showBoatsFUSER; }
		bool shouldShowGrounditemsFUSER() const { return showGrounditemsFUSER; }
		bool shouldShowDeadAnimalsFUSER() const { return showDeadAnimalsFUSER; }
		bool shouldShowClothingFUSER() const { return showClothingFUSER; }
		bool shouldShowWeaponsFUSER() const { return showWeaponsFUSER; }
		bool shouldShowProxyMagazinesFUSER() const { return showProxyMagazinesFUSER; }
		bool shouldShowBackpacksFUSER() const { return showBackpacksFUSER; }
		bool shouldShowRareFUSER() const { return showRareFUSER; }
		bool shouldShowFoodFUSER() const { return showFoodFUSER; }
		bool shouldShowAmmoFUSER() const { return showAmmoFUSER; }
		bool shouldShowZombiesFUSER() const { return showZombiesFUSER; }
		bool shouldShowAnimalsFUSER() const { return showAnimalsFUSER; }
		int shouldlootDistanceFUSER() const { return lootDistanceFUSER; }
		int shouldZombieDistanceFUSER() const { return ZombieDistanceFUSER; }
		bool shouldShowPlayerInfoesp() const { return showPlayerInfoesp; }
		bool shouldShowPlayerNameFUSER() const { return showPlayerNameFUSER; }
		bool shouldShowPlayerDistanceFUSER() const { return showPlayerDistanceFUSER; }
		bool shouldShowPlayerHandFUSER() const { return showPlayerHandFUSER; }
		bool shouldShowPlayerInventoryFUSER() const { return showPlayerInventoryFUSER; }
		bool shouldShowPlayerBoxFUSER() const { return showPlayerBoxFUSER; }
		bool shouldShowBotsFUSER() const { return showBotsFUSER; }
		bool shouldShowAnimalBoxFUSER() const { return showAnimalBoxFUSER; }
		bool shouldShowAnimalNameFUSER() const { return showAnimalNameFUSER; }
		bool shouldShowZombieBoxFUSER() const { return showZombieBoxFUSER; }
		bool shouldShowZombieNameFUSER() const { return showZombieNameFUSER; }
		bool shouldShowZombieDistanceFUSER() const { return showZombieDistanceFUSER; }
		bool shouldShowOpticsFUSER() const { return showOpticsFUSER; }
		bool shouldShowDebugFUSER() const { return showDebugFUSER; }

		// Entity distance getters
		int getAnimalMaxDistance() const { return animalMaxDistance; }
		int getVehicleMaxDistance() const { return vehicleMaxDistance; }
		int getBoatMaxDistance() const { return boatMaxDistance; }
		int getDeadPlayerMaxDistance() const { return deadPlayerMaxDistance; }
		int getDeadAnimalMaxDistance() const { return deadAnimalMaxDistance; }
		int getPlayerMaxDistance() const { return playerMaxDistance; }
		bool shouldShowBaseFUSER() const { return showBaseFUSER; }
		bool shouldShowMeleeFUSER() const { return showMeleeFUSER; }
		bool shouldShowExplosivesFUSER() const { return showExplosivesFUSER; }
		bool shouldShowContainerFUSER() const { return showContainerFUSER; }
		bool shouldShowCookingFUSER() const { return showCookingFUSER; }
		bool shouldShowCampingFUSER() const { return showCampingFUSER; }
		bool shouldShowStashFUSER() const { return showStashFUSER; }
		bool shouldShowCustomContainerFUSER() const { return showCustomContainerFUSER; }
		int shouldLootDebugDistance() const { return LootDebugDistance; }
		bool shouldEnableESPDebugLogging() const { return enableESPDebugLogging; }
		bool getEnableESPDebugLogging() const { return enableESPDebugLogging; }
		float getESPTextSize() const { return espTextSize; }
		float getBaseFontSize() const { return baseFontSize; }
		float getPlayerNameFontSize() const { return playerNameFontSize; }
		float getItemFontSize() const { return itemFontSize; }
		float getDistanceFontSize() const { return distanceFontSize; }

		// Font getters
		int getSelectedFontIndex() const { return selectedFontIndex; }
		bool getFontBold() const { return fontBold; }
		bool getFontOutline() const { return fontOutline; }
		bool getFontShadow() const { return fontShadow; }
		float getFontAlpha() const { return fontAlpha; }
		float getFontShadowOffset() const { return fontShadowOffset; }
		float getFontOutlineSize() const { return fontOutlineSize; }
		const float* getFontOutlineColor() const { return fontOutlineColor; }

		// Dynamic font system getters
		const std::vector<std::string>& getAvailableFontNames() const;
		const std::vector<std::string>& getAvailableFontFiles() const;
		std::string getSelectedFontName() const;
		std::string getSelectedFontFile() const;
		std::string getSelectedFontFullPath() const; // Get full path to selected font file
		void refreshFontList() const; // Rescan fonts
		void clearFontCache() const; // Clear cached font list to force refresh
		int findFontIndexByName(const std::string& fontName) const;
		bool isValidFontFile(const std::string& fontPath) const; // Validate font file safety

		// Font quality getters
		int getFontOversampleH() const { return fontOversampleH; }
		int getFontOversampleV() const { return fontOversampleV; }
		float getFontRasterizerMultiply() const { return fontRasterizerMultiply; }
		bool getFontPixelSnap() const { return fontPixelSnap; }
		int getFontAtlasSize() const { return fontAtlasSize; }
		float getFontUIBias() const { return fontUIBias; }
		
		// Counter font size getters
		float getFpsCounterFontSize() const { return fpsCounterFontSize; }
		
		// FPS Counter position getters
		float getFpsCounterHorizontalPadding() const { return fpsCounterHorizontalPadding; }
		float getFpsCounterVerticalPadding() const { return fpsCounterVerticalPadding; }

		// Bounding Box getters
		float getBoundingBoxThickness() const { return boundingBoxThickness; }
		bool getBoundingBoxAntiAliasing() const { return boundingBoxAntiAliasing; }
		bool getBoundingBoxRounded() const { return boundingBoxRounded; }
		float getBoundingBoxRounding() const { return boundingBoxRounding; }
		int getBoundingBoxType() const { return boundingBoxType; }
		float getBoundingBoxMaxDistance() const { return boundingBoxMaxDistance; }

		// Keybind getters
		int getItemESPToggleKey() const { return itemESPToggleKey; }
		bool isItemESPEnabled() const { return itemESPEnabled; }

		// Skeleton ESP getters (DayZ2-style)
		bool isPlayerSkeletonEnabled() const { return playerSkeletonEnabled; }
		bool isZombieSkeletonEnabled() const { return zombieSkeletonEnabled; }
		float getSkeletonLineThickness() const { return skeletonLineThickness; }
		float getPlayerSkeletonMaxDistance() const { return playerSkeletonMaxDistance; }
		float getZombieSkeletonMaxDistance() const { return zombieSkeletonMaxDistance; }
		int getSkeletonDetailLevel() const { return skeletonDetailLevel; }
		bool isHeadCircleEnabled() const { return headCircleEnabled; }
		float getHeadCircleSize() const { return headCircleSize; }

		// Health Bar ESP getters
		bool isPlayerHealthBarEnabled() const { return playerHealthBarEnabled; }
		bool isZombieHealthBarEnabled() const { return zombieHealthBarEnabled; }
		float getHealthBarWidth() const { return healthBarWidth; }
		float getHealthBarHeight() const { return healthBarHeight; }
		float getHealthBarOffsetY() const { return healthBarOffsetY; }
		float getHealthBarMaxDistance() const { return healthBarMaxDistance; }
		int getHealthBarType() const { return healthBarType; }
		bool isShowHealthNumbers() const { return showHealthNumbers; }
		bool isShowHealthBar() const { return showHealthBar; }
		bool isShowAllHealthStats() const { return showAllHealthStats; }

		// Personal Health Display getters
		bool isShowPersonalHealth() const { return showPersonalHealth; }
		int getPersonalHealthPosition() const { return personalHealthPosition; }
		float getPersonalHealthScale() const { return personalHealthScale; }
		bool isPersonalHealthBackground() const { return personalHealthBackground; }

		// Individual positioning getters
		bool isUseIndividualPositioning() const { return useIndividualPositioning; }
		float getHealthIndicatorX() const { return healthIndicatorX; }
		float getHealthIndicatorY() const { return healthIndicatorY; }
		float getBloodIndicatorX() const { return bloodIndicatorX; }
		float getBloodIndicatorY() const { return bloodIndicatorY; }
		float getShockIndicatorX() const { return shockIndicatorX; }
		float getShockIndicatorY() const { return shockIndicatorY; }
		float getStatusIndicatorX() const { return statusIndicatorX; }
		float getStatusIndicatorY() const { return statusIndicatorY; }

		// Individual indicator toggle getters
		bool isShowHealthIndicator() const { return showHealthIndicator; }
		bool isShowBloodIndicator() const { return showBloodIndicator; }
		bool isShowShockIndicator() const { return showShockIndicator; }
		bool isShowStatusIndicator() const { return showStatusIndicator; }

		// Health display formatting getters
		bool isShowHealthLabels() const { return showHealthLabels; }
		bool isUseCustomFont() const { return useCustomFont; }

		// Crosshair getters
		bool isCrosshairEnabled() const { return crosshairEnabled; }
		float getCrosshairSize() const { return crosshairSize; }
		float getCrosshairThickness() const { return crosshairThickness; }
		float getCrosshairGap() const { return crosshairGap; }
		float getCrosshairAlpha() const { return crosshairAlpha; }
		bool isCrosshairOutline() const { return crosshairOutline; }
		float getCrosshairOutlineThickness() const { return crosshairOutlineThickness; }
		bool isCrosshairDot() const { return crosshairDot; }
		float getCrosshairDotSize() const { return crosshairDotSize; }

		float getCrosshairVerticalOffset() const { return crosshairVerticalOffset; }
		float getCrosshairHorizontalOffset() const { return crosshairHorizontalOffset; }
		int getCrosshairColorR() const { return crosshairColorR; }
		int getCrosshairColorG() const { return crosshairColorG; }
		int getCrosshairColorB() const { return crosshairColorB; }
		int getCrosshairColorA() const { return crosshairColorA; }
		int getCrosshairOutlineColorR() const { return crosshairOutlineColorR; }
		int getCrosshairOutlineColorG() const { return crosshairOutlineColorG; }
		int getCrosshairOutlineColorB() const { return crosshairOutlineColorB; }
		int getCrosshairOutlineColorA() const { return crosshairOutlineColorA; }

		// Health Bar Color getters (already declared in ESP color section)

		// ===== ESP COLOR GETTERS =====

		// Player ESP Colors
		ImU32 getPlayerBoxColor() const;
		ImU32 getPlayerTextColor() const;
		ImU32 getPlayerNameColor() const;
		ImU32 getPlayerDistanceColor() const;
		ImU32 getPlayerHandColor() const;
		ImU32 getPlayerSkeletonColor() const;

		// Zombie ESP Colors
		ImU32 getZombieBoxColor() const;
		ImU32 getZombieTextColor() const;
		ImU32 getZombieSkeletonColor() const;

		// Animal ESP Colors
		ImU32 getAnimalBoxColor() const;
		ImU32 getAnimalTextColor() const;

		// Dead Player ESP Colors
		ImU32 getDeadPlayerBoxColor() const;
		ImU32 getDeadPlayerTextColor() const;

		// Vehicle ESP Colors
		ImU32 getVehicleBoxColor() const;
		ImU32 getVehicleTextColor() const;

		// Health Bar Colors
		ImU32 getHealthBarHighColor() const;
		ImU32 getHealthBarMediumColor() const;
		ImU32 getHealthBarLowColor() const;
		ImU32 getHealthBarBackgroundColor() const;

		// Container Contents ESP Colors
		ImU32 getContainerContentsColor() const;

		// ===== LOOT CATEGORY ESP COLORS =====

		// Weapon ESP Colors
		ImU32 getWeaponColor() const;

		// Clothing ESP Colors
		ImU32 getClothingColor() const;

		// Backpack ESP Colors
		ImU32 getBackpackColor() const;

		// Food ESP Colors
		ImU32 getFoodColor() const;

		// Ammo ESP Colors
		ImU32 getAmmoColor() const;

		// Proxy Magazine ESP Colors
		ImU32 getProxyMagazineColor() const;

		// Optic ESP Colors
		ImU32 getOpticColor() const;

		// Base Building ESP Colors
		ImU32 getBaseBuildingColor() const;

		// Melee ESP Colors
		ImU32 getMeleeColor() const;

		// Explosive ESP Colors
		ImU32 getExplosiveColor() const;

		// Container ESP Colors
		ImU32 getContainerColor() const;

		// Cooking ESP Colors
		ImU32 getCookingColor() const;

		// Camping ESP Colors
		ImU32 getCampingColor() const;

		// Stash ESP Colors
		ImU32 getStashColor() const;

		// Custom Container ESP Colors
		ImU32 getCustomContainerColor() const;

		// Ground Item ESP Colors
		ImU32 getGroundItemColor() const;

		// Crosshair Colors
		ImU32 getCrosshairColor() const;
		ImU32 getCrosshairOutlineColor() const;
		
		// Aim Point Calculation




		// fonts

		void setRadarFont(int value) { FontRADAR = value; }
		void setRadarFont2(int value) { FontRADAR2 = value; }


		void setShowPlayerList(bool value) { showPlayerList = value; }
		void setShowServerPlayerList(bool value) { showServerPlayerList = value; }
		void setShowDeadPlayers(bool value) { showDeadPlayers = value; }
		void setShowVehicles(bool value) { showVehicles = value; }
		void setShowBoats(bool value) { showBoats = value; }
		void setShowGrounditems(bool value) { showGrounditems = value; }
		void setShowDeadAnimals(bool value) { showDeadAnimals = value; }
		void setShowClothing(bool value) { showClothing = value; }
		void setShowWeapons(bool value) { showWeapons = value; }
		void setShowProxyMagazines(bool value) { showProxyMagazines = value; }
		void setShowBackpacks(bool value) { showBackpacks = value; }
		void setShowRare(bool value) { showRare = value; }
		void setShowFood(bool value) { showFood = value; }
		void setShowAmmo(bool value) { showAmmo = value; }
		void setShowZombies(bool value) { showZombies = value; }
		void setShowAnimals(bool value) { showAnimals = value; }
		void setShowBots(bool value) { showBots = value; }
		void setShowPlayerName(std::string value) { playername = value; }
		void setShowBlipSize(int value) { BlipSize = value; }
		void setShowBlipSize2(int value) { BlipSize2 = value; }
		void setAimlinelength(int value) { Aimlinelength = value; }
		void setShowOptics(bool value) { showOptics = value; }
		void setShowBase(bool value) { showBase = value; }
		void setShowMelee(bool value) { showMelee = value; }
		void setShowContainer(bool value) { showContainer = value; }
		void setShowCooking(bool value) { showCooking = value; }
		void setShowCamping(bool value) { showCamping = value; }
		void setShowStash(bool value) { showStash = value; }
		void setShowCustomContainer(bool value) { showCustomContainer = value; }
		void setLootDistanceDeadzone(int value) { LootDistanceDeadzone = value; }
		void setShowExplosives(bool value) { showExplosives = value; }
		void setZombiesBlipSize(int value) { ZombiesBlipSize = value; }
		void setAnimalsBlipSize(int value) { AnimalsBlipSize = value; }
		void setShowPlayerNameRadar(bool value) { showPlayerNameRadar = value; }
		void setShowPlayerDistanceRadar(bool value) { showPlayerDistanceRadar = value; }
		void setShowPlayerHandRadar(bool value) { showPlayerHandRadar = value; }
		void setShowRadar(bool value) { showRadar = value; }
		void setShowPerformanceInfo(bool value) { showPerformanceInfo = value; }
		void setShowBoundingBoxes(bool value) { showBoundingBoxes = value; }


		//fuser
		void setShowDeadPlayersFUSER(bool value) { showDeadPlayersFUSER = value; }
		void setShowVehiclesFUSER(bool value) { showVehiclesFUSER = value; }
		void setShowBoatsFUSER(bool value) { showBoatsFUSER = value; }
		void setShowGrounditemsFUSER(bool value) { showGrounditemsFUSER = value; }
		void setShowDeadAnimalsFUSER(bool value) { showDeadAnimalsFUSER = value; }
		void setShowClothingFUSER(bool value) { showClothingFUSER = value; }
		void setShowWeaponsFUSER(bool value) { showWeaponsFUSER = value; }
		void setShowProxyMagazinesFUSER(bool value) { showProxyMagazinesFUSER = value; }
		void setShowBackpacksFUSER(bool value) { showBackpacksFUSER = value; }
		void setShowRareFUSER(bool value) { showRareFUSER = value; }
		void setShowFoodFUSER(bool value) { showFoodFUSER = value; }
		void setShowAmmoFUSER(bool value) { showAmmoFUSER = value; }
		void setShowZombiesFUSER(bool value) { showZombiesFUSER = value; }
		void setShowAnimalsFUSER(bool value) { showAnimalsFUSER = value; }
		void setlootDistance(int value) { lootDistanceFUSER = value; }
		void setZombieDistance(int value) { ZombieDistanceFUSER = value; }
		void setShowPlayerInfoesp(bool value) { showPlayerInfoesp = value; }
		void setShowPlayerNameFUSER(bool value) { showPlayerNameFUSER = value; }
		void setShowPlayerDistanceFUSER(bool value) { showPlayerDistanceFUSER = value; }
		void setShowPlayerHandFUSER(bool value) { showPlayerHandFUSER = value; }
		void setShowPlayerInventoryFUSER(bool value) { showPlayerInventoryFUSER = value; }
		void setShowPlayerBoxFUSER(bool value) { showPlayerBoxFUSER = value; }
		void setShowBotsFUSER(bool value) { showBotsFUSER = value; }
		void setShowAnimalBoxFUSER(bool value) { showAnimalBoxFUSER = value; }
		void setShowAnimalNameFUSER(bool value) { showAnimalNameFUSER = value; }
		void setShowZombieBoxFUSER(bool value) { showZombieBoxFUSER = value; }
		void setShowZombieNameFUSER(bool value) { showZombieNameFUSER = value; }
		void setShowZombieDistanceFUSER(bool value) { showZombieDistanceFUSER = value; }
		void setShowOpticsFUSER(bool value) { showOpticsFUSER = value; }
		void setShowDebugFUSER(bool value) { showDebugFUSER = value; }

		// Entity distance setters
		void setAnimalMaxDistance(int value) { animalMaxDistance = value; }
		void setVehicleMaxDistance(int value) { vehicleMaxDistance = value; }
		void setBoatMaxDistance(int value) { boatMaxDistance = value; }
		void setDeadPlayerMaxDistance(int value) { deadPlayerMaxDistance = value; }
		void setDeadAnimalMaxDistance(int value) { deadAnimalMaxDistance = value; }
		void setPlayerMaxDistance(int value) { playerMaxDistance = value; }
		void setShowBaseFUSER(bool value) { showBaseFUSER = value; }
		void setShowMeleeFUSER(bool value) { showMeleeFUSER = value; }
		void setShowExplosivesFUSER(bool value) { showExplosivesFUSER = value; }
		void setShowContainerFUSER(bool value) { showContainerFUSER = value; }
		void setShowCookingFUSER(bool value) { showCookingFUSER = value; }
		void setShowCampingFUSER(bool value) { showCampingFUSER = value; }
		void setShowStashFUSER(bool value) { showStashFUSER = value; }
		void setShowCustomContainerFUSER(bool value) { showCustomContainerFUSER = value; }
		void setLootDebugDistance(int value) { LootDebugDistance = value; }
		void setEnableESPDebugLogging(bool value) { enableESPDebugLogging = value; }
		void setESPTextSize(float value) { espTextSize = value; }
		void setBaseFontSize(float value) {
			// Clamp to reasonable range to prevent issues
			if (value >= 8.0f && value <= 72.0f) {
				baseFontSize = value;
			}
		}
		void setPlayerNameFontSize(float value) {
			if (value >= 0.5f && value <= 3.0f) {
				playerNameFontSize = value;
			}
		}
		void setItemFontSize(float value) {
			if (value >= 0.5f && value <= 3.0f) {
				itemFontSize = value;
			}
		}
		void setDistanceFontSize(float value) {
			if (value >= 0.5f && value <= 3.0f) {
				distanceFontSize = value;
			}
		}

		// Skeleton ESP setters (DayZ2-style)
		void setPlayerSkeletonEnabled(bool value) { playerSkeletonEnabled = value; }
		void setZombieSkeletonEnabled(bool value) { zombieSkeletonEnabled = value; }
		void setSkeletonLineThickness(float value) { skeletonLineThickness = value; }
		void setPlayerSkeletonMaxDistance(float value) { playerSkeletonMaxDistance = value; }
		void setZombieSkeletonMaxDistance(float value) { zombieSkeletonMaxDistance = value; }
		void setSkeletonDetailLevel(int value) { skeletonDetailLevel = value; }
		void setHeadCircleEnabled(bool value) { headCircleEnabled = value; }
		void setHeadCircleSize(float value) { headCircleSize = value; }

		// Health Bar ESP setters
		void setPlayerHealthBarEnabled(bool value) { playerHealthBarEnabled = value; }
		void setZombieHealthBarEnabled(bool value) { zombieHealthBarEnabled = value; }
		void setHealthBarWidth(float value) { healthBarWidth = value; }
		void setHealthBarHeight(float value) { healthBarHeight = value; }
		void setHealthBarOffsetY(float value) { healthBarOffsetY = value; }
		void setHealthBarMaxDistance(float value) { healthBarMaxDistance = value; }
		void setHealthBarType(int value) { healthBarType = value; }
		void setShowHealthNumbers(bool value) { showHealthNumbers = value; }
		void setShowHealthBar(bool value) { showHealthBar = value; }
		void setShowAllHealthStats(bool value) { showAllHealthStats = value; }

		// Personal Health Display setters
		void setShowPersonalHealth(bool value) { showPersonalHealth = value; }
		void setPersonalHealthPosition(int value) { personalHealthPosition = value; }
		void setPersonalHealthScale(float value) { personalHealthScale = value; }
		void setPersonalHealthBackground(bool value) { personalHealthBackground = value; }

		// Individual positioning setters
		void setUseIndividualPositioning(bool value) { useIndividualPositioning = value; }
		void setHealthIndicatorX(float value) { healthIndicatorX = value; }
		void setHealthIndicatorY(float value) { healthIndicatorY = value; }
		void setBloodIndicatorX(float value) { bloodIndicatorX = value; }
		void setBloodIndicatorY(float value) { bloodIndicatorY = value; }
		void setShockIndicatorX(float value) { shockIndicatorX = value; }
		void setShockIndicatorY(float value) { shockIndicatorY = value; }
		void setStatusIndicatorX(float value) { statusIndicatorX = value; }
		void setStatusIndicatorY(float value) { statusIndicatorY = value; }

		// Individual indicator toggle setters
		void setShowHealthIndicator(bool value) { showHealthIndicator = value; }
		void setShowBloodIndicator(bool value) { showBloodIndicator = value; }
		void setShowShockIndicator(bool value) { showShockIndicator = value; }
		void setShowStatusIndicator(bool value) { showStatusIndicator = value; }

		// Health display formatting setters
		void setShowHealthLabels(bool value) { showHealthLabels = value; }
		void setUseCustomFont(bool value) { useCustomFont = value; }

		// Crosshair setters
		void setCrosshairEnabled(bool value) { crosshairEnabled = value; }
		void setCrosshairSize(float value) { crosshairSize = value; }
		void setCrosshairThickness(float value) { crosshairThickness = value; }
		void setCrosshairGap(float value) { crosshairGap = value; }
		void setCrosshairAlpha(float value) { crosshairAlpha = value; }
		void setCrosshairOutline(bool value) { crosshairOutline = value; }
		void setCrosshairOutlineThickness(float value) { crosshairOutlineThickness = value; }
		void setCrosshairDot(bool value) { crosshairDot = value; }
		void setCrosshairDotSize(float value) { crosshairDotSize = value; }

		void setCrosshairVerticalOffset(float value) { crosshairVerticalOffset = value; }
		void setCrosshairHorizontalOffset(float value) { crosshairHorizontalOffset = value; }
		void setCrosshairColorR(int value) { crosshairColorR = value; }
		void setCrosshairColorG(int value) { crosshairColorG = value; }
		void setCrosshairColorB(int value) { crosshairColorB = value; }
		void setCrosshairColorA(int value) { crosshairColorA = value; }
		void setCrosshairOutlineColorR(int value) { crosshairOutlineColorR = value; }
		void setCrosshairOutlineColorG(int value) { crosshairOutlineColorG = value; }
		void setCrosshairOutlineColorB(int value) { crosshairOutlineColorB = value; }
		void setCrosshairOutlineColorA(int value) { crosshairOutlineColorA = value; }

		// Font setters
		void setSelectedFontIndex(int index) { selectedFontIndex = index; }
		void setFontBold(bool value) { fontBold = value; }
		void setFontOutline(bool value) { fontOutline = value; }
		void setFontShadow(bool value) { fontShadow = value; }
		void setFontAlpha(float value) { 
			// Clamp value to valid range (0.0 to 1.0)
			if (value >= 0.0f && value <= 1.0f) {
				fontAlpha = value; 
			}
		}
		void setFontShadowOffset(float value) { fontShadowOffset = value; }
		void setFontOutlineSize(float value) { fontOutlineSize = value; }
		void setFontOutlineColor(const float color[4]) {
			fontOutlineColor[0] = color[0];
			fontOutlineColor[1] = color[1];
			fontOutlineColor[2] = color[2];
			fontOutlineColor[3] = color[3];
		}

		// Font quality setters
		void setFontOversampleH(int value) { 
			// Clamp value to safe range to prevent crashes
			if (value >= 4 && value <= 32) {
				fontOversampleH = value; 
			}
		}
		void setFontOversampleV(int value) { 
			// Clamp value to safe range to prevent crashes
			if (value >= 2 && value <= 16) {
				fontOversampleV = value; 
			}
		}
		void setFontRasterizerMultiply(float value) { 
			// Clamp value to safe range to prevent crashes
			if (value >= 0.5f && value <= 2.0f) {
				fontRasterizerMultiply = value; 
			}
		}
		void setFontPixelSnap(bool value) { fontPixelSnap = value; }
		void setFontAtlasSize(int value) {
			// Only allow specific valid sizes to prevent crashes
			if (value == 1024 || value == 2048 || value == 4096) {
				fontAtlasSize = value;
			}
		}
		void setFontUIBias(float value) {
			// Clamp value to safe range
			if (value >= -1.0f && value <= 1.0f) {
				fontUIBias = value;
			}
		}
		
		// Counter font size setters
		void setFpsCounterFontSize(float value) { 
			// Clamp value to reasonable range
			if (value >= 8.0f && value <= 50.0f) {
				fpsCounterFontSize = value;
			}
		}
		
		// FPS Counter position setters
		void setFpsCounterHorizontalPadding(float value) {
			// Clamp value to reasonable range (0 to screen width)
			if (value >= 0.0f && value <= 2560.0f) {
				fpsCounterHorizontalPadding = value;
			}
		}
		void setFpsCounterVerticalPadding(float value) {
			// Clamp value to reasonable range (0 to screen height)  
			if (value >= 0.0f && value <= 1440.0f) {
				fpsCounterVerticalPadding = value;
			}
		}

		// Bounding Box setters
		void setBoundingBoxThickness(float value) { boundingBoxThickness = value; }
		void setBoundingBoxAntiAliasing(bool value) { boundingBoxAntiAliasing = value; }
		void setBoundingBoxRounded(bool value) { boundingBoxRounded = value; }
		void setBoundingBoxRounding(float value) { boundingBoxRounding = value; }
		void setBoundingBoxType(int value) { boundingBoxType = value; }
		void setBoundingBoxMaxDistance(float value) { boundingBoxMaxDistance = value; }

		// Keybind setters
		void setItemESPToggleKey(int key) { itemESPToggleKey = key; }
		void setItemESPEnabled(bool enabled) { itemESPEnabled = enabled; }

		// Container Contents getters
		bool shouldShowContainerContents() const { return showContainerContents; }
		int getContainerContentsMaxItems() const { return containerContentsMaxItems; }
		float getContainerContentsMaxDistance() const { return containerContentsMaxDistance; }

		// Container Contents setters
		void setShowContainerContents(bool enabled) { showContainerContents = enabled; }
		void setContainerContentsMaxItems(int maxItems) { containerContentsMaxItems = maxItems; }
		void setContainerContentsMaxDistance(float maxDistance) { containerContentsMaxDistance = maxDistance; }



	};
}