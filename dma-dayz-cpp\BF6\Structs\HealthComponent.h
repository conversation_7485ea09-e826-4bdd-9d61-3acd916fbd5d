#pragma once
#include "DMAMemoryManagement/includes.h"

namespace BF6 {
    class HealthComponent : public DMAMem::MemoryObject {
    public:
        float Health;
        float MaxHealth;
        
        HealthComponent() {
            // BF2042 HealthComponent offsets - Forum confirmed (0x40, 0x44)
            this->registerOffset(0x040, &Health, sizeof(float));    // m_Health
            this->registerOffset(0x044, &MaxHealth, sizeof(float)); // m_MaxHealth
        }
        
        // Helper methods
        bool isAlive() const {
            return Health > 0.0f;
        }
        
        float getHealthPercentage() const {
            if (MaxHealth > 0.0f) {
                return (Health / MaxHealth) * 100.0f;
            }
            return 0.0f;
        }
        
        bool isLowHealth() const {
            return getHealthPercentage() < 25.0f;
        }
        
        bool isCriticalHealth() const {
            return getHealthPercentage() < 10.0f;
        }
    };
}
