#pragma once
#include "DMAMemoryManagement/includes.h";
#include "Entity.h";
#include "../DayzUtil.h";
#include "EntityFilterList.h"
#include <set>

// Forward declarations
namespace DMAMem {
	class VmmManager;
}

class WeirdTable
{
public:
	uint16_t validItemFlag; //0x0000
	char pad_0002[6]; //0x0002
	QWORD WeirdPtr; //0x0008
	QWORD Junk; //0x0010
}; //Size: 0x0018

namespace DayZ {
	class EntityTableSlowItem : public DMAMem::MemoryObject {
		struct SlowItemStruct {
			uint16_t flag; //0x0000
			char pad_0002[6]; //0x0002
			QWORD EntityPointer; //0x0008
			QWORD Junk; //0x0010
		};
		std::shared_ptr<SlowItemStruct[]> EntityPointers;
		int allocSize;
		int validSize;

		// Store manager and PID during resolution for blacklist checking
		DMAMem::VmmManager* currentManager = nullptr;
		DWORD currentPid = 0;

		// Quick blacklist check using minimal DMA reads (same as EntityTable)
		bool isEntityBlacklisted(DMAMem::VmmManager* manager, DWORD pid, QWORD entityPtr) {
			try {
				// Read only the EntityType pointer (minimal DMA read)
				QWORD entityTypePtr = 0;
				if (!manager->readMemory(pid, entityPtr + 0x180, &entityTypePtr, sizeof(QWORD))) {
					return true; // If we can't read it, consider it blacklisted
				}

				if (entityTypePtr == 0) return true;

				// Read TypeName pointer
				QWORD typeNamePtr = 0;
				if (!manager->readMemory(pid, entityTypePtr + 0x70, &typeNamePtr, sizeof(QWORD))) {
					return true;
				}

				if (typeNamePtr == 0) return true;

				// Read string length first (ArmaString uses uint16_t at offset 0x8)
				uint16_t stringLength = 0;
				if (!manager->readMemory(pid, typeNamePtr + 0x8, &stringLength, sizeof(uint16_t))) {
					return true;
				}

				// Sanity check string length
				if (stringLength == 0 || stringLength > 400) return true;

				// Read the actual string (ArmaString value at offset 0x10)
				std::vector<char> buffer(stringLength + 1, 0);
				if (!manager->readMemory(pid, typeNamePtr + 0x10, buffer.data(), stringLength)) {
					return true;
				}

				std::string entityName(buffer.data());

				// Check against blacklist
				return itemBlacklistSet.find(entityName) != itemBlacklistSet.end();
			}
			catch (...) {
				return true; // If anything fails, consider it blacklisted for safety
			}
		}
	protected:
		std::vector<DMAMem::MemoryObject::ResolutionRequest> postResolveResolutions() override {
			if (resolvedEntities.size() > 0) {
				std::vector<ResolutionRequest> requestVec;
				for (auto const ent : resolvedEntities) {
					auto entRes = ent->getRequestedResolutions(ent->_remoteAddress);
					DMAUtils::concatVectors(&requestVec, &entRes);
				}
				return requestVec;
			}

			// ENTITY LIST BLOCK READING OPTIMIZATION (SlowItem version)
			// Phase 1: Collect valid entity pointers (filter out blacklisted and invalid flags)
			std::vector<QWORD> validEntityPointers;
			validEntityPointers.reserve(validSize);

			for (int i = 0; i < allocSize && validEntityPointers.size() < validSize; i++) {
				SlowItemStruct entityPtr = EntityPointers[i];
				if (entityPtr.flag == 1) {
					// PERFORMANCE OPTIMIZATION: Skip blacklisted entities early to avoid expensive DMA operations
					if (entityPtr.EntityPointer != 0 && currentManager && isEntityBlacklisted(currentManager, currentPid, entityPtr.EntityPointer)) {
						continue; // Skip this entity entirely - no DMA reads, no processing, no logging!
					}

					if (entityPtr.EntityPointer != 0) {
						validEntityPointers.push_back(entityPtr.EntityPointer);
					}
				}
			}

			// Phase 2: Create entities and batch all resolution requests
			std::vector<ResolutionRequest> requestVec;
			requestVec.reserve(validEntityPointers.size() * 8); // Estimate ~8 requests per entity

			for (QWORD entityPointer : validEntityPointers) {
				auto ent = std::shared_ptr<Entity>(new Entity());
				auto entRes = ent->getRequestedResolutions(entityPointer);
				DMAUtils::concatVectors(&requestVec, &entRes);
				resolvedEntities.push_back(ent);
			}

			return requestVec;
		}

	public:
		std::vector<std::shared_ptr<Entity>> resolvedEntities;
		EntityTableSlowItem(int allocSize, int validSize) {
			this->validSize = validSize;
			this->allocSize = allocSize;
			EntityPointers = std::shared_ptr<SlowItemStruct[]>(new SlowItemStruct[allocSize]);
			this->registerOffset(0x0, EntityPointers.get(), sizeof(SlowItemStruct) * allocSize);
		}

		// Custom resolve method that stores manager and PID for blacklist checking
		void resolveObjectWithBlacklist(DMAMem::VmmManager* manager, DWORD pid, QWORD address, ULONG64 flags = VMMDLL_FLAG_NOCACHE) {
			currentManager = manager;
			currentPid = pid;
			DMAMem::MemoryObject::resolveObject(manager, pid, address, flags);
			// Clear after resolution to avoid dangling pointers
			currentManager = nullptr;
			currentPid = 0;
		}
	};
}