#pragma once
#include "DMAMemoryManagement/includes.h"
#include "ClientVehicleEntity.h"

// Forward declaration to avoid circular reference
namespace BF2042 {
    class ClientSoldierEntity;
}

namespace BF2042 {
    class ClientPlayer : public DMAMem::MemoryObject {
    public:
        QWORD PlayerNamePtr;
        uint32_t TeamId;
        std::shared_ptr<ClientSoldierEntity> SoldierEntity;
        std::shared_ptr<ClientVehicleEntity> VehicleEntity;
        
        ClientPlayer();  // Constructor defined in cpp to avoid circular reference
        
        // Helper methods
        bool isValid() const;
        std::string getName() const;
        bool isEnemy(uint32_t localTeamId) const;
        bool isInVehicle() const;
    };
}
