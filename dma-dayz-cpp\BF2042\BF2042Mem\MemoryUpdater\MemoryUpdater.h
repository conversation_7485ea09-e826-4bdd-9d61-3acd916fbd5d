#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMAMemoryManagement/CR3Bypass/CR3Bypass.h"
#include "DMAMemoryManagement/CR3Bypass/DynamicCR3Manager.h"
#include "../BF2042Mem.h"
#include "../../Structs/ClientPlayer.h"
#include "../../Structs/Camera.h"
#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>

namespace BF2042 {
    class MemoryUpdater {
        std::atomic<bool> threadRunning = false;

        BF2042::Mem* mem;
        std::mutex playerMutex;
        std::mutex cameraMutex;

        std::thread playerThread;
        std::thread cameraThread;

        std::vector<std::shared_ptr<ClientPlayer>> cachedPlayers;
        std::shared_ptr<ClientPlayer> cachedLocalPlayer;
        std::shared_ptr<Camera> cachedCamera;

        // CR3 Bypass for EA anti-cheat
        std::unique_ptr<DMAMem::BF2042CR3Reader> cr3Reader;
        std::unique_ptr<DMAMem::BF2042DynamicCR3Reader> dynamicCR3Reader;
        bool useCR3Bypass = false;
        bool useDynamicCR3 = false;
        
        // Update workers
        void playerWorker();
        void cameraWorker();
        
        // Helper methods
        bool updateGameContext();
        bool updatePlayerManager();
        bool updateGameRenderer();

    public:
        MemoryUpdater(BF2042::Mem* mem);
        ~MemoryUpdater();
        
        void beginUpdateLoop();
        void endUpdateLoop();
        
        // Getters for cached data
        std::vector<std::shared_ptr<ClientPlayer>> getAllPlayers();
        std::shared_ptr<ClientPlayer> getLocalPlayer();
        std::shared_ptr<Camera> getCamera();
        
        // Getters for VMM and PID access
        DMAMem::VmmManager* getVMM() { return mem->getVMM(); }
        DWORD getPid() { return mem->getPid(); }

        // CR3 Bypass methods
        bool initializeCR3Bypass();
        void enableCR3Bypass(bool enable) { useCR3Bypass = enable; }
        bool isCR3BypassEnabled() const { return useCR3Bypass; }
        void testCR3Bypass();
        void printCR3Statistics();

        // Dynamic CR3 methods (new EA bypass)
        bool initializeDynamicCR3();
        void enableDynamicCR3(bool enable) { useDynamicCR3 = enable; }
        bool isDynamicCR3Enabled() const { return useDynamicCR3; }
        void testDynamicCR3();
        void printDynamicCR3Statistics();

        // Performance methods
        size_t getPlayerCount() const;
        bool isRunning() const { return threadRunning; }
    };
}
