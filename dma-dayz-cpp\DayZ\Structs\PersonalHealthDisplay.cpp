#include "PersonalHealthDisplay.h"
#include "DMARender/includes.h"
#include <algorithm>

namespace DayZ {

    void PersonalHealthDisplay::DrawPersonalHealth(std::shared_ptr<DayZ::Entity> localPlayer, 
                                                 int position, float scale, bool showBackground) {
        if (!localPlayer) {
            return;
        }

        // Update health values
        localPlayer->ReadHealthValues();

        // Format health text
        std::string healthText = FormatPersonalHealthText(localPlayer);
        
        // Calculate text size with scaling
        ImFont* font = ImGui::GetFont();
        ImVec2 originalTextSize = font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, healthText.c_str());
        ImVec2 scaledTextSize = ImVec2(originalTextSize.x * scale, originalTextSize.y * scale);
        
        // Get position on screen
        ImVec2 textPosition = GetPositionOnScreen(position, scaledTextSize);
        
        // Get health color based on health percentage
        ImU32 textColor = GetHealthTextColor(localPlayer->health);
        
        // Draw the health text (use default font for grouped mode)
        DrawHealthText(healthText, textPosition, scale, showBackground, false, textColor);
    }

    void PersonalHealthDisplay::DrawHealthText(const std::string& text, ImVec2 position, float scale,
                                             bool showBackground, bool useCustomFont, ImU32 textColor) {
        ImDrawList* drawList = ImGui::GetBackgroundDrawList();

        // Select font based on useCustomFont setting
        ImFont* font = nullptr;
        if (useCustomFont) {
            // Try to get the current custom font from ImGui context
            if (ImGui::GetIO().Fonts->Fonts.size() > 1) {
                // Use the first custom font (index 1, since 0 is default)
                font = ImGui::GetIO().Fonts->Fonts[1];
            }

            // Fallback to default font if custom font not available
            if (!font || !font->IsLoaded()) {
                font = ImGui::GetFont();
            }
        } else {
            // Use default ImGui font
            font = ImGui::GetFont();
        }

        // Calculate scaled text size
        ImVec2 originalTextSize = font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, text.c_str());
        ImVec2 scaledTextSize = ImVec2(originalTextSize.x * scale, originalTextSize.y * scale);

        // Draw background if enabled
        if (showBackground) {
            ImVec2 bgMin = ImVec2(position.x - 5, position.y - 2);
            ImVec2 bgMax = ImVec2(position.x + scaledTextSize.x + 5, position.y + scaledTextSize.y + 2);
            drawList->AddRectFilled(bgMin, bgMax, IM_COL32(0, 0, 0, 150), 3.0f); // Rounded corners
        }

        // Draw text with scaling
        if (scale != 1.0f) {
            // Use scaled font rendering
            float originalFontSize = font->FontSize;

            // Push scaled font size
            ImGui::PushFont(font);
            drawList->AddText(font, originalFontSize * scale, position, textColor, text.c_str());
            ImGui::PopFont();
        } else {
            // Normal text rendering
            drawList->AddText(position, textColor, text.c_str());
        }
    }

    ImVec2 PersonalHealthDisplay::GetPositionOnScreen(int position, const ImVec2& textSize) {
        ImVec2 screenSize = ImGui::GetIO().DisplaySize;
        ImVec2 result;
        
        switch (position) {
            case 0: // Top Left
                result = ImVec2(MARGIN, MARGIN);
                break;
            case 1: // Top Right
                result = ImVec2(screenSize.x - textSize.x - MARGIN, MARGIN);
                break;
            case 2: // Bottom Left
                result = ImVec2(MARGIN, screenSize.y - textSize.y - MARGIN);
                break;
            case 3: // Bottom Right
                result = ImVec2(screenSize.x - textSize.x - MARGIN, screenSize.y - textSize.y - MARGIN);
                break;
            default: // Default to top left
                result = ImVec2(MARGIN, MARGIN);
                break;
        }
        
        return result;
    }

    std::string PersonalHealthDisplay::FormatPersonalHealthText(std::shared_ptr<DayZ::Entity> localPlayer) {
        if (!localPlayer) {
            return "Health: N/A";
        }

        // Format comprehensive health information
        std::string healthText = "Health: " + std::to_string((int)localPlayer->health) + "%\n";
        healthText += "Blood: " + std::to_string((int)localPlayer->blood) + "/5000\n";
        healthText += "Shock: " + std::to_string((int)localPlayer->shock) + "%";
        
        // Add status indicators
        if (localPlayer->isDead) {
            healthText += "\nStatus: DEAD";
        } else if (localPlayer->health < 25.0f) {
            healthText += "\nStatus: CRITICAL";
        } else if (localPlayer->health < 50.0f) {
            healthText += "\nStatus: INJURED";
        } else if (localPlayer->health < 75.0f) {
            healthText += "\nStatus: WOUNDED";
        } else {
            healthText += "\nStatus: HEALTHY";
        }
        
        return healthText;
    }

    void PersonalHealthDisplay::DrawPersonalHealthIndividual(std::shared_ptr<DayZ::Entity> localPlayer,
                                                           float scale, bool showBackground,
                                                           float healthX, float healthY,
                                                           float bloodX, float bloodY,
                                                           float shockX, float shockY,
                                                           float statusX, float statusY,
                                                           bool showHealth, bool showBlood, bool showShock, bool showStatus,
                                                           bool showLabels, bool useCustomFont) {
        if (!localPlayer) {
            return;
        }

        // Update health values
        localPlayer->ReadHealthValues();

        // Draw each indicator at its individual position (only if enabled)
        if (showHealth) {
            DrawHealthIndicator(localPlayer, healthX, healthY, scale, showBackground, showLabels, useCustomFont);
        }
        if (showBlood) {
            DrawBloodIndicator(localPlayer, bloodX, bloodY, scale, showBackground, showLabels, useCustomFont);
        }
        if (showShock) {
            DrawShockIndicator(localPlayer, shockX, shockY, scale, showBackground, showLabels, useCustomFont);
        }
        if (showStatus) {
            DrawStatusIndicator(localPlayer, statusX, statusY, scale, showBackground, showLabels, useCustomFont);
        }
    }

    void PersonalHealthDisplay::DrawHealthIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                                  float scale, bool showBackground, bool showLabels, bool useCustomFont) {
        std::string healthText = FormatHealthValue(localPlayer, "health", showLabels);
        ImU32 textColor = GetHealthTextColor(localPlayer->health);

        ImFont* font = ImGui::GetFont();
        ImVec2 textSize = font->CalcTextSizeA(font->FontSize * scale, FLT_MAX, 0.0f, healthText.c_str());
        ImVec2 position = GetPositionFromPercentage(x, y, textSize);

        DrawHealthText(healthText, position, scale, showBackground, useCustomFont, textColor);
    }

    void PersonalHealthDisplay::DrawBloodIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                                 float scale, bool showBackground, bool showLabels, bool useCustomFont) {
        std::string bloodText = FormatHealthValue(localPlayer, "blood", showLabels);
        ImU32 textColor = GetHealthTextColor(localPlayer->blood / 50.0f); // Convert to percentage

        ImFont* font = ImGui::GetFont();
        ImVec2 textSize = font->CalcTextSizeA(font->FontSize * scale, FLT_MAX, 0.0f, bloodText.c_str());
        ImVec2 position = GetPositionFromPercentage(x, y, textSize);

        DrawHealthText(bloodText, position, scale, showBackground, useCustomFont, textColor);
    }

    void PersonalHealthDisplay::DrawShockIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                                 float scale, bool showBackground, bool showLabels, bool useCustomFont) {
        std::string shockText = FormatHealthValue(localPlayer, "shock", showLabels);
        ImU32 textColor = GetHealthTextColor(localPlayer->shock);

        ImFont* font = ImGui::GetFont();
        ImVec2 textSize = font->CalcTextSizeA(font->FontSize * scale, FLT_MAX, 0.0f, shockText.c_str());
        ImVec2 position = GetPositionFromPercentage(x, y, textSize);

        DrawHealthText(shockText, position, scale, showBackground, useCustomFont, textColor);
    }

    void PersonalHealthDisplay::DrawStatusIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                                  float scale, bool showBackground, bool showLabels, bool useCustomFont) {
        std::string statusText = FormatHealthValue(localPlayer, "status", showLabels);
        ImU32 textColor = GetHealthTextColor(localPlayer->health);

        ImFont* font = ImGui::GetFont();
        ImVec2 textSize = font->CalcTextSizeA(font->FontSize * scale, FLT_MAX, 0.0f, statusText.c_str());
        ImVec2 position = GetPositionFromPercentage(x, y, textSize);

        DrawHealthText(statusText, position, scale, showBackground, useCustomFont, textColor);
    }

    ImVec2 PersonalHealthDisplay::GetPositionFromPercentage(float xPercent, float yPercent, const ImVec2& textSize) {
        ImVec2 screenSize = ImGui::GetIO().DisplaySize;

        // Convert percentage to pixel position
        float x = (screenSize.x * xPercent) / 100.0f;
        float y = (screenSize.y * yPercent) / 100.0f;

        // Ensure text doesn't go off screen
        x = (std::max)(0.0f, (std::min)(x, screenSize.x - textSize.x));
        y = (std::max)(0.0f, (std::min)(y, screenSize.y - textSize.y));

        return ImVec2(x, y);
    }

    std::string PersonalHealthDisplay::GetHealthStatus(std::shared_ptr<DayZ::Entity> localPlayer) {
        if (!localPlayer) {
            return "N/A";
        }

        if (localPlayer->isDead) {
            return "DEAD";
        } else if (localPlayer->health < 25.0f) {
            return "CRITICAL";
        } else if (localPlayer->health < 50.0f) {
            return "INJURED";
        } else if (localPlayer->health < 75.0f) {
            return "WOUNDED";
        } else {
            return "HEALTHY";
        }
    }

    std::string PersonalHealthDisplay::FormatHealthValue(std::shared_ptr<DayZ::Entity> localPlayer, const std::string& type, bool showLabels) {
        if (!localPlayer) {
            return "N/A";
        }

        if (type == "health") {
            int healthValue = (int)localPlayer->health;
            return showLabels ? "Health: " + std::to_string(healthValue) + "%" : std::to_string(healthValue) + "%";
        } else if (type == "blood") {
            int bloodValue = (int)localPlayer->blood;
            return showLabels ? "Blood: " + std::to_string(bloodValue) + "/5000" : std::to_string(bloodValue) + "/5000";
        } else if (type == "shock") {
            int shockValue = (int)localPlayer->shock;
            return showLabels ? "Shock: " + std::to_string(shockValue) + "%" : std::to_string(shockValue) + "%";
        } else if (type == "status") {
            std::string statusValue = GetHealthStatus(localPlayer);
            return showLabels ? "Status: " + statusValue : statusValue;
        }

        return "Unknown";
    }

    ImU32 PersonalHealthDisplay::GetHealthTextColor(float healthPercent) {
        // Color-code based on health percentage
        if (healthPercent >= 75.0f) {
            return IM_COL32(0, 255, 0, 255);    // Green - Healthy
        } else if (healthPercent >= 50.0f) {
            return IM_COL32(255, 255, 0, 255);  // Yellow - Wounded
        } else if (healthPercent >= 25.0f) {
            return IM_COL32(255, 165, 0, 255);  // Orange - Injured
        } else {
            return IM_COL32(255, 0, 0, 255);    // Red - Critical
        }
    }

} // namespace DayZ
