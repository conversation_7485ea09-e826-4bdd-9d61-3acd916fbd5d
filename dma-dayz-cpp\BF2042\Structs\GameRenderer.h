#pragma once
#include "DMAMemoryManagement/includes.h"
#include "ViewMatrix.h"

namespace BF2042 {
    class GameRenderer : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<ViewMatrix> RenderView;
        
        GameRenderer() {
            RenderView = std::shared_ptr<ViewMatrix>(new ViewMatrix());
            
            // BF2042 GameRenderer offsets - Forum confirmed
            this->registerPointer(0x60, RenderView.get()); // RenderView pointer - Forum: 0x60
        }
        
        // Helper methods
        bool isValid() const {
            return RenderView && RenderView->isValid();
        }
        
        // World-to-screen conversion
        bool worldToScreen(const DMARender::Vector3& worldPos, DMARender::Vector2& screenPos, 
                          int screenWidth, int screenHeight) const {
            if (!RenderView) {
                return false;
            }
            
            return RenderView->worldToScreen(worldPos, screenPos, screenWidth, screenHeight);
        }
        
        // Get view matrix for manual calculations
        const float* getViewProjectionMatrix() const {
            if (RenderView) {
                return RenderView->getViewProjectionMatrix();
            }
            return nullptr;
        }
    };
}
