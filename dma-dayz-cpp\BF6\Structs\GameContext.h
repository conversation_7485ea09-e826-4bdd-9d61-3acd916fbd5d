#pragma once
#include "DMAMemoryManagement/includes.h"
#include "PlayerManager.h"

namespace BF6 {
    class GameContext : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<PlayerManager> PlayerMgr;
        
        GameContext() {
            PlayerMgr = std::shared_ptr<PlayerManager>(new PlayerManager());
            
            // BF2042 GameContext offsets from UnknownCheats forum (2025 Update)
            this->registerPointer(0xA8, PlayerMgr.get()); // m_PlayerManager (forum confirmed: 0xA8)
        }
        
        // Helper methods
        bool isValid() const {
            if (!PlayerMgr) return false;
            return PlayerMgr->isValid();
        }
        
        std::vector<std::shared_ptr<ClientPlayer>> getAllPlayers() const {
            if (PlayerMgr) {
                return PlayerMgr->getAllPlayers();
            }
            return {};
        }
        
        std::shared_ptr<ClientPlayer> getLocalPlayer() const {
            if (PlayerMgr) {
                return PlayerMgr->getLocalPlayer();
            }
            return nullptr;
        }
        
        uint32_t getPlayerCount() const {
            if (PlayerMgr) {
                return PlayerMgr->getPlayerCount();
            }
            return 0;
        }
    };
}
