#include "BF2042Signatures.h"
#include <iostream>
#include <iomanip>
#include <sstream>

// External debug logging function (same as main ESP)
extern void DebugLog(const std::string& message);

namespace DMAMem {

    // Forum-confirmed BF2042 signature patterns
    const BF2042Signatures::SignaturePattern BF2042Signatures::BF2042_PATTERNS[] = {
        {
            "GameContext",
            "48 83 EC 20 48 8B ? ? ? ? ? 48 8B ? 48 8B ? ? ? ? ? 48 85 ? 74 ?",
            "BF2042.exe",
            0x0
        },
        {
            "GameContext_Alt1",
            "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 50 ?",
            "BF2042.exe",
            0x0
        },
        {
            "GameContext_Alt2",
            "48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 40 ?",
            "BF2042.exe",
            0x0
        },
        {
            "GameContext_Alt3",
            "48 8B 0D ? ? ? ? 48 8B 01 48 8B 80 ? ? ? ?",
            "BF2042.exe",
            0x0
        },
        {
            "GameContext_Alt4",
            "48 8B 15 ? ? ? ? 48 85 D2 0F 84 ? ? ? ?",
            "BF2042.exe",
            0x0
        },
        {
            "GameContext_Alt5",
            "4C 8B 05 ? ? ? ? 4D 85 C0 74 ?",
            "BF2042.exe",
            0x0
        },
        {
            "AimAssist",
            "48 8B 0D ? ? ? ? 48 8B 01 FF 50 20 48 8D 96 ? ? ? ? 48 8B 0A",
            "BF2042.exe",
            0x0
        },
        // Add more patterns as needed
        { nullptr, nullptr, nullptr, 0 } // Sentinel
    };

    BF2042Signatures::BF2042Signatures(VmmManager* vmm, DWORD pid, const std::string& procName)
        : vmmManager(vmm), targetPID(pid), processName(procName) {
    }

    BOOL BF2042Signatures::Initialize() {
        if (!vmmManager) {
            std::cout << "[-] VmmManager is null" << std::endl;
            return FALSE;
        }

        std::cout << "[+] Initializing BF2042 signature scanning..." << std::endl;

        // Ensure memory access is working (CR3 bypass if needed)
        if (!EnsureMemoryAccess()) {
            std::cout << "[-] Failed to ensure memory access, but continuing..." << std::endl;
            std::cout << "[!] Signature scanning may not work properly" << std::endl;
        } else {
            std::cout << "[+] Memory access verified successfully" << std::endl;
        }

        // Scan for critical signatures
        QWORD gameContext = FindGameContext();
        if (gameContext != 0) {
            cachedAddresses["GameContext"] = gameContext;
            std::cout << "[+] GameContext found at: 0x" << std::hex << gameContext << std::endl;
        } else {
            std::cout << "[-] GameContext not found via signature scanning" << std::endl;
            std::cout << "[!] Will continue initialization to allow fallback to hardcoded addresses" << std::endl;
        }

        // Scan for additional signatures
        QWORD aimAssist = FindAimAssistData();
        if (aimAssist != 0) {
            cachedAddresses["AimAssist"] = aimAssist;
            std::cout << "[+] AimAssist found at: 0x" << std::hex << aimAssist << std::endl;
        }

        // Validate found structures
        if (!ValidateFoundAddresses()) {
            std::cout << "[-] Address validation failed" << std::endl;
            return FALSE;
        }

        std::cout << "[+] BF2042 signature scanning completed successfully" << std::endl;
        return TRUE;
    }

    QWORD BF2042Signatures::FindGameContext() {
        DebugLog("[BF2042Signatures] Attempting to find GameContext using multiple signature patterns...");

        // Try all GameContext patterns
        const std::vector<std::string> gameContextPatterns = {
            "48 83 EC 20 48 8B ? ? ? ? ? 48 8B ? 48 8B ? ? ? ? ? 48 85 ? 74 ?",  // Primary pattern
            "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 50 ?",                   // Alternative pattern 1
            "48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 40 ?",                         // Alternative pattern 2
            "48 8B 0D ? ? ? ? 48 8B 01 48 8B 80 ? ? ? ?",                        // Alternative pattern 3
            "48 8B 15 ? ? ? ? 48 85 D2 0F 84 ? ? ? ?",                           // Alternative pattern 4
            "4C 8B 05 ? ? ? ? 4D 85 C0 74 ?",                                    // Alternative pattern 5
            "48 89 5C 24 ? 57 48 83 EC 20 48 8B ? ? ? ? ?",                      // Function prologue pattern
            "48 8B ? ? ? ? ? 48 85 ? 0F 84 ? ? ? ? 48 8B ?",                     // Extended pattern
        };

        for (size_t i = 0; i < gameContextPatterns.size(); i++) {
            DebugLog("[BF2042Signatures] Trying GameContext pattern " + std::to_string(i + 1) + "/" + std::to_string(gameContextPatterns.size()) + "...");

            QWORD result = RobustPatternScan(gameContextPatterns[i].c_str(), processName);
            if (result != 0) {
                DebugLog("[BF2042Signatures] GameContext signature found at: 0x" + std::to_string(result) + " (pattern " + std::to_string(i + 1) + ")");

                // Additional validation - check if this looks like a valid function
                BYTE testBytes[16] = { 0 };
                if (vmmManager->readMemory(targetPID, result, testBytes, sizeof(testBytes))) {
                    std::cout << "[+] Memory read successful, validating signature..." << std::endl;

                    // Try to extract GameContext address from the instruction
                    QWORD gameContextAddr = ExtractGameContextAddress(result, gameContextPatterns[i]);
                    if (gameContextAddr != 0) {
                        std::cout << "[+] GameContext address extracted: 0x" << std::hex << gameContextAddr << std::endl;
                        return gameContextAddr;
                    }

                    // Fallback: return the function address itself
                    std::cout << "[+] Using function address as GameContext: 0x" << std::hex << result << std::endl;
                    return result;
                } else {
                    std::cout << "[-] Failed to read memory at signature location" << std::endl;
                }
            } else {
                std::cout << "[-] Pattern " << (i + 1) << " not found" << std::endl;
            }
        }

        std::cout << "[-] GameContext signature not found with any pattern" << std::endl;
        return 0;
    }

    QWORD BF2042Signatures::ExtractGameContextAddress(QWORD functionAddr, const std::string& pattern) {
        std::cout << "[+] Attempting to extract GameContext address from function at 0x" << std::hex << functionAddr << std::endl;

        // Read function bytes to analyze the instruction
        BYTE functionBytes[64] = { 0 };
        if (!vmmManager->readMemory(targetPID, functionAddr, functionBytes, sizeof(functionBytes))) {
            std::cout << "[-] Failed to read function bytes" << std::endl;
            return 0;
        }

        // Look for common GameContext access patterns
        for (int i = 0; i < 48; i++) {
            // Pattern: 48 8B 0D ?? ?? ?? ?? (mov rcx, [rip+offset])
            if (functionBytes[i] == 0x48 && functionBytes[i + 1] == 0x8B && functionBytes[i + 2] == 0x0D) {
                // Extract RIP-relative offset (little endian)
                int32_t offset = *(int32_t*)&functionBytes[i + 3];
                QWORD targetAddr = functionAddr + i + 7 + offset; // RIP + instruction_size + offset

                std::cout << "[+] Found mov rcx, [rip+offset] at offset " << i << ", target: 0x" << std::hex << targetAddr << std::endl;

                // Read the value at the target address (this should be GameContext)
                QWORD gameContextAddr = 0;
                if (vmmManager->readMemory(targetPID, targetAddr, &gameContextAddr, sizeof(gameContextAddr))) {
                    if (gameContextAddr != 0 && gameContextAddr > 0x10000) {
                        std::cout << "[+] GameContext address extracted: 0x" << std::hex << gameContextAddr << std::endl;
                        return gameContextAddr;
                    }
                }
            }

            // Pattern: 48 8B 05 ?? ?? ?? ?? (mov rax, [rip+offset])
            if (functionBytes[i] == 0x48 && functionBytes[i + 1] == 0x8B && functionBytes[i + 2] == 0x05) {
                int32_t offset = *(int32_t*)&functionBytes[i + 3];
                QWORD targetAddr = functionAddr + i + 7 + offset;

                std::cout << "[+] Found mov rax, [rip+offset] at offset " << i << ", target: 0x" << std::hex << targetAddr << std::endl;

                QWORD gameContextAddr = 0;
                if (vmmManager->readMemory(targetPID, targetAddr, &gameContextAddr, sizeof(gameContextAddr))) {
                    if (gameContextAddr != 0 && gameContextAddr > 0x10000) {
                        std::cout << "[+] GameContext address extracted: 0x" << std::hex << gameContextAddr << std::endl;
                        return gameContextAddr;
                    }
                }
            }

            // Pattern: 48 8B 15 ?? ?? ?? ?? (mov rdx, [rip+offset])
            if (functionBytes[i] == 0x48 && functionBytes[i + 1] == 0x8B && functionBytes[i + 2] == 0x15) {
                int32_t offset = *(int32_t*)&functionBytes[i + 3];
                QWORD targetAddr = functionAddr + i + 7 + offset;

                std::cout << "[+] Found mov rdx, [rip+offset] at offset " << i << ", target: 0x" << std::hex << targetAddr << std::endl;

                QWORD gameContextAddr = 0;
                if (vmmManager->readMemory(targetPID, targetAddr, &gameContextAddr, sizeof(gameContextAddr))) {
                    if (gameContextAddr != 0 && gameContextAddr > 0x10000) {
                        std::cout << "[+] GameContext address extracted: 0x" << std::hex << gameContextAddr << std::endl;
                        return gameContextAddr;
                    }
                }
            }
        }

        std::cout << "[-] Could not extract GameContext address from function" << std::endl;
        return 0;
    }

    QWORD BF2042Signatures::FindAimAssistData() {
        const char* pattern = "48 8B 0D ? ? ? ? 48 8B 01 FF 50 20 48 8D 96 ? ? ? ? 48 8B 0A";
        
        QWORD result = RobustPatternScan(pattern, processName);
        if (result != 0) {
            std::cout << "[+] AimAssist signature found at: 0x" << std::hex << result << std::endl;
            return result;
        }

        std::cout << "[-] AimAssist signature not found" << std::endl;
        return 0;
    }

    BOOL BF2042Signatures::ValidateFoundAddresses() {
        // Basic validation - ensure we have critical addresses
        if (cachedAddresses.find("GameContext") == cachedAddresses.end()) {
            return FALSE;
        }

        // Additional validation could be added here
        return TRUE;
    }

    QWORD BF2042Signatures::GetCachedAddress(const std::string& name) {
        auto it = cachedAddresses.find(name);
        if (it != cachedAddresses.end()) {
            return it->second;
        }
        return 0;
    }

    BOOL BF2042Signatures::RescanSignatures() {
        std::cout << "[+] Rescanning BF2042 signatures..." << std::endl;
        cachedAddresses.clear();
        return Initialize();
    }

    std::unordered_map<std::string, QWORD> BF2042Signatures::GetAllAddresses() const {
        return cachedAddresses;
    }

    BOOL BF2042Signatures::GetModuleInfo(const std::string& moduleName, QWORD& baseAddress, SIZE_T& moduleSize) {
        DebugLog("[BF2042Signatures] GetModuleInfo called for: " + moduleName);
        DebugLog("[BF2042Signatures] Target PID: " + std::to_string(targetPID));

        PVMMDLL_MAP_MODULEENTRY moduleEntry = nullptr;
        BOOL result = VMMDLL_Map_GetModuleFromNameU(vmmManager->getVmm(), targetPID,
                                                   const_cast<LPSTR>(moduleName.c_str()),
                                                   &moduleEntry, 0);

        if (result && moduleEntry) {
            baseAddress = moduleEntry->vaBase;
            moduleSize = moduleEntry->cbImageSize;
            DebugLog("[BF2042Signatures] Module found - Base: 0x" + std::to_string(baseAddress) + ", Size: 0x" + std::to_string(moduleSize));
            return TRUE;
        }

        DebugLog("[BF2042Signatures] Module not found, listing available modules...");

        // List all available modules for debugging
        DebugLog("[BF2042Signatures] Attempting to get module list for PID: " + std::to_string(targetPID));
        DebugLog("[BF2042Signatures] VmmManager pointer: " + std::to_string(reinterpret_cast<uintptr_t>(vmmManager)));
        DebugLog("[BF2042Signatures] VMM handle: " + std::to_string(reinterpret_cast<uintptr_t>(vmmManager->getVmm())));

        PVMMDLL_MAP_MODULE pModuleMap = nullptr;
        BOOL moduleListResult = VMMDLL_Map_GetModuleU(vmmManager->getVmm(), targetPID, &pModuleMap, 0);
        DebugLog("[BF2042Signatures] VMMDLL_Map_GetModuleU result: " + std::to_string(moduleListResult));

        if (moduleListResult && pModuleMap) {
            DebugLog("[BF2042Signatures] Module map retrieved successfully, count: " + std::to_string(pModuleMap->cMap));
            DebugLog("[BF2042Signatures] Available modules:");
            for (DWORD i = 0; i < pModuleMap->cMap; i++) {
                std::string modName = pModuleMap->pMap[i].uszText;
                DebugLog("[BF2042Signatures] - " + modName);

                // Check if this might be BF2042 with a different name
                if (modName.find("bf") != std::string::npos ||
                    modName.find("BF") != std::string::npos ||
                    modName.find("battlefield") != std::string::npos ||
                    modName.find("Battlefield") != std::string::npos) {
                    DebugLog("[BF2042Signatures] *** POTENTIAL BF2042 MODULE: " + modName + " ***");
                }
            }
            VMMDLL_MemFree(pModuleMap);
        } else {
            DebugLog("[BF2042Signatures] Failed to get module list - moduleListResult: " + std::to_string(moduleListResult) + ", pModuleMap: " + std::to_string(reinterpret_cast<uintptr_t>(pModuleMap)));
        }

        return FALSE;
    }

    QWORD BF2042Signatures::RobustPatternScan(const char* pattern, const std::string& moduleName) {
        DebugLog("[BF2042Signatures] RobustPatternScan called for module: " + moduleName);
        DebugLog("[BF2042Signatures] Pattern: " + std::string(pattern));

        QWORD moduleBase = 0;
        SIZE_T moduleSize = 0;

        if (!GetModuleInfo(moduleName, moduleBase, moduleSize)) {
            DebugLog("[BF2042Signatures] Failed to get module info for: " + moduleName);
            return 0;
        }

        DebugLog("[BF2042Signatures] Module " + moduleName + " found - Base: 0x" + std::to_string(moduleBase) + ", Size: 0x" + std::to_string(moduleSize));

        // Strategy 1: Try full module scan
        DebugLog("[BF2042Signatures] Strategy 1: Full module scan...");
        QWORD result = vmmManager->FindSignature(pattern, moduleBase, moduleBase + moduleSize, targetPID);

        if (result != 0) {
            DebugLog("[BF2042Signatures] Pattern found at: 0x" + std::to_string(result) + " (full scan)");
            return result;
        } else {
            DebugLog("[BF2042Signatures] Pattern not found in full scan");
        }

        // Strategy 2: Try scanning in chunks (sometimes helps with memory access issues)
        std::cout << "[+] Strategy 2: Chunked scanning..." << std::endl;
        const SIZE_T chunkSize = 0x100000; // 1MB chunks
        for (QWORD addr = moduleBase; addr < moduleBase + moduleSize; addr += chunkSize) {
            QWORD endAddr = min(addr + chunkSize, moduleBase + moduleSize);
            result = vmmManager->FindSignature(pattern, addr, endAddr, targetPID);

            if (result != 0) {
                std::cout << "[+] Pattern found at: 0x" << std::hex << result << " (chunked scan)" << std::endl;
                return result;
            }
        }

        // Strategy 3: Try scanning just the .text section (code section)
        std::cout << "[+] Strategy 3: .text section scan..." << std::endl;
        QWORD textBase = moduleBase + 0x1000; // Usually .text starts at +0x1000
        SIZE_T textSize = min(moduleSize / 2, (SIZE_T)0x800000); // Limit to reasonable size
        result = vmmManager->FindSignature(pattern, textBase, textBase + textSize, targetPID);

        if (result != 0) {
            std::cout << "[+] Pattern found at: 0x" << std::hex << result << " (.text scan)" << std::endl;
            return result;
        }

        // Strategy 4: Try multiple signature variations (remove some wildcards)
        std::cout << "[+] Strategy 4: Relaxed pattern matching..." << std::endl;
        std::string relaxedPattern = pattern;
        // Replace some specific bytes with wildcards to make pattern more flexible
        // This is a simple approach - could be made more sophisticated

        std::cout << "[-] Pattern not found in " << moduleName << " with any strategy" << std::endl;
        return 0;
    }

    void BF2042Signatures::PrintDiagnostics() {
        std::cout << "\n===== BF2042 Signature Diagnostics =====" << std::endl;
        std::cout << "Process: " << processName << " (PID: " << targetPID << ")" << std::endl;
        std::cout << "Found addresses:" << std::endl;

        for (const auto& pair : cachedAddresses) {
            std::cout << "  " << pair.first << ": 0x" << std::hex << pair.second << std::endl;
        }

        if (cachedAddresses.empty()) {
            std::cout << "  No addresses found!" << std::endl;
        }

        std::cout << "========================================\n" << std::endl;
    }

    BOOL BF2042Signatures::EnsureMemoryAccess() {
        // Test basic memory access
        QWORD moduleBase = 0;
        SIZE_T moduleSize = 0;

        if (GetModuleInfo(processName, moduleBase, moduleSize)) {
            // Memory access is working
            return TRUE;
        }

        std::cout << "[!] Memory access issues detected, attempting CR3 fix..." << std::endl;
        
        // Try CR3 fix
        if (vmmManager->FixCR3Advanced(targetPID)) {
            std::cout << "[+] CR3 fix successful" << std::endl;
            return TRUE;
        }

        std::cout << "[-] CR3 fix failed" << std::endl;
        return FALSE;
    }

    // ===== TYPEINFO BRUTEFORCE IMPLEMENTATION =====

    int BF2042Signatures::BruteForceTypeInfoSeed(const std::string& className, int maxSeeds) {
        std::cout << "[+] Bruteforcing TypeInfo seed for: " << className << std::endl;

        for (int seed = 0; seed < maxSeeds; seed++) {
            DWORD hash = CalculateTypeInfoHash(className, seed);

            // Known hash for ClientSoldierEntity from forum
            if (className == "clientsoldierentity" && hash == 0x0BEF9139B) {
                std::cout << "[+] Found TypeInfo seed: " << seed << std::endl;
                return seed;
            }

            // For other classes, you would need to implement validation
            // This is a simplified version
        }

        std::cout << "[-] TypeInfo seed not found for: " << className << std::endl;
        return -1;
    }

    DWORD BF2042Signatures::CalculateTypeInfoHash(const std::string& className, int seed) {
        // This is a simplified implementation
        // The actual implementation would use SHA256 as described in the forum
        std::string combined = className + std::to_string(seed);

        // Simple hash for demonstration - replace with actual SHA256
        DWORD hash = 0;
        for (char c : combined) {
            hash = hash * 31 + c;
        }

        return hash;
    }

    QWORD BF2042Signatures::FindClassByTypeInfo(DWORD targetHash) {
        // This would implement the actual TypeInfo scanning
        // For now, return 0 as this requires more complex implementation
        return 0;
    }

    BOOL BF2042Signatures::ValidateBF2042Structures() {
        std::cout << "[+] Validating BF2042 structures..." << std::endl;

        QWORD gameContext = GetCachedAddress("GameContext");
        if (gameContext == 0) {
            std::cout << "[-] GameContext not found for validation" << std::endl;
            return FALSE;
        }

        // Test known offsets from forum analysis
        // GameContext + 0xA8 should point to PlayerManager
        QWORD playerManagerPtr = 0;
        if (!vmmManager->readMemory(targetPID, gameContext + 0xA8, &playerManagerPtr, sizeof(QWORD))) {
            std::cout << "[-] Failed to read PlayerManager pointer" << std::endl;
            return FALSE;
        }

        if (playerManagerPtr == 0) {
            std::cout << "[-] PlayerManager pointer is null" << std::endl;
            return FALSE;
        }

        // PlayerManager + 0x570 should point to ClientPlayerArray
        QWORD clientPlayerArrayPtr = 0;
        if (!vmmManager->readMemory(targetPID, playerManagerPtr + 0x570, &clientPlayerArrayPtr, sizeof(QWORD))) {
            std::cout << "[-] Failed to read ClientPlayerArray pointer" << std::endl;
            return FALSE;
        }

        if (clientPlayerArrayPtr == 0) {
            std::cout << "[-] ClientPlayerArray pointer is null" << std::endl;
            return FALSE;
        }

        std::cout << "[+] BF2042 structure validation passed" << std::endl;
        std::cout << "    GameContext: 0x" << std::hex << gameContext << std::endl;
        std::cout << "    PlayerManager: 0x" << std::hex << playerManagerPtr << std::endl;
        std::cout << "    ClientPlayerArray: 0x" << std::hex << clientPlayerArrayPtr << std::endl;

        // Cache these validated addresses
        cachedAddresses["PlayerManager"] = playerManagerPtr;
        cachedAddresses["ClientPlayerArray"] = clientPlayerArrayPtr;

        return TRUE;
    }
}
