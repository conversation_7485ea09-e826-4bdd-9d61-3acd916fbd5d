#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"

namespace BF2042 {
    // Bone indices from forum data (sycore confirmed)
    enum BoneIndex : uint32_t {
        BONE_HEAD = 104,
        BONE_NECK = 142,
        BONE_SPINE2 = 7,
        BONE_SPINE1 = 6,
        BONE_SPINE = 5,
        BONE_LEFTSHOULDER = 9,
        BONE_LEFTARM = 10,
        BONE_LEFTFOREARM = 11,
        BONE_LEFTHAND = 12,
        BONE_RIGHTSHOULDER = 109,
        BONE_RIGHTARM = 110,
        BONE_RIGHTFOREARM = 111,
        BONE_RIGHTHAND = 112,
        BONE_LEFTUPLEG = 184,
        BONE_LEFTLEG = 185,
        BONE_LEFTFOOT = 186,
        BONE_RIGHTUPLEG = 197,
        BONE_RIGHTLEG = 198,
        BONE_RIGHTFOOT = 199
    };

    struct BoneCollisionData : public DMAMem::MemoryObject {
        // Bone collision data structure
        // Exact structure varies, but typically contains bone matrices/positions
        
        BoneCollisionData() {
            // Forum data suggests bone data is accessed through this structure
            // Exact offsets may vary per game update
        }
        
        bool isValid() const {
            // Basic validation - can be enhanced based on actual bone data
            return true;
        }
    };

    class BoneCollisionComponent : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<BoneCollisionData> BoneData;
        
        BoneCollisionComponent() {
            BoneData = std::shared_ptr<BoneCollisionData>(new BoneCollisionData());
            
            // BF2042 BoneCollisionComponent offsets - Forum analysis
            // Some sources say 0x20, others say 0x48 - using 0x20 as primary
            this->registerPointer(0x20, BoneData.get());  // BoneCollisionData pointer
        }
        
        // Helper methods
        bool isValid() const {
            return BoneData && BoneData->isValid();
        }
        
        // Bone position calculation (would need actual bone matrix data)
        DMARender::Vector3 getBonePosition(BoneIndex boneIndex) const {
            // This would require reading bone matrices from memory
            // Implementation depends on actual bone data structure
            // For now, return zero vector as placeholder
            return DMARender::Vector3{0.0f, 0.0f, 0.0f};
        }
        
        // Check if specific bones are available
        bool hasHeadBone() const {
            return isValid();
        }
        
        bool hasChestBone() const {
            return isValid();
        }
        
        bool hasLimbBones() const {
            return isValid();
        }
    };
}
