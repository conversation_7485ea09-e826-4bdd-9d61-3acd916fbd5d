#include "ClientPlayer.h"
#include "ClientSoldierEntity.h"  // Include here to avoid circular reference

namespace BF2042 {
    ClientPlayer::ClientPlayer() {
        SoldierEntity = std::shared_ptr<ClientSoldierEntity>(new ClientSoldierEntity());
        VehicleEntity = std::shared_ptr<ClientVehicleEntity>(new ClientVehicleEntity());

        // BF2042 ClientPlayer offsets from UnknownCheats forum (2025 Update)
        this->registerOffset(0x20, &PlayerNamePtr, sizeof(QWORD)); // m_Name pointer (forum: 0x20)
        this->registerOffset(0x60, &TeamId, sizeof(uint32_t)); // m_teamId (forum: 0x60)
        this->registerPointer(0xC8, SoldierEntity.get());      // m_ClientSoldierEntity (Forum confirmed: 0xC8)
        this->registerPointer(0xB8, VehicleEntity.get());      // m_ClientVehicleEntity (forum: 0xB8)
    }

    bool ClientPlayer::isValid() const {
        // Accept players that have a valid soldier entity OR a non-zero name pointer
        bool hasName = PlayerNamePtr != 0;
        bool soldierOk = SoldierEntity && SoldierEntity->isValid();
        return hasName || soldierOk;
    }

    std::string ClientPlayer::getName() const {
        if (PlayerNamePtr != 0) {
            // TODO: Implement BF2042-specific string reading when needed
            return "Player";
        }
        return "";
    }

    bool ClientPlayer::isEnemy(uint32_t localTeamId) const {
        return TeamId != localTeamId && TeamId != 0;
    }

    bool ClientPlayer::isInVehicle() const {
        return VehicleEntity && VehicleEntity->isValid();
    }
}
