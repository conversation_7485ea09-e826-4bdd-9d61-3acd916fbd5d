#pragma once
#include "DMAMemoryManagement/includes.h"
#include "GameRenderer.h"

namespace BF2042 {
    struct NetworkManager : public DMAMem::MemoryObject {
        std::shared_ptr<GameRenderer> GameRendererPtr;

        NetworkManager() {
            GameRendererPtr = std::shared_ptr<GameRenderer>(new GameRenderer());
            // For BF2042, we're using the NetworkManager address to access GameRenderer
            this->registerPointer(0x0, GameRendererPtr.get());
        }
        
        // Helper methods
        bool isValid() const {
            return GameRendererPtr && GameRendererPtr->isValid();
        }
        
        std::shared_ptr<GameRenderer> getGameRenderer() const {
            return GameRendererPtr;
        }
    };
}
