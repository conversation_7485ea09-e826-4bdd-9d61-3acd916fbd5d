#pragma once
#include "Entity.h"
#include <fstream>
#include <iostream>

std::set<std::string> DayZ::Entity::rareItems;

void DayZ::Entity::loadRareItems(const std::string& filePath) {
	rareItems.clear();
	std::ifstream file(filePath);
	if (!file.is_open()) {
		std::cerr << "Warning: File RareItems.txt" << filePath << " could not be opened. Rare items are not loading.\n";
		return;
	}

	std::string line;
	while (std::getline(file, line)) {
		if (!line.empty()) {
			rareItems.insert(line);
		}
	}
	file.close();

	if (rareItems.empty()) {
		std::cerr << "Warning: File RareItems.txt" << filePath << " contains no valid entries.\n";
	}
}

// DayZ Health Reading Implementation using Entity Quality offset
// Based on forum research - reads quality/health from entity + 0x194
bool DayZ::Entity::ReadHealthValues() {
	// Always check isDead status first
	if (this->isDead) {
		this->health = 0.0f;
		this->blood = 0.0f;
		this->shock = 0.0f;
		return true;
	}

	// Try to read entity quality/health from memory offset 0x194
	try {
		// This requires access to VMM and PID - we'll need to pass these in
		// For now, implement the quality reading logic structure

		// Read quality value from entity + 0x194
		// Quality values: 0=Pristine(100%), 1=Worn(75%), 2=Damaged(50%), 3=Badly Damaged(25%), 4=Ruined(0%)
		int qualityValue = 0; // This will be read from memory

		// Convert quality to health percentage using forum code logic
		float healthPercent = 100.0f; // Default to pristine
		switch (qualityValue) {
			case 1: healthPercent = 75.0f; break;  // Worn
			case 2: healthPercent = 50.0f; break;  // Damaged
			case 3: healthPercent = 25.0f; break;  // Badly Damaged
			case 4: healthPercent = 0.0f; break;   // Ruined
			default: healthPercent = 100.0f; break; // Pristine or unknown
		}

		this->health = healthPercent;

		// Calculate blood and shock based on health percentage
		// Blood scales from 0-5000, shock stays at 100 unless severely damaged
		this->blood = (healthPercent / 100.0f) * 5000.0f;
		this->shock = (healthPercent > 25.0f) ? 100.0f : (healthPercent * 4.0f); // Shock drops when badly damaged

		return true;
	}
	catch (...) {
		// Fallback to default values if reading fails
		this->health = 100.0f;
		this->blood = 5000.0f;
		this->shock = 100.0f;
		return false;
	}
}

// Enhanced health reading with actual memory access
bool DayZ::Entity::ReadHealthValues(DMAMem::VmmManager* vmm, DWORD pid) {
	if (!vmm || !pid || !this->_remoteAddress) {
		return ReadHealthValues(); // Fallback to basic version
	}

	// Always check isDead status first
	if (this->isDead) {
		this->health = 0.0f;
		this->blood = 0.0f;
		this->shock = 0.0f;
		return true;
	}

	try {
		// Read quality value from entity + 0x194 (forum code offset)
		int qualityValue = 0;
		if (!vmm->readMemory(pid, this->_remoteAddress + 0x194, &qualityValue, sizeof(int), VMMDLL_FLAG_NOCACHE)) {
			return ReadHealthValues(); // Fallback if read fails
		}

		// Convert quality to health percentage using forum code logic
		float healthPercent = GetQualityPercentage(qualityValue);

		this->health = healthPercent;

		// Calculate blood and shock based on health percentage
		this->blood = (healthPercent / 100.0f) * 5000.0f;
		this->shock = (healthPercent > 25.0f) ? 100.0f : (healthPercent * 4.0f);

		return true;
	}
	catch (...) {
		return ReadHealthValues(); // Fallback to basic version
	}
}



// Helper function to get quality as string
std::string DayZ::Entity::GetQualityString(int qualityValue) {
	switch (qualityValue) {
		case 1: return "Worn";
		case 2: return "Damaged";
		case 3: return "Badly Damaged";
		case 4: return "Ruined";
		default: return "Pristine";
	}
}

// Helper function to get quality as percentage
float DayZ::Entity::GetQualityPercentage(int qualityValue) {
	switch (qualityValue) {
		case 1: return 75.0f;   // Worn
		case 2: return 50.0f;   // Damaged
		case 3: return 25.0f;   // Badly Damaged
		case 4: return 0.0f;    // Ruined
		default: return 100.0f; // Pristine
	}
}

float DayZ::Entity::ReadHealthType(const std::string& healthType) {
	// For now, return the current health values
	// Real implementation would require server-specific memory reading

	if (healthType == "Blood") {
		return this->blood;
	}
	else if (healthType == "Shock") {
		return this->shock;
	}
	else {
		return this->health; // Default to Health
	}
}

bool DayZ::Entity::IsHealthDataValid() {
	// Check if health values seem reasonable
	return (health >= 0.0f && health <= 100.0f &&
	        blood >= 0.0f && blood <= 5000.0f &&
	        shock >= 0.0f && shock <= 100.0f);
}

