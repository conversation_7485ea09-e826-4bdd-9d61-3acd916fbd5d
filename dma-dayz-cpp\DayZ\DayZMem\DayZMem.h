#pragma once
#include "DMAMemoryManagement/includes.h";
#define EXE_NAME "bf6event.exe"
#include "../Structs/NetworkManager.h";
#include "../Structs/WorldPointer.h";
#include <future>
#include <mutex>

namespace BF6 {
	class Mem {
		DMAMem::VmmManager* vmmManager = NULL;
		DMAMem::StaticManager staticManager = NULL;
		QWORD worldAddress;
		QWORD networkManagerAddress;
		void fetchBaseAddresses();

	public:
		Mem(DMAMem::VmmManager* vmmManager);
		DMAMem::VmmManager* getVMM();
		DWORD getPid();
		WorldPointer getWorld();
		NetworkManager getNetworkManager();
		std::vector<std::shared_ptr<BF6::Entity>> getAllUniqueEntities();
		void processESP();
	};
};