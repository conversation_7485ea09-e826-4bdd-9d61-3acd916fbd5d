#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"
#include "HealthComponent.h"
#include "WeaponsComponent.h"
#include "SpottingTargetComponent.h"
#include "BoneCollisionComponent.h"
#include "MeshOverlayComponent.h"

namespace BF2042 {
    // Forward declaration to avoid circular reference
    class ClientPlayer;
    
    class ClientSoldierEntity : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<ClientPlayer> Player;
        std::shared_ptr<HealthComponent> Health;
        std::shared_ptr<WeaponsComponent> Weapons;
        std::shared_ptr<SpottingTargetComponent> SpottingTarget;
        std::shared_ptr<BoneCollisionComponent> BoneCollision;
        std::shared_ptr<MeshOverlayComponent> MeshOverlay;
        DMARender::Vector3 Position;
        
        ClientSoldierEntity() {
            // Note: ClientPlayer creates circular reference, handle carefully
            Health = std::shared_ptr<HealthComponent>(new HealthComponent());
            Weapons = std::shared_ptr<WeaponsComponent>(new WeaponsComponent());
            SpottingTarget = std::shared_ptr<SpottingTargetComponent>(new SpottingTargetComponent());
            BoneCollision = std::shared_ptr<BoneCollisionComponent>(new BoneCollisionComponent());
            MeshOverlay = std::shared_ptr<MeshOverlayComponent>(new MeshOverlayComponent());

            // BF2042 ClientSoldierEntity offsets - Forum confirmed (sycore/wasd2 2025)
            this->registerPointer(0x00E8, nullptr); // m_ClientPlayer - Forum confirmed: 0xE8
            this->registerPointer(0xE00, Health.get());                    // m_HealthComponent - Forum confirmed: 0xE00
            this->registerPointer(0x1570, Weapons.get());                  // m_WeaponsComponent - Forum confirmed: 0x1570
            this->registerPointer(0x1BC0, BoneCollision.get());            // m_BoneCollisionComponent - Forum: 0x1BC0
            this->registerPointer(0x1E00, SpottingTarget.get());           // m_SpottingTargetComponent - Forum: 0x1E00
            this->registerPointer(0x1EA0, MeshOverlay.get());              // m_MeshOverlayComponent - Forum: 0x1EA0
            this->registerOffset(0x20E0, &Position, sizeof(DMARender::Vector3)); // m_Position - Forum confirmed: 0x20E0
        }
        
        // Helper methods
        bool isValid() const {
            return Health && Position.x != 0.0f && Position.y != 0.0f;
        }
        
        bool isAlive() const {
            return Health && Health->isAlive();
        }
        
        float getHealthPercentage() const {
            if (Health) {
                return Health->getHealthPercentage();
            }
            return 0.0f;
        }
        
        DMARender::Vector3 getPosition() const {
            return Position;
        }
        
        float getDistanceTo(const DMARender::Vector3& otherPos) const {
            float dx = Position.x - otherPos.x;
            float dy = Position.y - otherPos.y;
            float dz = Position.z - otherPos.z;
            return sqrt(dx*dx + dy*dy + dz*dz);
        }

        // Visibility and spotting methods
        bool isVisible() const {
            return SpottingTarget && SpottingTarget->isPlayerVisible();
        }

        bool isOccluded() const {
            return SpottingTarget && SpottingTarget->isPlayerOccluded();
        }

        bool isSpottedByPAC() const {
            return SpottingTarget && SpottingTarget->isSpottedByPAC();
        }

        bool isPACScanned() const {
            return MeshOverlay && MeshOverlay->isPACScanned();
        }

        bool shouldHighlight() const {
            return MeshOverlay && MeshOverlay->shouldHighlight();
        }

        // Bone system methods
        bool hasBoneData() const {
            return BoneCollision && BoneCollision->isValid();
        }

        DMARender::Vector3 getBonePosition(BoneIndex boneIndex) const {
            if (BoneCollision) {
                return BoneCollision->getBonePosition(boneIndex);
            }
            return Position; // Fallback to entity position
        }
    };
}
