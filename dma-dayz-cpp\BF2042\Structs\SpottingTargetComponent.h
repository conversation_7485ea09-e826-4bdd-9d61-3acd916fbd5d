#pragma once
#include "DMAMemoryManagement/includes.h"

namespace BF2042 {
    // Spotting types from forum analysis
    enum SpotType : uint32_t {
        Unspotted = 0x00,
        PassiveRadar = 0x01,
        Drone = 0x06,
        Proximity = 0x08,
        PAC = 0x12,        // Paik chams scan
        Ping = 0x20
    };

    // Occlusion types for visibility detection
    enum OcclusionType : uint8_t {
        Occluded = 0x20,   // Player is behind cover/wall
        Visible = 0x30     // Player is visible
    };

    struct SpottingTargetData : public DMAMem::MemoryObject {
        uint8_t occludedType;  // 0xB9 offset
        
        SpottingTargetData() {
            this->registerOffset(0xB9, &occludedType, sizeof(uint8_t));
        }
        
        bool isVisible() const {
            return occludedType == static_cast<uint8_t>(OcclusionType::Visible);
        }
        
        bool isOccluded() const {
            return occludedType == static_cast<uint8_t>(OcclusionType::Occluded);
        }
    };

    class SpottingTargetComponent : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<SpottingTargetData> SpottingData;
        uint32_t activeSpotType;      // 0x164
        uint32_t someSpotRelated;     // 0x1AC
        
        SpottingTargetComponent() {
            SpottingData = std::shared_ptr<SpottingTargetData>(new SpottingTargetData());
            
            // BF2042 SpottingTargetComponent offsets - Forum confirmed
            this->registerPointer(0x18, SpottingData.get());           // SpottingTargetData pointer
            this->registerOffset(0x164, &activeSpotType, sizeof(uint32_t));  // Active spot type
            this->registerOffset(0x1AC, &someSpotRelated, sizeof(uint32_t)); // Additional spot data
        }
        
        // Helper methods
        bool isValid() const {
            return SpottingData != nullptr;
        }
        
        bool isPlayerVisible() const {
            return SpottingData && SpottingData->isVisible();
        }
        
        bool isPlayerOccluded() const {
            return SpottingData && SpottingData->isOccluded();
        }
        
        SpotType getActiveSpotType() const {
            return static_cast<SpotType>(activeSpotType);
        }
        
        bool isSpottedByPAC() const {
            return getActiveSpotType() == SpotType::PAC;
        }
        
        bool isSpottedByDrone() const {
            return getActiveSpotType() == SpotType::Drone;
        }
        
        bool isSpottedByPing() const {
            return getActiveSpotType() == SpotType::Ping;
        }
        
        bool isAnySpotActive() const {
            return activeSpotType != static_cast<uint32_t>(SpotType::Unspotted);
        }
    };
}
