#pragma once
#include "DMAMemoryManagement/includes.h"

namespace BF6 {
    // PAC scan states from forum analysis
    enum PACState : int32_t {
        NotScanned = 0x0,
        Scanning = 0x1,
        ActivelyScanned = 0x2  // Player is highlighted by <PERSON><PERSON>'s scan
    };

    class MeshOverlayComponent : public DMAMem::MemoryObject {
    public:
        int32_t activePACScan;  // 0x50 offset - indicates if player is highlighted by PAC scan
        
        MeshOverlayComponent() {
            // BF2042 MeshOverlayComponent offsets - Forum confirmed
            this->registerOffset(0x50, &activePACScan, sizeof(int32_t));  // PAC scan detection
        }
        
        // Helper methods
        bool isValid() const {
            return true;  // Component is always valid if accessible
        }
        
        bool isPACScanned() const {
            return activePACScan == static_cast<int32_t>(PACState::ActivelyScanned);
        }
        
        bool isPACScanning() const {
            return activePACScan == static_cast<int32_t>(PACState::Scanning);
        }
        
        bool isNotPACScanned() const {
            return activePACScan == static_cast<int32_t>(PACState::NotScanned);
        }
        
        PACState getPACState() const {
            return static_cast<PACState>(activePACScan);
        }
        
        // Check if player should be rendered with chams/highlight
        bool shouldHighlight() const {
            return isPACScanned() || isPACScanning();
        }
    };
}
