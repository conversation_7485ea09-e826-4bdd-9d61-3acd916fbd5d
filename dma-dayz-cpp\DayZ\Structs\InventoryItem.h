#pragma once
#include "DMAMemoryManagement/includes.h"
#include "FutureVisualState.h"
#include "EntityType.h"
#include "Scoreboard.h"
#include "ScoreboardIdentity.h"
#include "EntityFilterList.h"


namespace DayZ {


	class InventoryItem : public DMAMem::MemoryObject {

		bool _isValid = false;
		bool _isValidChecked = false;

	public:

		std::shared_ptr<EntityType> EntityTypePtr;


		InventoryItem() {
			EntityTypePtr = std::shared_ptr<EntityType>(new EntityType());
			this->registerPointer(0x180, EntityTypePtr.get());
		}

		bool isValid() {

			if (!_isValidChecked) {
				// Validate entity type strings FIRST to prevent null pointer access
				if (!EntityTypePtr || !EntityTypePtr->CleanName || !EntityTypePtr->CleanName->value) {
					_isValid = false;
					_isValidChecked = true;
					return _isValid;
				}

				// Check for blacklisted items using CleanName only (DayZ mod style)
				if (this->EntityTypePtr->CleanName && this->EntityTypePtr->CleanName->value) {
					std::string entityName = this->EntityTypePtr->CleanName->value;
					if (itemBlacklistSet.find(entityName) != itemBlacklistSet.end()) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}

				// Validate CleanName only (no fallbacks)
				_isValid = this->EntityTypePtr->CleanName->length < 400 && this->EntityTypePtr->CleanName->length > 0;
				_isValidChecked = true;
			}
			return _isValid;
		}

	};
}