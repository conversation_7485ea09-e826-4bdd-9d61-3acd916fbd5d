#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"
#include <cmath>

namespace BF6 {
    class ViewMatrix : public DMAMem::MemoryObject {
    public:
        float ViewProjectionMatrix[16];
        
        ViewMatrix() {
            // BF2042 ViewMatrix offsets - Forum confirmed
            // ViewProjection matrix at RenderView + 0x480
            this->registerOffset(0x480, &ViewProjectionMatrix, sizeof(float) * 16);
        }
        
        // Helper methods
        bool isValid() const {
            // Check if matrix has reasonable values (not all zeros)
            for (int i = 0; i < 16; ++i) {
                if (ViewProjectionMatrix[i] != 0.0f) {
                    return true;
                }
            }
            return false;
        }
        
        const float* getViewProjectionMatrix() const {
            return ViewProjectionMatrix;
        }
        
        // World-to-screen conversion using the view-projection matrix
        bool worldToScreen(const DMARender::Vector3& worldPos, DMARender::Vector2& screenPos, 
                          int screenWidth, int screenHeight) const {
            if (!isValid()) {
                return false;
            }
            
            // Transform world position to clip space
            float clipX = ViewProjectionMatrix[0] * worldPos.x + ViewProjectionMatrix[4] * worldPos.y + 
                         ViewProjectionMatrix[8] * worldPos.z + ViewProjectionMatrix[12];
            float clipY = ViewProjectionMatrix[1] * worldPos.x + ViewProjectionMatrix[5] * worldPos.y + 
                         ViewProjectionMatrix[9] * worldPos.z + ViewProjectionMatrix[13];
            float clipW = ViewProjectionMatrix[3] * worldPos.x + ViewProjectionMatrix[7] * worldPos.y + 
                         ViewProjectionMatrix[11] * worldPos.z + ViewProjectionMatrix[15];
            
            // Check if point is behind camera
            if (clipW <= 0.0f) {
                return false;
            }
            
            // Perspective divide
            float ndcX = clipX / clipW;
            float ndcY = clipY / clipW;
            
            // Check if point is within screen bounds (NDC space is -1 to 1)
            if (ndcX < -1.0f || ndcX > 1.0f || ndcY < -1.0f || ndcY > 1.0f) {
                return false;
            }
            
            // Convert to screen coordinates
            screenPos.x = (ndcX + 1.0f) * 0.5f * screenWidth;
            screenPos.y = (1.0f - ndcY) * 0.5f * screenHeight; // Flip Y axis
            
            return true;
        }
        
        // Alternative W2S method for compatibility
        bool worldToScreenAlt(const DMARender::Vector3& worldPos, DMARender::Vector2& screenPos, 
                             int screenWidth, int screenHeight) const {
            if (!isValid()) {
                return false;
            }
            
            // Matrix multiplication: [x, y, z, 1] * ViewProjectionMatrix
            float x = worldPos.x;
            float y = worldPos.y;
            float z = worldPos.z;
            float w = 1.0f;
            
            float resultX = x * ViewProjectionMatrix[0] + y * ViewProjectionMatrix[1] + 
                           z * ViewProjectionMatrix[2] + w * ViewProjectionMatrix[3];
            float resultY = x * ViewProjectionMatrix[4] + y * ViewProjectionMatrix[5] + 
                           z * ViewProjectionMatrix[6] + w * ViewProjectionMatrix[7];
            float resultW = x * ViewProjectionMatrix[12] + y * ViewProjectionMatrix[13] + 
                           z * ViewProjectionMatrix[14] + w * ViewProjectionMatrix[15];
            
            if (resultW <= 0.001f) {
                return false;
            }
            
            float invW = 1.0f / resultW;
            resultX *= invW;
            resultY *= invW;
            
            screenPos.x = (screenWidth / 2.0f) + (resultX * screenWidth / 2.0f);
            screenPos.y = (screenHeight / 2.0f) - (resultY * screenHeight / 2.0f);
            
            return (screenPos.x >= 0 && screenPos.x <= screenWidth && 
                   screenPos.y >= 0 && screenPos.y <= screenHeight);
        }
        
        // Get distance from camera to world position
        float getDistanceToCamera(const DMARender::Vector3& worldPos) const {
            // Extract camera position from view matrix (inverse of view matrix translation)
            float camX = -(ViewProjectionMatrix[12] * ViewProjectionMatrix[0] + 
                          ViewProjectionMatrix[13] * ViewProjectionMatrix[1] + 
                          ViewProjectionMatrix[14] * ViewProjectionMatrix[2]);
            float camY = -(ViewProjectionMatrix[12] * ViewProjectionMatrix[4] + 
                          ViewProjectionMatrix[13] * ViewProjectionMatrix[5] + 
                          ViewProjectionMatrix[14] * ViewProjectionMatrix[6]);
            float camZ = -(ViewProjectionMatrix[12] * ViewProjectionMatrix[8] + 
                          ViewProjectionMatrix[13] * ViewProjectionMatrix[9] + 
                          ViewProjectionMatrix[14] * ViewProjectionMatrix[10]);
            
            float dx = worldPos.x - camX;
            float dy = worldPos.y - camY;
            float dz = worldPos.z - camZ;
            
            return sqrt(dx*dx + dy*dy + dz*dz);
        }
    };
}
