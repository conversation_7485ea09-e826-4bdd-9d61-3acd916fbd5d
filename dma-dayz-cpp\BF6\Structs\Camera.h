#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"
#include "ViewMatrix.h"

namespace BF6 {
    // Compatibility Camera structure that wraps ViewMatrix for BF6
    // This maintains compatibility with existing camera-based code
    struct Camera : public DMAMem::MemoryObject {
        std::shared_ptr<ViewMatrix> viewMatrix;
        DMARender::Vector2 ViewPortSize;
        
        Camera() {
            viewMatrix = std::shared_ptr<ViewMatrix>(new ViewMatrix());
            
            // Register ViewMatrix at offset 0 for direct access
            this->registerPointer(0x0, viewMatrix.get());
            
            // Default viewport size (will be updated from actual screen dimensions)
            ViewPortSize.x = 1920.0f;
            ViewPortSize.y = 1080.0f;
        }
        
        // Compatibility methods for existing camera code
        bool isValid() const {
            return viewMatrix && viewMatrix->isValid();
        }
        
        // World-to-screen conversion (compatibility method)
        bool worldToScreen(const DMARender::Vector3& worldPos, DMARender::Vector2& screenPos) const {
            if (!viewMatrix) {
                return false;
            }
            
            return viewMatrix->worldToScreen(worldPos, screenPos, 
                                           static_cast<int>(ViewPortSize.x), 
                                           static_cast<int>(ViewPortSize.y));
        }
        
        // World-to-screen with custom screen dimensions
        bool worldToScreen(const DMARender::Vector3& worldPos, DMARender::Vector2& screenPos, 
                          int screenWidth, int screenHeight) const {
            if (!viewMatrix) {
                return false;
            }
            
            return viewMatrix->worldToScreen(worldPos, screenPos, screenWidth, screenHeight);
        }
        
        // Get distance to camera
        float getDistanceToCamera(const DMARender::Vector3& worldPos) const {
            if (!viewMatrix) {
                return 0.0f;
            }
            
            return viewMatrix->getDistanceToCamera(worldPos);
        }
        
        // Update viewport size
        void setViewportSize(float width, float height) {
            ViewPortSize.x = width;
            ViewPortSize.y = height;
        }
        
        // Get view projection matrix
        const float* getViewProjectionMatrix() const {
            if (viewMatrix) {
                return viewMatrix->getViewProjectionMatrix();
            }
            return nullptr;
        }
        
        // Legacy compatibility methods (for DayZ code that expects these)
        DMARender::Vector3 getPosition() const {
            // Extract camera position from view matrix if needed
            // For now, return zero vector as placeholder
            return DMARender::Vector3{0.0f, 0.0f, 0.0f};
        }
        
        DMARender::Vector3 getForward() const {
            // Extract camera forward vector from view matrix if needed
            // For now, return default forward vector
            return DMARender::Vector3{0.0f, 0.0f, 1.0f};
        }
    };
}
