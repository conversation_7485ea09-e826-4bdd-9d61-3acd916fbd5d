#include "SDFFont.h"
#include <d3dcompiler.h>
#include <fstream>
#include <algorithm>
#include <cmath>
#include <iostream>

// Undefine Windows min/max macros that conflict with std::min/std::max
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "d3dcompiler.lib")

using namespace DirectX;

namespace DMARender {

    // SDF Vertex Shader HLSL
    const char* SDF_VERTEX_SHADER = R"(
        cbuffer SDFConstants : register(b0)
        {
            matrix projection;
            float4 textColor;
            float4 outlineColor;
            float outlineThickness;
            float shadowOffset;
            float glowRadius;
            float distanceRange;
            float2 screenSize;
            float2 padding;
        };

        struct VSInput
        {
            float2 pos : POSITION;
            float2 uv : TEXCOORD0;
            float4 col : COLOR0;
        };

        struct PSInput
        {
            float4 pos : SV_POSITION;
            float2 uv : TEXCOORD0;
            float4 col : COLOR0;
        };

        PSInput main(VSInput input)
        {
            PSInput output;
            // DirectX convention: vector * matrix (not matrix * vector)
            output.pos = mul(float4(input.pos, 0.0f, 1.0f), projection);
            output.uv = input.uv;
            output.col = input.col;
            return output;
        }
    )";

    // SDF Pixel Shader HLSL with advanced effects
    const char* SDF_PIXEL_SHADER = R"(
        cbuffer SDFConstants : register(b0)
        {
            matrix projection;
            float4 textColor;
            float4 outlineColor;
            float outlineThickness;
            float shadowOffset;
            float glowRadius;
            float distanceRange;
            float2 screenSize;
            float2 padding;
        };

        Texture2D fontTexture : register(t0);
        SamplerState fontSampler : register(s0);

        struct PSInput
        {
            float4 pos : SV_POSITION;
            float2 uv : TEXCOORD0;
            float4 col : COLOR0;
        };

        float4 main(PSInput input) : SV_TARGET
        {
            // Sample the SDF texture
            float distance = fontTexture.Sample(fontSampler, input.uv).r;
            
            // Convert from [0,1] to signed distance
            distance = (distance - 0.5f) * distanceRange;
            
            // Calculate derivatives for smooth antialiasing
            float2 ddist = float2(ddx(distance), ddy(distance));
            float pixelDist = length(ddist);
            float edgeWidth = 0.7f * pixelDist; // Smooth edge width
            
            // Base text rendering
            float textAlpha = smoothstep(-edgeWidth, edgeWidth, distance);
            float4 finalColor = input.col * textColor;
            finalColor.a *= textAlpha;

            // Declare outlineAlpha outside the outline block so it can be used in glow effect
            float outlineAlpha = 0.0f;

            // Outline effect
            if (outlineThickness > 0.0f)
            {
                outlineAlpha = smoothstep(-outlineThickness - edgeWidth,
                                        -outlineThickness + edgeWidth, distance);
                float4 outlineColorFinal = outlineColor;
                outlineColorFinal.a *= outlineAlpha;

                // Blend outline with text
                finalColor = lerp(outlineColorFinal, finalColor, textAlpha);
            }

            // Glow effect
            if (glowRadius > 0.0f)
            {
                float glowAlpha = smoothstep(-glowRadius - edgeWidth,
                                           -glowRadius + edgeWidth, distance);
                float4 glowColor = textColor;
                glowColor.a *= glowAlpha * 0.5f; // Softer glow

                // Blend glow
                finalColor = lerp(glowColor, finalColor, max(textAlpha, outlineThickness > 0.0f ? outlineAlpha : 0.0f));
            }
            
            // Apply overall alpha
            finalColor.a *= input.col.a;
            
            return finalColor;
        }
    )";

    SDFFontRenderer::~SDFFontRenderer() {
        Shutdown();
    }

    bool SDFFontRenderer::Initialize(ID3D11Device* device, ID3D11DeviceContext* context)
    {
        if (!device || !context) return false;
        
        d3dDevice = device;
        d3dContext = context;
        
        // Create shaders
        if (!CreateShaders()) {
            std::cerr << "[SDF] Failed to create shaders" << std::endl;
            return false;
        }
        
        // Create buffers and states
        if (!CreateBuffersAndStates()) {
            std::cerr << "[SDF] Failed to create buffers and states" << std::endl;
            return false;
        }
        
        initialized = true;
        std::cout << "[SDF] Font renderer initialized successfully" << std::endl;
        return true;
    }

    void SDFFontRenderer::Shutdown()
    {
        // Clean up DirectX resources
        if (samplerState) { samplerState->Release(); samplerState = nullptr; }
        if (rasterizerState) { rasterizerState->Release(); rasterizerState = nullptr; }
        if (blendState) { blendState->Release(); blendState = nullptr; }
        if (depthStencilState) { depthStencilState->Release(); depthStencilState = nullptr; }
        if (indexBuffer) { indexBuffer->Release(); indexBuffer = nullptr; }
        if (vertexBuffer) { vertexBuffer->Release(); vertexBuffer = nullptr; }
        if (inputLayout) { inputLayout->Release(); inputLayout = nullptr; }
        if (constantBuffer) { constantBuffer->Release(); constantBuffer = nullptr; }
        if (pixelShader) { pixelShader->Release(); pixelShader = nullptr; }
        if (vertexShader) { vertexShader->Release(); vertexShader = nullptr; }
        
        // Clear font atlases
        fontAtlases.clear();
        
        d3dDevice = nullptr;
        d3dContext = nullptr;
        initialized = false;
        
        std::cout << "[SDF] Font renderer shutdown" << std::endl;
    }

    bool SDFFontRenderer::CreateShaders()
    {
        HRESULT hr;
        ID3DBlob* vsBlob = nullptr;
        ID3DBlob* psBlob = nullptr;
        ID3DBlob* errorBlob = nullptr;

        // Compile vertex shader
        hr = D3DCompile(SDF_VERTEX_SHADER, strlen(SDF_VERTEX_SHADER), nullptr, nullptr, nullptr,
                       "main", "vs_4_0", 0, 0, &vsBlob, &errorBlob);
        
        if (FAILED(hr)) {
            if (errorBlob) {
                std::cerr << "[SDF] Vertex shader compilation error: " << (char*)errorBlob->GetBufferPointer() << std::endl;
                errorBlob->Release();
            }
            return false;
        }

        // Create vertex shader
        hr = d3dDevice->CreateVertexShader(vsBlob->GetBufferPointer(), vsBlob->GetBufferSize(), nullptr, &vertexShader);
        if (FAILED(hr)) {
            vsBlob->Release();
            return false;
        }

        // Create input layout
        D3D11_INPUT_ELEMENT_DESC layout[] = {
            { "POSITION", 0, DXGI_FORMAT_R32G32_FLOAT, 0, 0, D3D11_INPUT_PER_VERTEX_DATA, 0 },
            { "TEXCOORD", 0, DXGI_FORMAT_R32G32_FLOAT, 0, 8, D3D11_INPUT_PER_VERTEX_DATA, 0 },
            { "COLOR", 0, DXGI_FORMAT_R32G32B32A32_FLOAT, 0, 16, D3D11_INPUT_PER_VERTEX_DATA, 0 }
        };

        hr = d3dDevice->CreateInputLayout(layout, 3, vsBlob->GetBufferPointer(), vsBlob->GetBufferSize(), &inputLayout);
        vsBlob->Release();
        if (FAILED(hr)) return false;

        // Compile pixel shader
        hr = D3DCompile(SDF_PIXEL_SHADER, strlen(SDF_PIXEL_SHADER), nullptr, nullptr, nullptr,
                       "main", "ps_4_0", 0, 0, &psBlob, &errorBlob);
        
        if (FAILED(hr)) {
            if (errorBlob) {
                std::cerr << "[SDF] Pixel shader compilation error: " << (char*)errorBlob->GetBufferPointer() << std::endl;
                errorBlob->Release();
            }
            return false;
        }

        // Create pixel shader
        hr = d3dDevice->CreatePixelShader(psBlob->GetBufferPointer(), psBlob->GetBufferSize(), nullptr, &pixelShader);
        psBlob->Release();
        if (FAILED(hr)) return false;

        return true;
    }

    bool SDFFontRenderer::CreateBuffersAndStates()
    {
        HRESULT hr;

        // Create constant buffer
        D3D11_BUFFER_DESC cbDesc = {};
        cbDesc.ByteWidth = sizeof(SDFConstants);
        cbDesc.Usage = D3D11_USAGE_DYNAMIC;
        cbDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
        cbDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

        hr = d3dDevice->CreateBuffer(&cbDesc, nullptr, &constantBuffer);
        if (FAILED(hr)) return false;

        // Create vertex buffer
        D3D11_BUFFER_DESC vbDesc = {};
        vbDesc.ByteWidth = sizeof(SDFVertex) * MAX_VERTICES;
        vbDesc.Usage = D3D11_USAGE_DYNAMIC;
        vbDesc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
        vbDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

        hr = d3dDevice->CreateBuffer(&vbDesc, nullptr, &vertexBuffer);
        if (FAILED(hr)) return false;

        // Create index buffer
        D3D11_BUFFER_DESC ibDesc = {};
        ibDesc.ByteWidth = sizeof(uint16_t) * MAX_INDICES;
        ibDesc.Usage = D3D11_USAGE_DYNAMIC;
        ibDesc.BindFlags = D3D11_BIND_INDEX_BUFFER;
        ibDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

        hr = d3dDevice->CreateBuffer(&ibDesc, nullptr, &indexBuffer);
        if (FAILED(hr)) return false;

        // Create blend state for alpha blending
        D3D11_BLEND_DESC blendDesc = {};
        blendDesc.RenderTarget[0].BlendEnable = TRUE;
        blendDesc.RenderTarget[0].SrcBlend = D3D11_BLEND_SRC_ALPHA;
        blendDesc.RenderTarget[0].DestBlend = D3D11_BLEND_INV_SRC_ALPHA;
        blendDesc.RenderTarget[0].BlendOp = D3D11_BLEND_OP_ADD;
        blendDesc.RenderTarget[0].SrcBlendAlpha = D3D11_BLEND_ONE;
        blendDesc.RenderTarget[0].DestBlendAlpha = D3D11_BLEND_INV_SRC_ALPHA;
        blendDesc.RenderTarget[0].BlendOpAlpha = D3D11_BLEND_OP_ADD;
        blendDesc.RenderTarget[0].RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;

        hr = d3dDevice->CreateBlendState(&blendDesc, &blendState);
        if (FAILED(hr)) return false;

        // Create rasterizer state
        D3D11_RASTERIZER_DESC rastDesc = {};
        rastDesc.FillMode = D3D11_FILL_SOLID;
        rastDesc.CullMode = D3D11_CULL_NONE;
        rastDesc.FrontCounterClockwise = FALSE;
        rastDesc.DepthBias = 0;
        rastDesc.DepthBiasClamp = 0.0f;
        rastDesc.SlopeScaledDepthBias = 0.0f;
        rastDesc.DepthClipEnable = TRUE;
        rastDesc.ScissorEnable = FALSE;
        rastDesc.MultisampleEnable = FALSE;
        rastDesc.AntialiasedLineEnable = FALSE;

        hr = d3dDevice->CreateRasterizerState(&rastDesc, &rasterizerState);
        if (FAILED(hr)) return false;

        // Create sampler state
        D3D11_SAMPLER_DESC sampDesc = {};
        sampDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
        sampDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
        sampDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
        sampDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
        sampDesc.MipLODBias = 0.0f;
        sampDesc.MaxAnisotropy = 1;
        sampDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
        sampDesc.BorderColor[0] = 0.0f;
        sampDesc.BorderColor[1] = 0.0f;
        sampDesc.BorderColor[2] = 0.0f;
        sampDesc.BorderColor[3] = 0.0f;
        sampDesc.MinLOD = 0.0f;
        sampDesc.MaxLOD = D3D11_FLOAT32_MAX;

        hr = d3dDevice->CreateSamplerState(&sampDesc, &samplerState);
        if (FAILED(hr)) return false;

        // Create depth stencil state (disable depth testing for overlay text)
        D3D11_DEPTH_STENCIL_DESC dsDesc = {};
        dsDesc.DepthEnable = FALSE;  // Disable depth testing for overlay text
        dsDesc.DepthWriteMask = D3D11_DEPTH_WRITE_MASK_ZERO;
        dsDesc.DepthFunc = D3D11_COMPARISON_ALWAYS;
        dsDesc.StencilEnable = FALSE;

        hr = d3dDevice->CreateDepthStencilState(&dsDesc, &depthStencilState);
        if (FAILED(hr)) return false;

        return true;
    }

    // Simple SDF generation from bitmap using distance transform
    void SDFFontRenderer::GenerateSDFFromBitmap(const unsigned char* bitmap, int width, int height, 
                                               unsigned char* sdfOutput, float distanceRange)
    {
        // This is a simplified SDF generation algorithm
        // For production use, consider using msdfgen library or more advanced algorithms
        
        std::vector<float> distances(width * height);
        const float maxDist = distanceRange;
        
        // Calculate distance for each pixel
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int idx = y * width + x;
                bool inside = bitmap[idx] > 128;
                
                float minDist = maxDist;
                
                // Search in a neighborhood for edge
                int searchRadius = (int)ceil(maxDist);
                for (int dy = -searchRadius; dy <= searchRadius; dy++) {
                    for (int dx = -searchRadius; dx <= searchRadius; dx++) {
                        int nx = x + dx;
                        int ny = y + dy;
                        
                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            int nidx = ny * width + nx;
                            bool nInside = bitmap[nidx] > 128;
                            
                            if (inside != nInside) {
                                float dist = sqrtf(dx * dx + dy * dy);
                                minDist = std::min(minDist, dist);
                            }
                        }
                    }
                }
                
                distances[idx] = inside ? minDist : -minDist;
            }
        }
        
        // Convert to [0,255] range
        for (int i = 0; i < width * height; i++) {
            float normalized = (distances[i] / maxDist + 1.0f) * 0.5f;
            normalized = std::max(0.0f, std::min(1.0f, normalized));
            sdfOutput[i] = (unsigned char)(normalized * 255);
        }
    }

    bool SDFFontRenderer::LoadFontFromImGui(float fontSize, 
                                          std::unordered_map<char, SDFGlyph>& glyphs, 
                                          std::vector<unsigned char>& atlasData, 
                                          int& atlasWidth, int& atlasHeight,
                                          float& lineHeight, float& ascender, float& descender) {
        
        // Get current ImGui font
        ImFont* font = ImGui::GetFont();
        if (!font) {
            std::cerr << "[SDF] No ImGui font available" << std::endl;
            return false;
        }

        // Get font atlas from ImGui
        ImFontAtlas* atlas = font->ContainerAtlas;
        if (!atlas) {
            std::cerr << "[SDF] No font atlas available" << std::endl;
            return false;
        }

        // Get atlas texture data
        unsigned char* atlasPixels;
        int atlasW, atlasH;
        atlas->GetTexDataAsAlpha8(&atlasPixels, &atlasW, &atlasH);

        // Extract font metrics
        lineHeight = font->FontSize;
        ascender = font->Ascent;
        descender = font->Descent;

        // Character range to process (printable ASCII)
        const int startChar = 32;
        const int endChar = 126;

        // Store glyph bitmaps and info for SDF atlas generation
        std::vector<std::pair<char, std::vector<unsigned char>>> glyphBitmaps;
        std::vector<std::pair<char, SimpleGlyphInfo>> glyphInfos;

        // Extract glyphs from ImGui font
        for (int c = startChar; c <= endChar; c++) {
            const ImFontGlyph* glyph = font->FindGlyph((ImWchar)c);
            if (!glyph) continue;

            // Calculate glyph dimensions
            int glyphWidth = (int)(glyph->X1 - glyph->X0);
            int glyphHeight = (int)(glyph->Y1 - glyph->Y0);
            
            if (glyphWidth <= 0 || glyphHeight <= 0) continue;

            // Extract glyph bitmap from atlas
            std::vector<unsigned char> bitmap(glyphWidth * glyphHeight);
            
            for (int y = 0; y < glyphHeight; y++) {
                for (int x = 0; x < glyphWidth; x++) {
                    int atlasX = (int)(glyph->U0 * atlasW) + x;
                    int atlasY = (int)(glyph->V0 * atlasH) + y;
                    
                    if (atlasX >= 0 && atlasX < atlasW && atlasY >= 0 && atlasY < atlasH) {
                        bitmap[y * glyphWidth + x] = atlasPixels[atlasY * atlasW + atlasX];
                    }
                }
            }

            glyphBitmaps.push_back({(char)c, bitmap});

            // Store glyph info
            SimpleGlyphInfo info;
            info.width = glyphWidth;
            info.height = glyphHeight;
            info.offsetX = (int)glyph->X0;
            info.offsetY = (int)glyph->Y0;
            info.advanceX = glyph->AdvanceX;

            glyphInfos.push_back({(char)c, info});
        }

        // Pack glyphs into SDF atlas
        PackGlyphsIntoAtlas(glyphBitmaps, glyphInfos, glyphs, atlasData, atlasWidth, atlasHeight);

        std::cout << "[SDF] Loaded font from ImGui with " << glyphs.size() << " glyphs, atlas size: " 
                  << atlasWidth << "x" << atlasHeight << std::endl;

        return true;
    }

    void SDFFontRenderer::PackGlyphsIntoAtlas(const std::vector<std::pair<char, std::vector<unsigned char>>>& glyphBitmaps,
                                            const std::vector<std::pair<char, SimpleGlyphInfo>>& glyphInfos,
                                            std::unordered_map<char, SDFGlyph>& glyphs,
                                            std::vector<unsigned char>& atlasData,
                                            int& atlasWidth, int& atlasHeight) {
        
        // Simple row-based packing algorithm
        atlasWidth = 1024;
        atlasHeight = 1024;
        atlasData.resize(atlasWidth * atlasHeight, 0);

        int currentX = 0;
        int currentY = 0;
        int rowHeight = 0;
        const int padding = 2;

        for (size_t i = 0; i < glyphBitmaps.size(); i++) {
            char c = glyphBitmaps[i].first;
            const auto& bitmap = glyphBitmaps[i].second;
            const auto& info = glyphInfos[i].second;

            int glyphWidth = info.width;
            int glyphHeight = info.height;

            // Check if we need to move to next row
            if (currentX + glyphWidth + padding > atlasWidth) {
                currentX = 0;
                currentY += rowHeight + padding;
                rowHeight = 0;
            }

            // Check if we have enough vertical space
            if (currentY + glyphHeight + padding > atlasHeight) {
                std::cerr << "[SDF] Atlas too small, some glyphs may be missing" << std::endl;
                break;
            }

            // Generate SDF for this glyph
            std::vector<unsigned char> sdfBitmap(glyphWidth * glyphHeight);
            GenerateSDFFromBitmap(bitmap.data(), glyphWidth, glyphHeight, sdfBitmap.data(), distanceRange);

            // Copy SDF bitmap to atlas
            for (int y = 0; y < glyphHeight; y++) {
                for (int x = 0; x < glyphWidth; x++) {
                    int srcIdx = y * glyphWidth + x;
                    int dstIdx = (currentY + y) * atlasWidth + (currentX + x);
                    if (dstIdx < atlasData.size()) {
                        atlasData[dstIdx] = sdfBitmap[srcIdx];
                    }
                }
            }

            // Store glyph info
            SDFGlyph glyph;
            glyph.character = c;
            glyph.x0 = (float)currentX / atlasWidth;
            glyph.y0 = (float)currentY / atlasHeight;
            glyph.x1 = (float)(currentX + glyphWidth) / atlasWidth;
            glyph.y1 = (float)(currentY + glyphHeight) / atlasHeight;
            glyph.xoff = info.offsetX;
            glyph.yoff = info.offsetY;
            glyph.xadvance = info.advanceX;
            glyph.width = glyphWidth;
            glyph.height = glyphHeight;
            glyph.bearingX = info.offsetX;
            glyph.bearingY = -info.offsetY;

            glyphs[c] = glyph;

            // Update position
            currentX += glyphWidth + padding;
            rowHeight = std::max(rowHeight, glyphHeight);
        }
    }

    bool SDFFontRenderer::CreateSDFFontAtlas(const std::string& fontPath, float fontSize, int atlasSize) {
        if (!initialized) {
            std::cerr << "[SDF] Renderer not initialized" << std::endl;
            return false;
        }

        std::cout << "[SDF DEBUG] Creating font atlas from ImGui font (fontSize: " << fontSize << ", atlasSize: " << atlasSize << ")" << std::endl;
        
        auto atlas = std::make_unique<SDFFontAtlas>();
        atlas->fontName = fontPath.empty() ? "ImGui Default" : fontPath;
        atlas->fontSize = fontSize;
        atlas->distanceRange = distanceRange;
        
        std::vector<unsigned char> atlasData;
        
        std::cout << "[SDF DEBUG] Calling LoadFontFromImGui..." << std::endl;
        if (!LoadFontFromImGui(fontSize, atlas->glyphs, atlasData,
                              atlas->atlasWidth, atlas->atlasHeight,
                              atlas->lineHeight, atlas->ascender, atlas->descender)) {
            std::cerr << "[SDF] Failed to load font from ImGui" << std::endl;
            return false;
        }
        std::cout << "[SDF DEBUG] LoadFontFromImGui completed successfully" << std::endl;

        // Create DirectX texture
        D3D11_TEXTURE2D_DESC texDesc = {};
        texDesc.Width = atlas->atlasWidth;
        texDesc.Height = atlas->atlasHeight;
        texDesc.MipLevels = 1;
        texDesc.ArraySize = 1;
        texDesc.Format = DXGI_FORMAT_R8_UNORM;
        texDesc.SampleDesc.Count = 1;
        texDesc.Usage = D3D11_USAGE_DEFAULT;
        texDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE;

        D3D11_SUBRESOURCE_DATA initData = {};
        initData.pSysMem = atlasData.data();
        initData.SysMemPitch = atlas->atlasWidth;

        HRESULT hr = d3dDevice->CreateTexture2D(&texDesc, &initData, &atlas->atlasTexture);
        if (FAILED(hr)) {
            std::cerr << "[SDF] Failed to create atlas texture" << std::endl;
            return false;
        }
        
        // Create shader resource view
        D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
        srvDesc.Format = texDesc.Format;
        srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
        srvDesc.Texture2D.MipLevels = 1;
        
        hr = d3dDevice->CreateShaderResourceView(atlas->atlasTexture, &srvDesc, &atlas->atlasSRV);
        if (FAILED(hr)) {
            std::cerr << "[SDF] Failed to create SRV" << std::endl;
            return false;
        }
        
        fontAtlases.push_back(std::move(atlas));
        
        std::cout << "[SDF] Font atlas created successfully with " << fontAtlases.back()->glyphs.size() << " glyphs" << std::endl;
        return true;
    }

    void SDFFontRenderer::AddGlyphToRenderBatch(char c, float x, float y, float scale, ImU32 color) {
        if (fontAtlases.empty() || currentFontIndex >= fontAtlases.size()) return;
        
        auto& atlas = fontAtlases[currentFontIndex];
        auto it = atlas->glyphs.find(c);
        if (it == atlas->glyphs.end()) return;
        
        const SDFGlyph& glyph = it->second;
        
        // Convert ImU32 color to float4
        float r = ((color >> 0) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = ((color >> 16) & 0xFF) / 255.0f;
        float a = ((color >> 24) & 0xFF) / 255.0f;
        XMFLOAT4 colorF4 = {r, g, b, a};

        // Debug: Log color values for first few glyphs
        static int colorDebugCount = 0;
        colorDebugCount++;
        if (colorDebugCount <= 3) {
            std::cout << "[SDF DEBUG] Color conversion - ImU32: 0x" << std::hex << color << std::dec << ", RGBA: (" << r << ", " << g << ", " << b << ", " << a << ")" << std::endl;
        }
        
        // Calculate glyph position and size
        float glyphX = x + glyph.xoff * scale;
        float glyphY = y + glyph.yoff * scale;
        float glyphW = glyph.width * scale;
        float glyphH = glyph.height * scale;
        
        // Add vertices (quad)
        uint16_t baseVertex = (uint16_t)renderBatch.vertices.size();
        
        renderBatch.vertices.push_back({{glyphX, glyphY}, {glyph.x0, glyph.y0}, colorF4});
        renderBatch.vertices.push_back({{glyphX + glyphW, glyphY}, {glyph.x1, glyph.y0}, colorF4});
        renderBatch.vertices.push_back({{glyphX + glyphW, glyphY + glyphH}, {glyph.x1, glyph.y1}, colorF4});
        renderBatch.vertices.push_back({{glyphX, glyphY + glyphH}, {glyph.x0, glyph.y1}, colorF4});
        
        // Add indices (two triangles)
        renderBatch.indices.push_back(baseVertex + 0);
        renderBatch.indices.push_back(baseVertex + 1);
        renderBatch.indices.push_back(baseVertex + 2);
        renderBatch.indices.push_back(baseVertex + 0);
        renderBatch.indices.push_back(baseVertex + 2);
        renderBatch.indices.push_back(baseVertex + 3);
        
        renderBatch.vertexCount += 4;
        renderBatch.indexCount += 6;
    }

    void SDFFontRenderer::UpdateConstantBuffer() {
        if (!constantBuffer) return;
        
        // Get viewport for orthographic projection
        D3D11_VIEWPORT viewport;
        UINT numViewports = 1;
        d3dContext->RSGetViewports(&numViewports, &viewport);
        
        // Create orthographic projection matrix (ImGui-compatible: Y increases downward)
        float left = 0.0f;
        float right = viewport.Width;
        float top = 0.0f;
        float bottom = viewport.Height;

        // Use top, bottom (not bottom, top) to match ImGui coordinate system
        XMMATRIX orthoMatrix = XMMatrixOrthographicOffCenterLH(left, right, top, bottom, -1.0f, 1.0f);
        
        // Update constants (no transpose needed with vector * matrix multiplication)
        constants.projection = orthoMatrix;
        constants.textColor = XMFLOAT4(1.0f, 1.0f, 1.0f, fontAlpha);
        constants.outlineColor = XMFLOAT4(0.0f, 0.0f, 0.0f, 1.0f);
        constants.outlineThickness = outlineThickness;
        constants.shadowOffset = shadowOffset;
        constants.glowRadius = glowRadius;
        constants.distanceRange = distanceRange;
        constants.screenSize = XMFLOAT2(viewport.Width, viewport.Height);
        
        // Map and update constant buffer
        D3D11_MAPPED_SUBRESOURCE mapped;
        if (SUCCEEDED(d3dContext->Map(constantBuffer, 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped))) {
            memcpy(mapped.pData, &constants, sizeof(SDFConstants));
            d3dContext->Unmap(constantBuffer, 0);
        }
    }

    void SDFFontRenderer::FlushRenderBatch() {
        if (renderBatch.vertexCount == 0 || fontAtlases.empty() || currentFontIndex >= fontAtlases.size()) {
            return;
        }

        // Debug: Log render batch info
        static int flushCount = 0;
        flushCount++;
        if (flushCount % 50 == 0) {
            std::cout << "[SDF DEBUG] FlushRenderBatch #" << flushCount << " - vertex count: " << renderBatch.vertexCount << std::endl;
        }
        
        auto& atlas = fontAtlases[currentFontIndex];
        
        // Update constant buffer
        UpdateConstantBuffer();
        
        // Map vertex buffer
        D3D11_MAPPED_SUBRESOURCE mappedVertex;
        if (FAILED(d3dContext->Map(vertexBuffer, 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedVertex))) {
            return;
        }
        memcpy(mappedVertex.pData, renderBatch.vertices.data(), sizeof(SDFVertex) * renderBatch.vertexCount);
        d3dContext->Unmap(vertexBuffer, 0);
        
        // Map index buffer
        D3D11_MAPPED_SUBRESOURCE mappedIndex;
        if (FAILED(d3dContext->Map(indexBuffer, 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedIndex))) {
            return;
        }
        memcpy(mappedIndex.pData, renderBatch.indices.data(), sizeof(uint16_t) * renderBatch.indexCount);
        d3dContext->Unmap(indexBuffer, 0);
        
        // Set up rendering pipeline
        d3dContext->IASetInputLayout(inputLayout);
        
        UINT stride = sizeof(SDFVertex);
        UINT offset = 0;
        d3dContext->IASetVertexBuffers(0, 1, &vertexBuffer, &stride, &offset);
        d3dContext->IASetIndexBuffer(indexBuffer, DXGI_FORMAT_R16_UINT, 0);
        d3dContext->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
        
        d3dContext->VSSetShader(vertexShader, nullptr, 0);
        d3dContext->VSSetConstantBuffers(0, 1, &constantBuffer);
        
        d3dContext->PSSetShader(pixelShader, nullptr, 0);
        d3dContext->PSSetConstantBuffers(0, 1, &constantBuffer);
        d3dContext->PSSetShaderResources(0, 1, &atlas->atlasSRV);
        d3dContext->PSSetSamplers(0, 1, &samplerState);
        
        // Set render states
        float blendFactor[4] = {0.0f, 0.0f, 0.0f, 0.0f};
        d3dContext->OMSetBlendState(blendState, blendFactor, 0xffffffff);
        d3dContext->OMSetDepthStencilState(depthStencilState, 0);
        d3dContext->RSSetState(rasterizerState);
        
        // Draw
        d3dContext->DrawIndexed((UINT)renderBatch.indexCount, 0, 0);
        
        // Clear batch
        renderBatch.clear();
    }

    void SDFFontRenderer::RenderText(const std::string& text, const Vector2& position, float size, ImU32 color) {
        if (!initialized) {
            std::cerr << "[SDF DEBUG] RenderText called but renderer not initialized" << std::endl;
            return;
        }

        // Debug: Log first call and coordinates
        static bool firstCall = true;
        static int callCount = 0;
        callCount++;

        if (firstCall || callCount % 100 == 0) {
            std::cout << "[SDF DEBUG] RenderText call #" << callCount << " - text: '" << text << "', pos: (" << position.x << ", " << position.y << "), size: " << size << std::endl;
            firstCall = false;
        }

        // Auto-create font atlas on first use if none exists
        if (fontAtlases.empty()) {
            std::cout << "[SDF DEBUG] Creating font atlas on first use..." << std::endl;
            if (!CreateSDFFontAtlas("ImGui Default", 48.0f, 1024)) {
                std::cerr << "[SDF] Failed to auto-create font atlas" << std::endl;
                return;
            }
            std::cout << "[SDF DEBUG] Font atlas created successfully" << std::endl;
        }
        
        if (currentFontIndex >= fontAtlases.size()) {
            return;
        }
        
        auto& atlas = fontAtlases[currentFontIndex];
        float scale = size / atlas->fontSize;
        float currentX = position.x;
        float currentY = position.y;
        
        renderBatch.reserve(text.length());
        
        for (char c : text) {
            if (c == '\n') {
                currentX = position.x;
                currentY += atlas->lineHeight * scale;
                continue;
            }
            
            auto it = atlas->glyphs.find(c);
            if (it != atlas->glyphs.end()) {
                AddGlyphToRenderBatch(c, currentX, currentY, scale, color);
                currentX += it->second.xadvance * scale;
            }
        }
        
        FlushRenderBatch();
    }

    void SDFFontRenderer::RenderTextList(const std::vector<std::string>& strings, const Vector2& position, float size, ImU32 color) {
        if (!initialized) {
            return;
        }
        
        // Auto-create font atlas on first use if none exists
        if (fontAtlases.empty()) {
            if (!CreateSDFFontAtlas("ImGui Default", 48.0f, 1024)) {
                std::cerr << "[SDF] Failed to auto-create font atlas" << std::endl;
                return;
            }
        }
        
        if (currentFontIndex >= fontAtlases.size()) {
            return;
        }
        
        auto& atlas = fontAtlases[currentFontIndex];
        float scale = size / atlas->fontSize;
        float lineHeight = atlas->lineHeight * scale;
        Vector2 currentPos = position;
        
        for (const std::string& str : strings) {
            RenderText(str, currentPos, size, color);
            currentPos.y += lineHeight;
        }
    }

    Vector2 SDFFontRenderer::CalcTextSize(const std::string& text, float size) {
        if (fontAtlases.empty() || currentFontIndex >= fontAtlases.size()) {
            return Vector2(0, 0);
        }
        
        auto& atlas = fontAtlases[currentFontIndex];
        float scale = size / atlas->fontSize;
        
        float width = 0.0f;
        float height = atlas->lineHeight * scale;
        float currentLineWidth = 0.0f;
        int lineCount = 1;
        
        for (char c : text) {
            if (c == '\n') {
                width = std::max(width, currentLineWidth);
                currentLineWidth = 0.0f;
                lineCount++;
                continue;
            }
            
            auto it = atlas->glyphs.find(c);
            if (it != atlas->glyphs.end()) {
                currentLineWidth += it->second.xadvance * scale;
            }
        }
        
        width = std::max(width, currentLineWidth);
        height = atlas->lineHeight * scale * lineCount;
        
        return Vector2(width, height);
    }

    std::string SDFFontRenderer::GetFontName(int index) const
    {
        if (index >= 0 && index < fontAtlases.size()) {
            return fontAtlases[index]->fontName;
        }
        return "";
    }

} // namespace DMARender 