#pragma once
#include <math.h>
#include <immintrin.h> // For AVX instructions

namespace DMARender {
	struct Vector3 {
		union {
			struct {
		float x;
		float y;
		float z;
			};
			__m128 simd; // SIMD register for vector operations
		};

		Vector3() {
			this->x = 0.0f;
			this->y = 0.0f;
			this->z = 0.0f;
		}

		Vector3(float x, float y, float z) {
			this->x = x;
			this->y = y;
			this->z = z;
		}

		Vector3 operator-(const Vector3& vec) {
			Vector3 result;
			result.simd = _mm_sub_ps(simd, vec.simd);
			return result;
		}

		Vector3 operator+(const Vector3& vec) {
			Vector3 result;
			result.simd = _mm_add_ps(simd, vec.simd);
			return result;
		}

		float Dot(const Vector3& vec) {
			__m128 mul = _mm_mul_ps(simd, vec.simd);
			__m128 shuf = _mm_movehdup_ps(mul);
			__m128 sums = _mm_add_ps(mul, shuf);
			shuf = _mm_movehl_ps(shuf, sums);
			sums = _mm_add_ss(sums, shuf);
			return _mm_cvtss_f32(sums);
		}

		float Dist(const Vector3& vec) const {
			__m128 diff = _mm_sub_ps(simd, vec.simd);
			__m128 mul = _mm_mul_ps(diff, diff);
			__m128 shuf = _mm_movehdup_ps(mul);
			__m128 sums = _mm_add_ps(mul, shuf);
			shuf = _mm_movehl_ps(shuf, sums);
			sums = _mm_add_ss(sums, shuf);
			return _mm_cvtss_f32(_mm_sqrt_ss(sums));
		}
	};
}