#include "MemoryUpdater.h"
#include "../../Structs/ClientSoldierEntity.h"  // Include for complete type
#include <iostream>
#include <chrono>
#include <thread>
#include <string>

// Forward declare global logger defined in main
extern void DebugLog(const std::string& message);

BF2042::MemoryUpdater::MemoryUpdater(BF2042::Mem* mem) : mem(mem) {
    cachedCamera = std::make_shared<Camera>();
}

BF2042::MemoryUpdater::~MemoryUpdater() {
    endUpdateLoop();
}

void BF2042::MemoryUpdater::beginUpdateLoop() {
    if (threadRunning) { return; }

    threadRunning = true;
    DebugLog("[BF2042] Starting memory update threads...");

    // Small delay to ensure threadRunning is set before threads start
    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    // Start update threads
    playerThread = std::thread(&MemoryUpdater::playerWorker, this);
    cameraThread = std::thread(&MemoryUpdater::cameraWorker, this);

    // Give threads time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    DebugLog("[BF2042] Memory update threads started");
}

void BF2042::MemoryUpdater::endUpdateLoop() {
    if (!threadRunning) {
        return;
    }

    DebugLog("[BF2042] Stopping memory update threads...");
    threadRunning = false;
    
    if (playerThread.joinable()) {
        playerThread.join();
    }
    
    if (cameraThread.joinable()) {
        cameraThread.join();
    }
    
    DebugLog("[BF2042] Memory update threads stopped");
}

void BF2042::MemoryUpdater::playerWorker() {
    DebugLog("[BF2042] Player worker thread started");

    DebugLog(std::string("[BF2042 WORKER] Checking threadRunning flag: ") + (threadRunning ? "true" : "false"));

    if (!threadRunning) {
        DebugLog("[BF6 WORKER] ERROR: threadRunning is false! Worker exiting immediately!");
        return;
    }

    DebugLog("[BF6 WORKER] Entering main worker loop...");

    while (threadRunning) {
        try {
            // Update at 30 FPS (33ms intervals) for player data
            std::this_thread::sleep_for(std::chrono::milliseconds(33));

            // tight loop, avoid spam

            // FORCE Dynamic CR3 bypass check FIRST - before any other memory operations that might hang
            DebugLog("[BF6 WORKER] === FORCING DYNAMIC CR3 BYPASS CHECK ===");

            // Check for EA anti-cheat corruption and activate Dynamic CR3 bypass if needed
            if (useDynamicCR3 && dynamicCR3Reader && mem->getBF2042Integration()) {
                // Force check for corruption every loop iteration to see the bypass in action
                QWORD gameContextAddr = mem->getBF2042Integration()->GetGameContext();
                if (gameContextAddr) {
                    DebugLog("[BF2042] 🔍 Checking for EA anti-cheat corruption...");

                    // Try to read PlayerManager normally first
                    QWORD playerManager = mem->getBF2042Integration()->GetPlayerManager();

                    if (playerManager == 0 || mem->getBF2042Integration()->IsCorruptionDetected()) {
                        DebugLog("[BF2042] 🎯 EA CORRUPTION DETECTED - ACTIVATING DYNAMIC CR3 BYPASS!");
                        DebugLog("[BF2042] Corrupted PlayerManager: 0x" + std::to_string(playerManager));

                        // Try to read PlayerManager using Dynamic CR3 bypass
                        QWORD validPlayerManager = 0;

                        if (dynamicCR3Reader->ReadPlayerManager(gameContextAddr, validPlayerManager)) {
                            DebugLog("[BF2042] ✅ Dynamic CR3 bypass SUCCESS! Valid PlayerManager: 0x" + std::to_string(validPlayerManager));

                            // Continue with normal processing using the valid PlayerManager
                            // Note: This would require modifying the memory structures to use the bypassed data
                            // For now, we'll just log the success and continue with normal flow
                        } else {
                            DebugLog("[BF2042] ❌ Dynamic CR3 bypass failed - EA protection too strong");
                        }
                    } else {
                        DebugLog("[BF2042] ✅ PlayerManager appears valid: 0x" + std::to_string(playerManager));
                    }
                } else {
                    DebugLog("[BF2042] ❌ GameContext not available for bypass check");
                }

                // Add a small delay to prevent spam
                std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            }

            // Get game context and update players (this might hang, so we do bypass check first)
            DebugLog("[BF6 WORKER] Attempting to get world data...");
            auto world = mem->getWorld();
            if (!world.isValid()) { continue; }

            auto gameContext = world.GameContextPtr;
            if (!gameContext || !gameContext->isValid()) { continue; }

            // Dynamic CR3 bypass check moved to beginning of loop to avoid hanging

            auto playerManager = gameContext->PlayerMgr;
            if (!playerManager || !playerManager->isValid()) { continue; }
            
            // Resolve via standard reads only
            if (playerManager->resolvePlayerArray(mem->getVMM(), mem->getPid())) {
                std::lock_guard<std::mutex> lock(playerMutex);
                cachedPlayers = playerManager->getAllPlayers();
                cachedLocalPlayer = playerManager->getLocalPlayer();
            }
            
            } catch (const std::exception& e) {
            DebugLog(std::string("[BF6] Player worker error: ") + e.what());
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } catch (...) {
            DebugLog("[BF6] Player worker unknown exception caught!");
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    DebugLog("[BF2042] Player worker thread stopped");
}

void BF2042::MemoryUpdater::cameraWorker() {
    std::cout << "[BF2042] Camera worker thread started" << std::endl;
    
    while (threadRunning) {
        try {
            // Update at 60 FPS (16ms intervals) for camera data
            std::this_thread::sleep_for(std::chrono::milliseconds(16));
            
            // Get game renderer and update camera
            auto networkManager = mem->getNetworkManager();
            if (!networkManager.isValid()) {
                continue;
            }
            
            auto gameRenderer = networkManager.getGameRenderer();
            if (!gameRenderer || !gameRenderer->isValid()) {
                continue;
            }
            
            // Update camera with latest view matrix
            std::lock_guard<std::mutex> lock(cameraMutex);
            if (cachedCamera && cachedCamera->viewMatrix) {
                // The view matrix should be automatically updated through the memory object system
                // For now, just ensure the camera object is valid
                // The actual matrix data will be updated through the DMA memory system
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[BF6] Camera worker error: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    std::cout << "[BF2042] Camera worker thread stopped" << std::endl;
}

std::vector<std::shared_ptr<BF2042::ClientPlayer>> BF2042::MemoryUpdater::getAllPlayers() {
    std::lock_guard<std::mutex> lock(playerMutex);
    return cachedPlayers;
}

std::shared_ptr<BF2042::ClientPlayer> BF2042::MemoryUpdater::getLocalPlayer() {
    std::lock_guard<std::mutex> lock(playerMutex);
    return cachedLocalPlayer;
}

std::shared_ptr<BF2042::Camera> BF2042::MemoryUpdater::getCamera() {
    std::lock_guard<std::mutex> lock(cameraMutex);
    return cachedCamera;
}

size_t BF2042::MemoryUpdater::getPlayerCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(playerMutex));
    return cachedPlayers.size();
}

bool BF2042::MemoryUpdater::updateGameContext() {
    try {
        auto world = mem->getWorld();
        return world.isValid() && world.GameContextPtr && world.GameContextPtr->isValid();
    } catch (...) {
        return false;
    }
}

bool BF2042::MemoryUpdater::updatePlayerManager() {
    try {
        auto world = mem->getWorld();
        if (!world.isValid()) return false;
        
        auto gameContext = world.GameContextPtr;
        if (!gameContext) return false;
        
        auto playerManager = gameContext->PlayerMgr;
        return playerManager && playerManager->isValid();
    } catch (...) {
        return false;
    }
}

bool BF2042::MemoryUpdater::updateGameRenderer() {
    try {
        auto networkManager = mem->getNetworkManager();
        if (!networkManager.isValid()) return false;
        
        auto gameRenderer = networkManager.getGameRenderer();
        return gameRenderer && gameRenderer->isValid();
    } catch (...) {
        return false;
    }
}

// CR3 Bypass Implementation
bool BF2042::MemoryUpdater::initializeCR3Bypass() {
    if (!mem || !mem->getVMM()) {
        std::cerr << "[BF2042] Cannot initialize CR3 bypass: VmmManager is null" << std::endl;
        return false;
    }

    try {
        cr3Reader = std::make_unique<DMAMem::BF2042CR3Reader>(mem->getVMM(), mem->getPid());

        if (!cr3Reader->Initialize()) {
            std::cerr << "[BF2042] Failed to initialize CR3 bypass reader" << std::endl;
            cr3Reader.reset();
            return false;
        }

        std::cout << "[BF2042] CR3 bypass initialized successfully!" << std::endl;
        useCR3Bypass = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[BF2042] Exception during CR3 bypass initialization: " << e.what() << std::endl;
        cr3Reader.reset();
        return false;
    }
}

void BF2042::MemoryUpdater::testCR3Bypass() {
    if (!cr3Reader) {
        std::cout << "[BF2042] CR3 bypass not initialized. Initializing now..." << std::endl;
        if (!initializeCR3Bypass()) {
            std::cout << "[BF2042] Failed to initialize CR3 bypass for testing" << std::endl;
            return;
        }
    }

    std::cout << "\n[BF2042] === Testing CR3 Bypass Against EA Anti-Cheat ===" << std::endl;
    std::cout << "[BF2042] This test attempts to bypass EA's CR3/PML4 manipulation" << std::endl;
    std::cout << "[BF2042] Target PID: " << mem->getPid() << std::endl;

    bool testResult = cr3Reader->TestBF2042Memory();

    if (testResult) {
        std::cout << "[BF2042] ✅ CR3 BYPASS TEST PASSED!" << std::endl;
        std::cout << "[BF2042] Successfully bypassed EA's anti-cheat protection" << std::endl;
        std::cout << "[BF2042] Memory reads are working via manual page table translation" << std::endl;
    } else {
        std::cout << "[BF2042] ❌ CR3 BYPASS TEST FAILED" << std::endl;
        std::cout << "[BF2042] EA's anti-cheat is still blocking memory access" << std::endl;
        std::cout << "[BF2042] May need more advanced bypass techniques" << std::endl;
    }

    std::cout << "[BF2042] ================================================\n" << std::endl;
}

void BF2042::MemoryUpdater::printCR3Statistics() {
    if (!cr3Reader) {
        std::cout << "[BF2042] CR3 bypass not initialized" << std::endl;
        return;
    }

    cr3Reader->PrintDiagnostics();
}

// Dynamic CR3 Implementation (New EA Bypass Method)
bool BF2042::MemoryUpdater::initializeDynamicCR3() {
    if (!mem || !mem->getVMM()) {
        std::cerr << "[BF2042] Cannot initialize Dynamic CR3: VmmManager is null" << std::endl;
        return false;
    }

    try {
        std::cout << "[BF2042] === INITIALIZING DYNAMIC CR3 BYPASS ===" << std::endl;
        std::cout << "[BF2042] Implementing advanced EA anti-cheat bypass strategy" << std::endl;
        std::cout << "[BF2042] Target: Multiple fake CR3s and continuous shuffling" << std::endl;

        // Create the BF2042DynamicCR3Reader
        dynamicCR3Reader = std::make_unique<DMAMem::BF2042DynamicCR3Reader>(mem->getVMM(), mem->getPid());
        if (!dynamicCR3Reader->Initialize()) {
            std::cerr << "[BF2042] Failed to initialize Dynamic CR3 reader" << std::endl;
            dynamicCR3Reader.reset();
            return false;
        }

        std::cout << "[BF2042] ✅ Dynamic CR3 bypass initialized successfully!" << std::endl;
        useDynamicCR3 = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[BF2042] Exception during Dynamic CR3 initialization: " << e.what() << std::endl;
        dynamicCR3Reader.reset();
        return false;
    }
}

void BF2042::MemoryUpdater::testDynamicCR3() {
    if (!dynamicCR3Reader) {
        std::cout << "[BF2042] Dynamic CR3 not initialized. Initializing now..." << std::endl;
        if (!initializeDynamicCR3()) {
            std::cout << "[BF2042] Failed to initialize Dynamic CR3 for testing" << std::endl;
            return;
        }
    }

    std::cout << "\n[BF2042] === Testing Dynamic CR3 Against EA Anti-Cheat ===" << std::endl;
    std::cout << "[BF2042] This test validates dynamic CR3 management against EA's shuffle protection" << std::endl;
    std::cout << "[BF2042] Target PID: " << mem->getPid() << std::endl;

    bool testResult = dynamicCR3Reader->TestBF2042Access();

    if (testResult) {
        std::cout << "[BF2042] ✅ Dynamic CR3 bypass test PASSED!" << std::endl;
        std::cout << "[BF2042] 🎯 EA's anti-cheat successfully bypassed!" << std::endl;
        std::cout << "[BF2042] 💡 Dynamic CR3 management is working correctly" << std::endl;
    } else {
        std::cout << "[BF2042] ❌ Dynamic CR3 bypass test FAILED" << std::endl;
        std::cout << "[BF2042] 💡 EA's protection may require additional techniques" << std::endl;
    }
}

void BF2042::MemoryUpdater::printDynamicCR3Statistics() {
    if (dynamicCR3Reader) {
        dynamicCR3Reader->PrintDiagnostics();
    } else {
        std::cout << "[BF2042] Dynamic CR3 bypass not initialized" << std::endl;
    }
}
