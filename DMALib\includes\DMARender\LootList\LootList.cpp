#include "LootList.h"
#include <algorithm>
#include <sstream>
#include <fstream>
#include <iostream>

namespace DMARender {

    LootListManager::LootListManager() : saveFilePath("rareitems.txt"), isDirty(false) {
        loadFromFile();
    }

    LootListManager::~LootListManager() {
        if (isDirty) {
            saveToFile();
        }
    }

    void LootListManager::addRareItem(const std::string& itemName, ImU32 color, const std::string& customName) {
        if (!itemName.empty()) {
            rareItems[itemName] = RareItemData(itemName, color, customName);
            isDirty = true;
        }
    }

    void LootListManager::removeRareItem(const std::string& itemName) {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end()) {
            rareItems.erase(it);
            isDirty = true;
        }
    }

    void LootListManager::clearAll() {
        rareItems.clear();
        isDirty = true;
    }

    bool LootListManager::isRareItem(const std::string& itemName) const {
        return rareItems.find(itemName) != rareItems.end();
    }

    void LootListManager::setItemColor(const std::string& itemName, ImU32 color) {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end()) {
            it->second.color = color;
            isDirty = true;
        }
    }

    ImU32 LootListManager::getItemColor(const std::string& itemName) const {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end()) {
            return it->second.color;
        }
        return IM_COL32(255, 0, 255, 255); // Default purple
    }

    void LootListManager::setItemCustomName(const std::string& itemName, const std::string& customName) {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end()) {
            it->second.customName = customName.empty() ? itemName : customName;
            isDirty = true;
        }
    }

    std::string LootListManager::getItemCustomName(const std::string& itemName) const {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end()) {
            return it->second.customName;
        }
        return itemName; // Return original name if not found
    }

    std::string LootListManager::getDisplayName(const std::string& itemName) const {
        auto it = rareItems.find(itemName);
        if (it != rareItems.end() && !it->second.customName.empty()) {
            return it->second.customName;
        }
        return itemName; // Return original name if no custom name set
    }

    std::set<std::string> LootListManager::getRareItems() const {
        std::set<std::string> itemNames;
        for (const auto& pair : rareItems) {
            itemNames.insert(pair.first);
        }
        return itemNames;
    }

    std::vector<std::string> LootListManager::findItemsByPartialName(const std::string& partialName) const {
        std::vector<std::string> matches;
        for (const auto& pair : rareItems) {
            if (pair.first.find(partialName) != std::string::npos) {
                matches.push_back(pair.first);
            }
        }
        return matches;
    }

    std::vector<std::string> LootListManager::getSortedItems() const {
        std::vector<std::string> sortedItems;
        for (const auto& pair : rareItems) {
            sortedItems.push_back(pair.first);
        }
        std::sort(sortedItems.begin(), sortedItems.end());
        return sortedItems;
    }

    bool LootListManager::saveToFile() {
        try {
            std::ofstream file(saveFilePath);
            if (!file.is_open()) {
                return false;
            }

            for (const auto& pair : rareItems) {
                // Format: itemName|color|customName
                file << pair.first << "|" << std::hex << pair.second.color << std::dec << "|" << pair.second.customName << std::endl;
            }

            file.close();
            isDirty = false;
            return true;
        }
        catch (...) {
            return false;
        }
    }

    bool LootListManager::loadFromFile() {
        try {
            std::ifstream file(saveFilePath);
            if (!file.is_open()) {
                return false; // File doesn't exist yet, that's okay
            }

            rareItems.clear();
            std::string line;
            while (std::getline(file, line)) {
                if (!line.empty()) {
                    size_t firstPipe = line.find('|');
                    if (firstPipe != std::string::npos) {
                        std::string itemName = line.substr(0, firstPipe);
                        size_t secondPipe = line.find('|', firstPipe + 1);

                        if (secondPipe != std::string::npos) {
                            // New format with color and custom name: itemName|color|customName
                            std::string colorStr = line.substr(firstPipe + 1, secondPipe - firstPipe - 1);
                            std::string customName = line.substr(secondPipe + 1);
                            ImU32 color = std::stoul(colorStr, nullptr, 16);
                            rareItems[itemName] = RareItemData(itemName, color, customName);
                        } else {
                            // Format with color only: itemName|color
                            std::string colorStr = line.substr(firstPipe + 1);
                            ImU32 color = std::stoul(colorStr, nullptr, 16);
                            rareItems[itemName] = RareItemData(itemName, color, itemName);
                        }
                    } else {
                        // Old format without color - use default purple and original name
                        rareItems[line] = RareItemData(line, IM_COL32(255, 0, 255, 255), line);
                    }
                }
            }

            file.close();
            isDirty = false;
            return true;
        }
        catch (...) {
            return false;
        }
    }

    void LootListManager::syncWithEntitySystem() {
        // This will be implemented when we have access to Entity class
        // For now, we'll handle sync manually in the adapters
    }

    void LootListManager::syncFromEntitySystem() {
        // This will be implemented when we have access to Entity class
        // For now, we'll handle sync manually in the adapters
    }

} // namespace DMARender
