<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{05d76552-ed33-44e3-81b3-8b0006ca45e9}</ProjectGuid>
    <RootNamespace>dmabf2042cpp</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>dma-bf2042-cpp</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>Battlefield-2042-DMA-ESP-Debug</TargetName>
    <IncludePath>C:\Users\<USER>\Desktop\bf6 main\DMALib\includes;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui\backends;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui\misc\cpp;C:\Users\<USER>\Desktop\bf6 main\freetype\include;$(IncludePath)</IncludePath>
    <LibraryPath>C:\Users\<USER>\Desktop\bf6 main\DMALib\libs;C:\Users\<USER>\Desktop\bf6 main\freetype\release static\vs2015-2022\win64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>Battlefield-2042-DMA-ESP</TargetName>
    <IncludePath>C:\Users\<USER>\Desktop\bf6 main\DMALib\includes;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui\backends;C:\Users\<USER>\Desktop\bf6 main\DMALib\imgui\misc\cpp;C:\Users\<USER>\Desktop\bf6 main\freetype\include;$(IncludePath)</IncludePath>
    <LibraryPath>C:\Users\<USER>\Desktop\bf6 main\DMALib\libs;C:\Users\<USER>\Desktop\bf6 main\freetype\release static\vs2015-2022\win64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)DMALib\includes;$(SolutionDir)DMALib\imgui;$(SolutionDir)DMALib\imgui\backends;$(SolutionDir)DMALib\imgui\misc\cpp</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)x64\Release;$(SolutionDir)DMALib\libs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>DMALib.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)DMALib\includes;$(SolutionDir)DMALib\imgui;$(SolutionDir)DMALib\imgui\backends;$(SolutionDir)DMALib\imgui\misc\cpp</AdditionalIncludeDirectories>
      <Optimization>MaxSpeed</Optimization>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)x64\Release;$(SolutionDir)DMALib\libs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>DMALib.lib;freetype.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="BF2042\Config\MultiCoreConfig.cpp" />
    <ClCompile Include="BF2042\BF2042Mem\BF2042Mem.cpp" />
    <ClCompile Include="BF2042\BF2042Mem\MemoryUpdater\MemoryUpdater.cpp" />
    <ClCompile Include="BF2042\BF2042Mem\OverlayAdapter\OverlayAdapter.cpp" />
    <ClCompile Include="BF2042\BF2042Mem\RadarAdapter\RadarAdapter.cpp" />
    <ClCompile Include="BF2042\Structs\ClientPlayer.cpp" />
    <ClCompile Include="BF2042\TestCR3Bypass.cpp" />
    <ClCompile Include="..\DMALib\includes\DMAMemoryManagement\CR3Bypass\CR3Bypass.cpp" />
    <ClCompile Include="..\DMALib\includes\DMAMemoryManagement\CR3Bypass\DynamicCR3Manager.cpp" />
    <ClCompile Include="dma-dayz-cpp.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="BF2042\Config\MultiCoreConfig.h" />
    <ClInclude Include="BF2042\BF2042Mem\BF2042Mem.h" />
    <ClInclude Include="BF2042\BF2042Mem\MemoryUpdater\MemoryUpdater.h" />
    <ClInclude Include="BF2042\BF2042Mem\OverlayAdapter\OverlayAdapter.h" />
    <ClInclude Include="BF2042\BF2042Mem\RadarAdapter\RadarAdapter.h" />
    <ClInclude Include="..\DMALib\includes\DMAMemoryManagement\CR3Bypass\CR3Bypass.h" />

    <ClInclude Include="BF2042\Structs\BoneCollisionComponent.h" />
    <ClInclude Include="BF2042\Structs\Camera.h" />
    <ClInclude Include="BF2042\Structs\ClientPlayer.h" />
    <ClInclude Include="BF2042\Structs\ClientSoldierEntity.h" />
    <ClInclude Include="BF2042\Structs\ClientVehicleEntity.h" />
    <ClInclude Include="BF2042\Structs\MeshOverlayComponent.h" />
    <ClInclude Include="BF2042\Structs\SpottingTargetComponent.h" />
    <ClInclude Include="BF2042\Structs\Entity.h" />
    <ClInclude Include="BF2042\Structs\GameContext.h" />
    <ClInclude Include="BF2042\Structs\GameRenderer.h" />
    <ClInclude Include="BF2042\Structs\HealthComponent.h" />
    <ClInclude Include="BF2042\Structs\NetworkManager.h" />
    <ClInclude Include="BF2042\Structs\PlayerManager.h" />
    <ClInclude Include="BF2042\Structs\VehicleEntityData.h" />
    <ClInclude Include="BF2042\Structs\ViewMatrix.h" />
    <ClInclude Include="BF2042\Structs\WeaponsComponent.h" />
    <ClInclude Include="BF2042\Structs\WorldPointer.h" />
    <ClInclude Include="resource.h" />

  </ItemGroup>

  <ItemGroup>
    <ResourceCompile Include="dma-dayz-cpp.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="53ac2d01e41ff949c11c916309ae17b7.ico" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>