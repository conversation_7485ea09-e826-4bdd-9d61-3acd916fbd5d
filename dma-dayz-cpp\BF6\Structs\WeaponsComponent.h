#pragma once
#include "DMAMemoryManagement/includes.h"

namespace BF6 {
    class WeaponsComponent : public DMAMem::MemoryObject {
    public:
        QWORD CurrentWeaponNamePtr;
        uint32_t AmmoCount;
        uint32_t MaxAmmo;

        WeaponsComponent() {
            // BF6 WeaponsComponent offsets (to be determined from further analysis)
            // These are placeholder offsets that will need to be updated
            this->registerOffset(0x00, &CurrentWeaponNamePtr, sizeof(QWORD));
            this->registerOffset(0x10, &AmmoCount, sizeof(uint32_t));
            this->registerOffset(0x14, &MaxAmmo, sizeof(uint32_t));
        }

        // Helper methods
        bool hasWeapon() const {
            return CurrentWeaponNamePtr != 0;
        }
        
        std::string getWeaponName() const {
            if (CurrentWeaponNamePtr != 0) {
                // TODO: Implement BF2042-specific string reading when needed
                return "Weapon";
            }
            return "";
        }
        
        bool isLowAmmo() const {
            return MaxAmmo > 0 && (float(AmmoCount) / float(MaxAmmo)) < 0.25f;
        }
        
        float getAmmoPercentage() const {
            if (MaxAmmo > 0) {
                return (float(AmmoCount) / float(MaxAmmo)) * 100.0f;
            }
            return 0.0f;
        }
    };
}
