#pragma once

#include <vector>
#include <string>
#include <unordered_set>


//CASESENSITIVE!!!!!!!!
namespace DayZ {
	const std::vector<std::string> InventoryItemWhitelist{
		// IMPORTANT: Keep "inventoryItem" for modded item detection, but add smart filtering
		"inventoryItem", "itemoptics", "carwheel", "ItemBook", "ItemSuppressor",
		// Specific optics and attachments
		"ItemOptics", "ItemSuppressor",
		// Specific car parts
		"CarWheel", "CarBattery", "CarRadiator", "SparkPlug", "GlowPlug", "HeadlightH7", "TruckBattery",
		// Specific camping items
		"CarTent", "MediumTent", "LargeTent",
		// Specific tools and navigation
		"Compass", "Map", "Flashlight", "Binoculars", "Rangefinder", "NVGoggles",
		// Specific communication
		"PersonalRadio", "Walkietalkie",
		// Specific chemlights
		"Chemlight_White", "Chemlight_<PERSON>", "Chemlight_Green", "Chemlight_Blue", "Chemlight_Yellow", "RoadFlare",
		// Specific medical items
		"Morphine", "Epinephrine", "Bandage", "Disinfectant", "BloodBag", "SalineBag", "StartKitIV", "Defibrillator", "Thermometer",
		// Specific repair items
		"Duct_Tape", "ElectronicRepairKit", "TireRepairKit", "SewingKit", "WeaponCleaningKit", "Sharpening_Stone", "Whetstone"
	};
	const std::vector<std::string> itemBlacklist {
		"AreaDamageTriggerBase", "TrapTrigger", "TripWireTrigger", "ClutterCutter6x6", 
		"ContaminatedTrigger_Dynamic", 
		"ContaminatedArea_Dynamic", "ContaminatedArea_Static", "ContaminatedTrigger", 
		"AreaDamageTriggerBase", "TreeEffecter", "FreeDebugCamera", 
		"SnapPoint", "TerritoryTrigger", "AP_FarmTrigger","AP_TamedTrigger",
		"NoBuildZone", "SafeZoneTrigger", "Beam", "Beam",
		"Fog_30_MovingWind1", "$UNT$Clutter Cutter 3x3", "ClutterCutter6x6", "SafeZoneTeleportTrigger",
		"house", "ContaminatedArea_Static", "ContaminatedTrigger", "TerritoryTrigger", "TrapTrigger", "SafezoneTrigger", "GunRangeTrigger", "SafeZoneExitTeleportTrigger", "UndergroundTriggerCarrier", "UndergroundTrigger"
	};

	// Performance optimization: Hash set for O(1) blacklist lookups
	const std::unordered_set<std::string> itemBlacklistSet(itemBlacklist.begin(), itemBlacklist.end());
}