#pragma once
#include "../includes.h"
#include "../VmmManager/VmmManager.h"
#include <vector>
#include <unordered_map>
#include <mutex>
#include <chrono>
#include <atomic>

namespace DMAMem {

    // CR3 quality metrics for validation
    struct CR3QualityMetrics {
        int entityListScore = 0;        // 0-25 points
        int localPlayerScore = 0;       // 0-25 points
        int consistencyScore = 0;       // 0-25 points
        int stabilityScore = 0;         // 0-25 points
        int totalScore = 0;             // 0-100 total
        
        bool isValid() const { return totalScore >= 70; }
        bool isExcellent() const { return totalScore >= 90; }
    };

    // CR3 candidate with tracking data
    struct CR3Candidate {
        QWORD cr3Value;
        CR3QualityMetrics metrics;
        std::chrono::steady_clock::time_point lastValidated;
        std::chrono::steady_clock::time_point firstDiscovered;
        int validationAttempts = 0;
        int successfulValidations = 0;
        int consecutiveFailures = 0;
        bool isCurrentlyActive = false;
        bool isFlaggedAsFake = false;
        
        // Performance tracking
        double averageValidationTime = 0.0;
        int entityCount = 0;
        int lastEntityCount = 0;
        bool hasLocalPlayer = false;
        
        double getSuccessRate() const {
            return validationAttempts > 0 ? 
                (double)successfulValidations / validationAttempts : 0.0;
        }
        
        bool isStable() const {
            return consecutiveFailures < 3 && getSuccessRate() > 0.7;
        }
    };

    /**
     * \brief Dynamic CR3 Manager for EA's Anti-Cheat Bypass
     * Handles EA's sophisticated CR3 shuffle mechanism with multiple fake CR3s
     * Implements quality-based validation and continuous monitoring
     */
    class DynamicCR3Manager {
    private:
        VmmManager* vmm;
        DWORD targetPid;
        
        // CR3 management
        std::vector<CR3Candidate> cr3Candidates;
        mutable std::mutex cr3Mutex;
        QWORD currentActiveCR3 = 0;
        
        // Validation settings
        static constexpr int MIN_QUALITY_SCORE = 70;
        static constexpr int EXCELLENT_QUALITY_SCORE = 90;
        static constexpr int MAX_CONSECUTIVE_FAILURES = 3;
        static constexpr auto VALIDATION_INTERVAL = std::chrono::seconds(5);
        static constexpr auto CR3_DISCOVERY_INTERVAL = std::chrono::seconds(30);
        
        // Performance tracking
        struct Statistics {
            int totalCR3sDiscovered = 0;
            int validCR3sFound = 0;
            int fakeCR3sDetected = 0;
            int shuffleEventsDetected = 0;
            int successfulSwitches = 0;
            std::chrono::steady_clock::time_point lastShuffleDetected;
        } stats;
        
        // Threading
        std::atomic<bool> managerRunning = false;
        std::thread managementThread;
        
        // Internal methods
        bool DiscoverCR3Candidates();
        CR3QualityMetrics ValidateCR3Quality(QWORD cr3);
        bool TestEntityListAccess(QWORD cr3, int& entityCount);
        bool TestLocalPlayerAccess(QWORD cr3);
        bool TestMemoryConsistency(QWORD cr3);
        bool TestLongTermStability(QWORD cr3);
        
        void UpdateCR3Metrics(CR3Candidate& candidate);
        void DetectShuffleEvents();
        void CleanupInvalidCR3s();
        void ManagementLoop();
        
        // Validation helpers
        bool ReadWithCR3(QWORD cr3, QWORD virtualAddress, void* buffer, SIZE_T size);
        bool ValidateGameStructures(QWORD cr3);
        bool CheckForMissingEntities(QWORD cr3, const std::vector<QWORD>& previousEntities);

    public:
        DynamicCR3Manager(VmmManager* vmmManager, DWORD processId);
        ~DynamicCR3Manager();
        
        // Main interface
        bool Initialize();
        void StartManagement();
        void StopManagement();
        
        // CR3 access
        QWORD GetCurrentCR3();
        bool SwitchToNextBestCR3();
        std::vector<CR3Candidate> GetAllCandidates();
        
        // Validation and testing
        bool ValidateCurrentCR3();
        bool TestCR3(QWORD cr3);
        CR3QualityMetrics GetCR3Quality(QWORD cr3);
        
        // Memory access with automatic CR3 management
        bool ReadMemoryWithBestCR3(QWORD virtualAddress, void* buffer, SIZE_T size);
        bool ReadMemoryWithFallback(QWORD virtualAddress, void* buffer, SIZE_T size);
        
        // Diagnostics and monitoring
        void PrintStatistics() const;
        void PrintCR3Candidates() const;
        bool IsShuffleEventDetected() const;
        double GetCurrentCR3Quality() const;
        
        // Advanced features
        void ForceCR3Discovery();
        void MarkCR3AsFake(QWORD cr3);
        void ResetStatistics();
        bool IsManagementActive() const { return managerRunning; }
        
        // BF2042-specific validation
        bool ValidateBF2042Structures(QWORD cr3);
        bool TestPlayerManagerAccess(QWORD cr3, QWORD gameContext);
        bool TestClientPlayerArrayAccess(QWORD cr3, QWORD playerManager);
    };

    /**
     * \brief BF2042-specific CR3 Reader with Dynamic Management
     * Integrates dynamic CR3 management with BF2042 structure access
     */
    class BF2042DynamicCR3Reader {
    private:
        std::unique_ptr<DynamicCR3Manager> cr3Manager;
        VmmManager* vmm;
        DWORD pid;
        
        // BF2042-specific addresses
        QWORD gameContextAddress = 0;
        QWORD playerManagerAddress = 0;
        QWORD clientPlayerArrayAddress = 0;
        
        // Performance tracking
        std::chrono::steady_clock::time_point lastSuccessfulRead;
        int consecutiveReadFailures = 0;

    public:
        BF2042DynamicCR3Reader(VmmManager* vmmManager, DWORD processId);
        ~BF2042DynamicCR3Reader();
        
        bool Initialize();
        
        // BF2042-specific reading with dynamic CR3
        bool ReadGameContext(QWORD& gameContextAddr);
        bool ReadPlayerManager(QWORD gameContext, QWORD& playerManagerAddr);
        bool ReadClientPlayerArray(QWORD playerManager, std::vector<QWORD>& playerAddresses);
        bool ReadPlayerData(QWORD playerAddress, void* buffer, SIZE_T size);
        
        // Advanced BF2042 access
        bool ValidatePlayerChain();
        bool RefreshAddresses();
        int GetValidPlayerCount();
        
        // Diagnostics
        void PrintDiagnostics() const;
        bool TestBF2042Access();
        
        // Direct access to CR3 manager
        DynamicCR3Manager* GetCR3Manager() { return cr3Manager.get(); }
    };

} // namespace DMAMem
