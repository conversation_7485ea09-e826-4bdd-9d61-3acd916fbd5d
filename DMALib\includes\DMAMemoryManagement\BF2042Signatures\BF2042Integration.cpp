#include "BF2042Integration.h"
#include "../CR3Bypass/DynamicCR3Manager.h"
#include <iostream>
#include <sstream>
#include <iomanip>

// External debug logging function (same as main ESP)
extern void DebugLog(const std::string& message);

namespace DMAMem {

    BF2042Integration::BF2042Integration(VmmManager* vmm, DWORD pid)
        : vmmManager(vmm), targetPID(pid), isInitialized(false) {
        signatures = new BF2042Signatures(vmm, pid, "BF2042.exe");
        lastScanTime = std::chrono::steady_clock::now();
        
        std::cout << "[BF2042Integration] 🚀 Comprehensive signature scanning initialized" << std::endl;
        std::cout << "[BF2042Integration] 📊 Forum-confirmed structures loaded" << std::endl;
        std::cout << "[BF2042Integration] 🛡️ Anti-cheat bypass enabled" << std::endl;
    }

    BF2042Integration::~BF2042Integration() {
        if (signatures) {
            delete signatures;
            signatures = nullptr;
        }

    }

    BOOL BF2042Integration::Initialize() {
        if (!vmmManager) {
            std::cout << "[-] VmmManager is null" << std::endl;
            return FALSE;
        }

        std::cout << "[BF2042Integration] ===== INITIALIZATION STARTING =====" << std::endl;
        std::cout << "[+] Initializing BF2042 comprehensive integration..." << std::endl;

        // Try CR3 fix first for anti-cheat bypass
        if (!TryFixCR3()) {
            std::cout << "[!] CR3 bypass failed, continuing with standard access..." << std::endl;
        }

        // Initialize signature scanning (don't fail if it doesn't work)
        std::cout << "[BF2042Integration] Attempting signature scanning initialization..." << std::endl;
        if (!signatures->Initialize()) {
            std::cout << "[BF2042Integration] Signature scanning initialization failed, but continuing..." << std::endl;
            std::cout << "[BF2042Integration] Will rely on hardcoded addresses and runtime scanning" << std::endl;
        } else {
            std::cout << "[BF2042Integration] Signature scanning initialization successful!" << std::endl;
        }

        // Scan for all base addresses (don't fail if some aren't found)
        std::cout << "[BF2042Integration] Attempting to scan for base addresses..." << std::endl;
        if (!ScanForAddresses()) {
            std::cout << "[BF2042Integration] Address scanning failed, but continuing..." << std::endl;
            std::cout << "[BF2042Integration] Will attempt runtime address resolution" << std::endl;
        } else {
            std::cout << "[BF2042Integration] Address scanning successful!" << std::endl;
        }

        // Validate all structures (don't fail if validation fails)
        std::cout << "[BF2042Integration] Attempting structure validation..." << std::endl;
        if (!ValidateAllStructures()) {
            std::cout << "[BF2042Integration] Structure validation failed, but continuing..." << std::endl;
            std::cout << "[BF2042Integration] Will attempt runtime validation" << std::endl;
        } else {
            std::cout << "[BF2042Integration] Structure validation successful!" << std::endl;
        }

        isInitialized = true;
        std::cout << "[+] ✅ BF2042 integration initialized successfully!" << std::endl;
        PrintDiagnostics();

        return TRUE;
    }

    // ===== CORE ADDRESS RESOLUTION =====

    QWORD BF2042Integration::GetGameContext() {
        DebugLog("[BF2042Integration] GetGameContext() called");

        QWORD cached = GetCachedAddress("GameContext");
        if (cached != 0) {
            DebugLog("[BF2042Integration] Returning cached GameContext: 0x" + std::to_string(cached));

            // FORCE structure dump for CR3/PML4 analysis
            static bool structureDumped = false;
            if (!structureDumped) {
                DebugLog("[BF2042Integration] === FORCED GAMECONTEXT STRUCTURE DUMP FOR CR3 ANALYSIS ===");
                for (int i = 0; i < 32; i++) {
                    QWORD value = 0;
                    QWORD offset = i * 8;
                    if (SafeRead(cached + offset, value)) {
                        DebugLog("[BF2042Integration] GameContext+0x" + std::to_string(offset) + " = 0x" + std::to_string(value));
                    } else {
                        DebugLog("[BF2042Integration] GameContext+0x" + std::to_string(offset) + " = READ_FAILED");
                    }
                }

                // Analyze the data
                int zeroCount = 0;
                int readFailCount = 0;
                for (int i = 0; i < 32; i++) {
                    QWORD value = 0;
                    if (SafeRead(cached + (i * 8), value)) {
                        if (value == 0) zeroCount++;
                    } else {
                        readFailCount++;
                    }
                }

                DebugLog("[BF2042Integration] CR3/PML4 Analysis: " + std::to_string(zeroCount) + " zeros, " + std::to_string(readFailCount) + " read failures out of 32 QWORDs");

                if (zeroCount >= 24) {
                    DebugLog("[BF2042Integration] *** CR3/PML4 MANIPULATION DETECTED - High zero count ***");
                } else if (readFailCount >= 16) {
                    DebugLog("[BF2042Integration] *** MEMORY PROTECTION DETECTED - High read failure count ***");
                } else {
                    DebugLog("[BF2042Integration] Memory appears to be readable - checking for valid pointers...");
                }

                structureDumped = true;
            }

            return cached;
        }

        // Use signature scanning to find GameContext
        DebugLog("[BF2042Integration] Checking signatures for GameContext...");
        QWORD gameContext = signatures->GetCachedAddress("GameContext");
        if (gameContext != 0) {
            DebugLog("[BF2042Integration] Found GameContext in signatures: 0x" + std::to_string(gameContext));
            if (ValidateGameContext(gameContext)) {
                DebugLog("[BF2042Integration] GameContext validation successful");
                SetCachedAddress("GameContext", gameContext);
                return gameContext;
            } else {
                DebugLog("[BF2042Integration] GameContext validation failed");
            }
        } else {
            DebugLog("[BF2042Integration] GameContext not found in signatures cache");

            // Try to find it now if it wasn't found during initialization
            DebugLog("[BF2042Integration] Attempting fresh GameContext scan...");
            gameContext = signatures->FindGameContext();
            if (gameContext != 0) {
                DebugLog("[BF2042Integration] Fresh scan found GameContext: 0x" + std::to_string(gameContext));
                if (ValidateGameContext(gameContext)) {
                    DebugLog("[BF2042Integration] Fresh GameContext validation successful");
                    SetCachedAddress("GameContext", gameContext);
                    return gameContext;
                } else {
                    DebugLog("[BF2042Integration] Fresh GameContext validation failed");
                }
            } else {
                DebugLog("[BF2042Integration] Fresh GameContext scan failed");
            }
        }

        DebugLog("[BF2042Integration] GameContext not found via signature scanning");
        return 0;
    }

    QWORD BF2042Integration::GetPlayerManager() {
        QWORD cached = GetCachedAddress("PlayerManager");
        if (cached != 0) return cached;

        QWORD gameContext = GetGameContext();
        if (gameContext == 0) return 0;

        QWORD playerManager = 0;

        // First, try normal read
        if (SafeRead(gameContext + BF2042Offsets::GAMECONTEXT_PLAYERMANAGER, playerManager)) {

            // Check for EA anti-cheat corruption
            // EA uses non-canonical addresses and null pointers as corruption
            bool isNonCanonical = (playerManager > 0x00007FFFFFFFFFFFULL && playerManager < 0xFFFF800000000000ULL);
            bool isNull = (playerManager == 0);

            // Check for any corruption (non-canonical addresses or null)
            if (isNonCanonical || isNull) {
                std::cout << "[BF2042Integration] ❌ DETECTED EA ANTI-CHEAT CORRUPTION!" << std::endl;
                std::cout << "[BF2042Integration] PlayerManager corrupted value: 0x" << std::hex << playerManager << std::dec << std::endl;
                std::cout << "[BF2042Integration] 🎯 ACTIVATING DYNAMIC CR3 BYPASS..." << std::endl;

                // Set corruption flag for external systems to detect
                SetCachedAddress("CorruptionDetected", 1);

                std::cout << "[BF2042Integration] ⚠️ Corruption detected - Dynamic CR3 bypass activation signaled" << std::endl;
                return 0; // Return 0 to signal corruption, let Dynamic CR3 Manager handle it
            }

            // Normal validation for non-corrupted values
            if (ValidatePlayerManager(playerManager)) {
                SetCachedAddress("PlayerManager", playerManager);
                return playerManager;
            }
        }

        return 0;
    }

    bool BF2042Integration::IsCorruptionDetected() {
        return GetCachedAddress("CorruptionDetected") != 0;
    }

    QWORD BF2042Integration::GetClientPlayerArray() {
        QWORD cached = GetCachedAddress("ClientPlayerArray");
        if (cached != 0) return cached;

        QWORD playerManager = GetPlayerManager();
        if (playerManager == 0) return 0;

        QWORD clientPlayerArray = 0;
        if (SafeRead(playerManager + BF2042Offsets::PLAYERMANAGER_CLIENTARRAY, clientPlayerArray) &&
            IsCanonicalAddress(clientPlayerArray)) {
            SetCachedAddress("ClientPlayerArray", clientPlayerArray);
            return clientPlayerArray;
        }

        return 0;
    }

    QWORD BF2042Integration::GetLocalPlayer() {
        QWORD cached = GetCachedAddress("LocalPlayer");
        if (cached != 0) return cached;

        QWORD playerManager = GetPlayerManager();
        if (playerManager == 0) return 0;

        QWORD localPlayer = 0;
        if (SafeRead(playerManager + BF2042Offsets::PLAYERMANAGER_LOCALPLAYER, localPlayer) &&
            IsCanonicalAddress(localPlayer)) {
            SetCachedAddress("LocalPlayer", localPlayer);
            return localPlayer;
        }

        return 0;
    }

    QWORD BF2042Integration::GetGameRenderer() {
        QWORD cached = GetCachedAddress("GameRenderer");
        if (cached != 0) return cached;

        QWORD gameContext = GetGameContext();
        if (gameContext == 0) return 0;

        QWORD gameRenderer = 0;
        if (SafeRead(gameContext + BF2042Offsets::GAMECONTEXT_GAMERENDERER, gameRenderer) &&
            IsCanonicalAddress(gameRenderer)) {
            SetCachedAddress("GameRenderer", gameRenderer);
            return gameRenderer;
        }

        return 0;
    }

    // ===== ENHANCED STRUCTURE ACCESS =====

    QWORD BF2042Integration::GetPlayerHealthComponent(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        // Use enhanced offset (forum confirmed)
        QWORD healthComponent = 0;
        if (SafeRead(clientSoldier + BF2042Offsets::HEALTHCOMPONENT_ENHANCED, healthComponent)) {
            return healthComponent;
        }
        
        // Fallback to original offset
        if (SafeRead(clientSoldier + BF2042Offsets::CLIENTSOLDIER_HEALTHCOMPONENT, healthComponent)) {
            return healthComponent;
        }
        
        return 0;
    }

    QWORD BF2042Integration::GetPlayerWeaponsComponent(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        // Use enhanced offset (forum confirmed)
        QWORD weaponsComponent = 0;
        if (SafeRead(clientSoldier + BF2042Offsets::WEAPONSCOMPONENT_ENHANCED, weaponsComponent)) {
            return weaponsComponent;
        }
        
        // Fallback to original offset
        if (SafeRead(clientSoldier + BF2042Offsets::CLIENTSOLDIER_WEAPONSCOMPONENT, weaponsComponent)) {
            return weaponsComponent;
        }
        
        return 0;
    }

    QWORD BF2042Integration::GetPlayerSpottingComponent(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        QWORD spottingComponent = 0;
        if (SafeRead(clientSoldier + BF2042Offsets::SPOTTINGCOMPONENT_ENHANCED, spottingComponent)) {
            return spottingComponent;
        }
        
        return 0;
    }

    QWORD BF2042Integration::GetPlayerBoneCollision(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        QWORD boneCollision = 0;
        if (SafeRead(clientSoldier + BF2042Offsets::BONECOLLISION_ENHANCED, boneCollision)) {
            return boneCollision;
        }
        
        return 0;
    }

    QWORD BF2042Integration::GetPlayerMeshOverlay(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        QWORD meshOverlay = 0;
        if (SafeRead(clientSoldier + BF2042Offsets::MESHOVERLAY_ENHANCED, meshOverlay)) {
            return meshOverlay;
        }
        
        return 0;
    }

    QWORD BF2042Integration::GetPlayerPosition(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0;
        
        return clientSoldier + BF2042Offsets::CLIENTSOLDIER_POSITION;
    }

    // ===== VISIBILITY SYSTEM =====

    VisibilityState BF2042Integration::GetPlayerVisibility(QWORD clientSoldier) {
        QWORD spottingComponent = GetPlayerSpottingComponent(clientSoldier);
        if (spottingComponent == 0) return VisibilityState::OCCLUDED;
        
        // Read SpottingTargetData
        QWORD spottingData = 0;
        if (!SafeRead(spottingComponent, spottingData) || spottingData == 0) {
            return VisibilityState::OCCLUDED;
        }
        
        // Read occludedType at offset 0xB9
        BYTE occludedType = 0;
        if (SafeRead(spottingData + BF2042Offsets::SPOTTING_OCCLUDEDTYPE, occludedType)) {
            if (occludedType == static_cast<BYTE>(VisibilityState::VISIBLE)) {
                return VisibilityState::VISIBLE;
            }
        }
        
        return VisibilityState::OCCLUDED;
    }

    SpotType BF2042Integration::GetPlayerSpotType(QWORD clientSoldier) {
        QWORD spottingComponent = GetPlayerSpottingComponent(clientSoldier);
        if (spottingComponent == 0) return SpotType::PASSIVE_RADAR;
        
        // Read spot type from SpottingTargetComponent
        BYTE spotType = 0;
        if (SafeRead(spottingComponent + 0x10, spotType)) {  // Spot type offset
            return static_cast<SpotType>(spotType);
        }
        
        return SpotType::PASSIVE_RADAR;
    }

    bool BF2042Integration::IsPlayerPACScanned(QWORD clientSoldier) {
        QWORD meshOverlay = GetPlayerMeshOverlay(clientSoldier);
        if (meshOverlay == 0) return false;
        
        // Check PAC scan flag at MeshOverlayComponent + 0x50
        BYTE pacScanFlag = 0;
        if (SafeRead(meshOverlay + BF2042Offsets::MESHOVERLAY_PACSCAN, pacScanFlag)) {
            return pacScanFlag == static_cast<BYTE>(PACScanFlags::ACTIVELY_SCANNED);
        }
        
        return false;
    }

    // ===== WEAPON SYSTEM =====

    QWORD BF2042Integration::GetPlayerWeapon(QWORD clientSoldier) {
        QWORD weaponsComponent = GetPlayerWeaponsComponent(clientSoldier);
        if (weaponsComponent == 0) return 0;
        
        // Follow weapon chain: WeaponsComponent -> 0x4AB8 -> 0x38 = ClientWeapon
        QWORD weaponPtr1 = 0;
        if (!SafeRead(weaponsComponent + BF2042Offsets::WEAPONS_CLIENTWEAPON, weaponPtr1) || weaponPtr1 == 0) {
            return 0;
        }
        
        QWORD clientWeapon = 0;
        if (SafeRead(weaponPtr1 + BF2042Offsets::CLIENTWEAPON_OFFSET, clientWeapon)) {
            return clientWeapon;
        }
        
        return 0;
    }

    float BF2042Integration::GetWeaponBulletSpeed(QWORD weapon) {
        if (weapon == 0) return 0.0f;
        
        // Follow chain: ClientWeapon -> 0x3CF8 = WeaponFiring -> 0x130 = WeaponFiringData -> 0x20 -> 0x208 = BulletSpeed
        QWORD weaponFiring = 0;
        if (!SafeRead(weapon + BF2042Offsets::WEAPONFIRING_OFFSET, weaponFiring) || weaponFiring == 0) {
            return 0.0f;
        }
        
        QWORD firingData = 0;
        if (!SafeRead(weaponFiring + BF2042Offsets::FIRINGDATA_OFFSET, firingData) || firingData == 0) {
            return 0.0f;
        }
        
        QWORD bulletSpeedPtr = 0;
        if (!SafeRead(firingData + BF2042Offsets::BULLETSPEED_CHAIN, bulletSpeedPtr) || bulletSpeedPtr == 0) {
            return 0.0f;
        }
        
        float bulletSpeed = 0.0f;
        if (SafeRead(bulletSpeedPtr + BF2042Offsets::BULLETSPEED_OFFSET, bulletSpeed)) {
            return bulletSpeed;
        }
        
        return 0.0f;
    }

    Vector3 BF2042Integration::GetWeaponRecoil(QWORD weapon) {
        Vector3 recoil;
        if (weapon == 0) return recoil;
        
        // Follow chain: WeaponFiringData -> 0x18 -> 0xA0 = RecoilVector3
        QWORD weaponFiring = 0;
        if (!SafeRead(weapon + BF2042Offsets::WEAPONFIRING_OFFSET, weaponFiring) || weaponFiring == 0) {
            return recoil;
        }
        
        QWORD firingData = 0;
        if (!SafeRead(weaponFiring + BF2042Offsets::FIRINGDATA_OFFSET, firingData) || firingData == 0) {
            return recoil;
        }
        
        QWORD recoilPtr = 0;
        if (!SafeRead(firingData + BF2042Offsets::RECOIL_CHAIN, recoilPtr) || recoilPtr == 0) {
            return recoil;
        }
        
        SafeRead(recoilPtr + BF2042Offsets::RECOIL_OFFSET, recoil);
        return recoil;
    }

    Vector3 BF2042Integration::GetSoldierVelocity(QWORD clientSoldier) {
        Vector3 velocity;
        if (clientSoldier == 0) return velocity;

        QWORD weaponsComponent = GetPlayerWeaponsComponent(clientSoldier);
        if (weaponsComponent == 0) return velocity;

        // Get velocity from WeaponFiring + 0x68 + 0x148
        QWORD weaponFiring = 0;
        if (!SafeRead(weaponsComponent + BF2042Offsets::WEAPONS_CLIENTWEAPON, weaponFiring) || weaponFiring == 0) {
            return velocity;
        }

        QWORD velocityPtr = 0;
        if (!SafeRead(weaponFiring + BF2042Offsets::WEAPONFIRING_VELOCITY, velocityPtr) || velocityPtr == 0) {
            return velocity;
        }

        SafeRead(velocityPtr + BF2042Offsets::VELOCITY_OFFSET, velocity);
        return velocity;
    }

    float BF2042Integration::GetAuthoritativeYaw(QWORD clientSoldier) {
        if (clientSoldier == 0) return 0.0f;

        float yaw = 0.0f;
        SafeRead(clientSoldier + BF2042Offsets::CLIENTSOLDIER_YAW, yaw);
        return yaw;
    }

    // ===== VEHICLE SYSTEM =====

    QWORD BF2042Integration::GetPlayerVehicle(QWORD clientPlayer) {
        if (clientPlayer == 0) return 0;

        QWORD vehicle = 0;
        if (SafeRead(clientPlayer + BF2042Offsets::CLIENTPLAYER_VEHICLE, vehicle)) {
            return vehicle;
        }

        return 0;
    }

    bool BF2042Integration::IsPlayerInVehicle(QWORD clientPlayer) {
        return GetPlayerVehicle(clientPlayer) != 0;
    }

    float BF2042Integration::GetVehicleHealth(QWORD vehicle) {
        if (vehicle == 0) return 0.0f;

        float health = 0.0f;
        SafeRead(vehicle + BF2042Offsets::VEHICLE_HEALTH, health);
        return health;
    }

    float BF2042Integration::GetVehicleMaxHealth(QWORD vehicle) {
        if (vehicle == 0) return 0.0f;

        float maxHealth = 0.0f;
        SafeRead(vehicle + BF2042Offsets::VEHICLE_MAXHEALTH, maxHealth);
        return maxHealth;
    }

    // ===== BONE SYSTEM =====

    QWORD BF2042Integration::GetBoneTransforms(QWORD clientSoldier) {
        QWORD boneCollision = GetPlayerBoneCollision(clientSoldier);
        if (boneCollision == 0) return 0;

        QWORD updatePoseResult = 0;
        if (SafeRead(boneCollision + BF2042Offsets::BONECOLLISION_UPDATEPOSE, updatePoseResult)) {
            return updatePoseResult;
        }

        return 0;
    }

    bool BF2042Integration::ForceBoneUpdate(QWORD clientSoldier) {
        // This would require writing to memory, which is dangerous with EAAC
        // For now, just return true as bones should update automatically
        return true;
    }

    // ===== W2S MATRIX SYSTEM =====

    QWORD BF2042Integration::GetViewMatrix() {
        QWORD gameRenderer = GetGameRenderer();
        if (gameRenderer == 0) return 0;

        QWORD renderView = 0;
        if (!SafeRead(gameRenderer + BF2042Offsets::GAMERENDERER_RENDERVIEW, renderView) || renderView == 0) {
            return 0;
        }

        return renderView + BF2042Offsets::RENDERVIEW_MATRIX;
    }

    QWORD BF2042Integration::GetRenderView() {
        QWORD gameRenderer = GetGameRenderer();
        if (gameRenderer == 0) return 0;

        QWORD renderView = 0;
        if (SafeRead(gameRenderer + BF2042Offsets::GAMERENDERER_RENDERVIEW, renderView)) {
            return renderView;
        }

        return 0;
    }

    // ===== ANTI-CHEAT BYPASS =====

    bool BF2042Integration::TryFixCR3() {
        if (!vmmManager) return false;

        std::cout << "[+] Attempting advanced CR3/DTB bypass for EAAC..." << std::endl;

        // Use the enhanced CR3 fix from VmmManager
        if (vmmManager->FixCR3Advanced(targetPID)) {
            std::cout << "[+] ✅ CR3 bypass successful!" << std::endl;
            return true;
        }

        std::cout << "[!] CR3 bypass failed, memory access may be limited" << std::endl;
        return false;
    }

    bool BF2042Integration::ValidateMemoryAccess() {
        if (!vmmManager) return false;

        // Test basic memory access
        QWORD testValue = 0;
        QWORD gameContext = GetGameContext();
        if (gameContext == 0) return false;

        return SafeRead(gameContext, testValue);
    }

    std::vector<QWORD> BF2042Integration::GetOrphanedDTBs() {
        if (!vmmManager) return {};

        return vmmManager->GetOrphanedDTBs();
    }

    bool BF2042Integration::ValidateDTBForProcess(DWORD pid, QWORD dtb) {
        if (!vmmManager) return false;

        return vmmManager->ValidateDTBForProcess(pid, dtb);
    }

    // ===== VALIDATION METHODS =====

    bool BF2042Integration::ValidateGameContext(QWORD address) {
        DebugLog("[BF2042Integration] Validating GameContext at 0x" + std::to_string(address));

        if (!IsCanonicalAddress(address)) {
            DebugLog("[BF2042Integration] GameContext address is not canonical");
            return false;
        }

        // ALWAYS read and dump GameContext structure data to check for CR3/PML4 manipulation
        DebugLog("[BF2042Integration] Reading GameContext structure data:");
        for (int i = 0; i < 16; i++) {
            QWORD value = 0;
            QWORD offset = i * 8;
            if (SafeRead(address + offset, value)) {
                DebugLog("[BF2042Integration] GameContext+0x" + std::to_string(offset) + " = 0x" + std::to_string(value));
            } else {
                DebugLog("[BF2042Integration] GameContext+0x" + std::to_string(offset) + " = READ_FAILED");
            }
        }

        // Check if we're getting consistent zeros (sign of CR3 manipulation)
        int zeroCount = 0;
        int readFailCount = 0;
        for (int i = 0; i < 16; i++) {
            QWORD value = 0;
            if (SafeRead(address + (i * 8), value)) {
                if (value == 0) zeroCount++;
            } else {
                readFailCount++;
            }
        }

        DebugLog("[BF2042Integration] GameContext analysis: " + std::to_string(zeroCount) + " zeros, " + std::to_string(readFailCount) + " read failures out of 16 QWORDs");

        if (zeroCount >= 12) {
            DebugLog("[BF2042Integration] WARNING: High zero count (" + std::to_string(zeroCount) + "/16) - possible CR3/PML4 manipulation detected");
        }

        if (readFailCount >= 8) {
            DebugLog("[BF2042Integration] WARNING: High read failure count (" + std::to_string(readFailCount) + "/16) - possible memory protection");
        }

        // Validate by checking PlayerManager pointer
        QWORD playerManager = 0;
        if (!SafeRead(address + BF2042Offsets::GAMECONTEXT_PLAYERMANAGER, playerManager)) {
            DebugLog("[BF2042Integration] Failed to read PlayerManager from GameContext+0x" + std::to_string(BF2042Offsets::GAMECONTEXT_PLAYERMANAGER));
            return false;
        }

        DebugLog("[BF2042Integration] PlayerManager read as: 0x" + std::to_string(playerManager));
        DebugLog("[BF2042Integration] PlayerManager offset used: 0x" + std::to_string(BF2042Offsets::GAMECONTEXT_PLAYERMANAGER));

        if (!IsCanonicalAddress(playerManager)) {
            DebugLog("[BF2042Integration] PlayerManager address is not canonical");
            return false;
        }

        DebugLog("[BF2042Integration] GameContext validation successful");
        return true;
    }

    bool BF2042Integration::ValidatePlayerManager(QWORD address) {
        if (!IsCanonicalAddress(address)) return false;

        // Validate by checking LocalPlayer and ClientPlayerArray pointers
        QWORD localPlayer = 0;
        QWORD clientPlayerArray = 0;

        if (!SafeRead(address + BF2042Offsets::PLAYERMANAGER_LOCALPLAYER, localPlayer) ||
            !SafeRead(address + BF2042Offsets::PLAYERMANAGER_CLIENTARRAY, clientPlayerArray)) {
            return false;
        }

        return IsCanonicalAddress(localPlayer) && IsCanonicalAddress(clientPlayerArray);
    }

    bool BF2042Integration::ValidateClientSoldier(QWORD address) {
        if (!IsCanonicalAddress(address)) return false;

        // Validate by checking health component
        QWORD healthComponent = GetPlayerHealthComponent(address);
        return IsCanonicalAddress(healthComponent);
    }

    bool BF2042Integration::ValidateAllStructures() {
        std::cout << "[+] Validating all BF2042 structures..." << std::endl;

        QWORD gameContext = GetGameContext();
        if (!ValidateGameContext(gameContext)) {
            std::cout << "[-] GameContext validation failed" << std::endl;
            return false;
        }

        QWORD playerManager = GetPlayerManager();
        if (!ValidatePlayerManager(playerManager)) {
            std::cout << "[-] PlayerManager validation failed" << std::endl;
            return false;
        }

        std::cout << "[+] ✅ All structures validated successfully" << std::endl;
        return true;
    }

    // ===== ENHANCED PLAYER LOOP =====

    int BF2042Integration::EnhancedPlayerLoop(int maxPlayers) {
        if (!isInitialized) {
            std::cout << "[-] BF2042Integration not initialized" << std::endl;
            return 0;
        }

        QWORD clientPlayerArray = GetClientPlayerArray();
        if (clientPlayerArray == 0) return 0;

        // Get player list pointer
        QWORD playerListPtr = 0;
        if (!SafeRead(clientPlayerArray + 0x18, playerListPtr) || playerListPtr == 0) {
            return 0;
        }

        int validPlayers = 0;
        for (int i = 0; i < maxPlayers; i++) {
            QWORD playerPtr = GetPlayerByIndex(i);
            if (playerPtr == 0) continue;

            // Get soldier entity
            QWORD soldierEntity = 0;
            if (!SafeRead(playerPtr + BF2042Offsets::CLIENTPLAYER_SOLDIER, soldierEntity) || soldierEntity == 0) {
                continue;
            }

            // Validate soldier
            if (!ValidateClientSoldier(soldierEntity)) continue;

            validPlayers++;

            // Example enhanced processing:
            // - Check visibility state
            VisibilityState visibility = GetPlayerVisibility(soldierEntity);

            // - Check PAC scan status
            bool isPACScanned = IsPlayerPACScanned(soldierEntity);

            // - Get weapon info
            QWORD weapon = GetPlayerWeapon(soldierEntity);
            float bulletSpeed = GetWeaponBulletSpeed(weapon);

            // - Check vehicle status
            bool inVehicle = IsPlayerInVehicle(playerPtr);

            // Your ESP processing code would go here
            // Example: draw ESP, calculate aimbot data, etc.
        }

        return validPlayers;
    }

    QWORD BF2042Integration::GetPlayerByIndex(int playerIndex) {
        QWORD clientPlayerArray = GetClientPlayerArray();
        if (clientPlayerArray == 0) return 0;

        // Get player list pointer
        QWORD playerListPtr = 0;
        if (!SafeRead(clientPlayerArray + 0x18, playerListPtr) || playerListPtr == 0) {
            return 0;
        }

        // Calculate player address: PlayerList + (index * 0x8)
        QWORD playerPtr = 0;
        if (SafeRead(playerListPtr + (playerIndex * 0x8), playerPtr)) {
            return playerPtr;
        }

        return 0;
    }

    int BF2042Integration::BatchReadPlayers(const std::vector<int>& playerIndices, std::vector<QWORD>& playerData) {
        playerData.clear();
        playerData.reserve(playerIndices.size());

        int successCount = 0;
        for (int index : playerIndices) {
            QWORD playerPtr = GetPlayerByIndex(index);
            playerData.push_back(playerPtr);
            if (playerPtr != 0) successCount++;
        }

        return successCount;
    }

    // ===== FALLBACK COMPATIBILITY =====

    void BF2042Integration::SetFallbackAddresses(QWORD gameContext, QWORD playerMgr) {
        std::cout << "[+] Setting fallback addresses for compatibility..." << std::endl;

        if (gameContext != 0) {
            SetCachedAddress("GameContext", gameContext);
            std::cout << "[+] Fallback GameContext: 0x" << std::hex << gameContext << std::endl;
        }

        if (playerMgr != 0) {
            SetCachedAddress("PlayerManager", playerMgr);
            std::cout << "[+] Fallback PlayerManager: 0x" << std::hex << playerMgr << std::endl;
        }
    }

    bool BF2042Integration::IsSignatureScanningWorking() {
        return isInitialized && signatures != nullptr && GetGameContext() != 0;
    }

    void BF2042Integration::RefreshAddresses() {
        std::cout << "[+] Refreshing all cached addresses..." << std::endl;
        InvalidateCache();
        ScanForAddresses();
    }

    // ===== DIAGNOSTICS AND PERFORMANCE =====

    void BF2042Integration::PrintDiagnostics() {
        std::cout << "\n===== BF2042 Integration Diagnostics =====" << std::endl;
        std::cout << "Initialized: " << (isInitialized ? "✅ Yes" : "❌ No") << std::endl;
        std::cout << "Signature Scanning: " << (IsSignatureScanningWorking() ? "✅ Working" : "❌ Failed") << std::endl;
        std::cout << "Memory Access: " << (ValidateMemoryAccess() ? "✅ Stable" : "❌ Unstable") << std::endl;

        QWORD gameContext = GetGameContext();
        QWORD playerManager = GetPlayerManager();
        QWORD clientPlayerArray = GetClientPlayerArray();
        QWORD localPlayer = GetLocalPlayer();

        std::cout << "\n--- Core Addresses ---" << std::endl;
        std::cout << "GameContext:       0x" << std::hex << gameContext << std::endl;
        std::cout << "PlayerManager:     0x" << std::hex << playerManager << std::endl;
        std::cout << "ClientPlayerArray: 0x" << std::hex << clientPlayerArray << std::endl;
        std::cout << "LocalPlayer:       0x" << std::hex << localPlayer << std::endl;

        std::cout << "\n--- Structure Validation ---" << std::endl;
        std::cout << "GameContext Valid: " << (ValidateGameContext(gameContext) ? "✅" : "❌") << std::endl;
        std::cout << "PlayerManager Valid: " << (ValidatePlayerManager(playerManager) ? "✅" : "❌") << std::endl;

        std::cout << "\n--- Anti-Cheat Status ---" << std::endl;
        std::cout << "CR3 Bypass: " << (ValidateMemoryAccess() ? "✅ Working" : "❌ May be needed") << std::endl;

        std::cout << "==========================================\n" << std::endl;
    }

    std::string BF2042Integration::GetPerformanceStats() {
        std::stringstream ss;
        ss << "BF2042 Integration Performance:\n";
        ss << "  Initialized: " << (isInitialized ? "Yes" : "No") << "\n";
        ss << "  Signature Scanning: " << (IsSignatureScanningWorking() ? "Working" : "Failed") << "\n";
        ss << "  Memory Access: " << (ValidateMemoryAccess() ? "Stable" : "Unstable") << "\n";
        ss << "  Cache Entries: " << addressCache.size() << "\n";

        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastScan = std::chrono::duration_cast<std::chrono::seconds>(now - lastScanTime).count();
        ss << "  Last Scan: " << timeSinceLastScan << " seconds ago\n";

        return ss.str();
    }

    bool BF2042Integration::DumpMemoryRegion(QWORD address, SIZE_T size, const char* filePath) {
        if (!vmmManager) return false;

        return vmmManager->DumpProcessMemory(targetPID, address, size, filePath);
    }

    // ===== PRIVATE HELPER METHODS =====

    void BF2042Integration::CacheAddresses() {
        // This method is called automatically during scanning
    }

    bool BF2042Integration::ScanForAddresses() {
        std::cout << "[+] Scanning for BF2042 base addresses..." << std::endl;

        // Get GameContext from signature scanning
        QWORD gameContext = signatures->GetCachedAddress("GameContext");
        if (gameContext == 0) {
            std::cout << "[-] Failed to find GameContext via signatures" << std::endl;
            return false;
        }

        SetCachedAddress("GameContext", gameContext);
        std::cout << "[+] ✅ GameContext found: 0x" << std::hex << gameContext << std::endl;

        return true;
    }

    bool BF2042Integration::ValidateAddressCache() {
        auto now = std::chrono::steady_clock::now();

        for (auto& [key, cached] : addressCache) {
            auto age = std::chrono::duration_cast<std::chrono::minutes>(now - cached.lastValidated);
            if (age > CACHE_VALIDITY_DURATION) {
                cached.isValid = false;
                cached.validationFailures++;

                if (cached.validationFailures > MAX_VALIDATION_FAILURES) {
                    std::cout << "[!] Cache entry '" << key << "' exceeded max failures, invalidating" << std::endl;
                    cached.address = 0;
                }
            }
        }

        return true;
    }

    void BF2042Integration::InvalidateCache() {
        addressCache.clear();
        std::cout << "[+] Address cache invalidated" << std::endl;
    }

    bool BF2042Integration::IsCanonicalAddress(QWORD address) {
        // Check if address is a canonical user-mode pointer
        return (address > 0x10000 && address < 0x7FFFFFFFFFFF);
    }

    bool BF2042Integration::ValidateStructureChain(QWORD baseAddress, const std::vector<QWORD>& offsets) {
        QWORD currentAddress = baseAddress;

        for (QWORD offset : offsets) {
            if (!IsCanonicalAddress(currentAddress)) return false;

            QWORD nextAddress = 0;
            if (!SafeRead(currentAddress + offset, nextAddress)) return false;

            currentAddress = nextAddress;
        }

        return IsCanonicalAddress(currentAddress);
    }



    bool BF2042Integration::SafeReadBytes(QWORD address, void* buffer, SIZE_T size) {
        if (!vmmManager || !IsCanonicalAddress(address) || !buffer) return false;

        return vmmManager->readMemory(targetPID, address, buffer, size);
    }

    QWORD BF2042Integration::GetCachedAddress(const std::string& key) {
        auto it = addressCache.find(key);
        if (it == addressCache.end()) return 0;

        auto& cached = it->second;
        auto now = std::chrono::steady_clock::now();
        auto age = std::chrono::duration_cast<std::chrono::minutes>(now - cached.lastValidated);

        if (age > CACHE_VALIDITY_DURATION || !cached.isValid) {
            return 0;
        }

        return cached.address;
    }

    void BF2042Integration::SetCachedAddress(const std::string& key, QWORD address) {
        auto now = std::chrono::steady_clock::now();
        addressCache[key] = { address, now, true, 0 };
    }

    // Template instantiations
    template bool BF2042Integration::SafeRead<QWORD>(QWORD address, QWORD& value);
    template bool BF2042Integration::SafeRead<DWORD>(QWORD address, DWORD& value);
    template bool BF2042Integration::SafeRead<BYTE>(QWORD address, BYTE& value);
    template bool BF2042Integration::SafeRead<float>(QWORD address, float& value);
    template bool BF2042Integration::SafeRead<Vector3>(QWORD address, Vector3& value);

} // namespace DMAMem

// Template implementation
namespace DMAMem {
    template<typename T>
    bool BF2042Integration::SafeRead(QWORD address, T& value) {
        if (!vmmManager || !IsCanonicalAddress(address)) return false;
        return vmmManager->readMemory(targetPID, address, &value, sizeof(T));
    }
}
