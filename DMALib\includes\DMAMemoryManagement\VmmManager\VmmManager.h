#pragma once
#include <iostream>
#include <Windows.h>
#include <stdio.h>
#include <conio.h>
#include <chrono>
#include <thread>
#include <atomic>
#include <vector>
#include <functional>
#include <string>
#include <sstream>
#include <fstream>
#include <memory>
#include "../MemProcFS//leechcore.h"
#include "../MemProcFS/vmmdll.h"
#include "../../DMARender/includes.h"  // Fixed include path for Vector3
#pragma comment(lib, "leechcore")
#pragma comment(lib, "vmm")

namespace DMAMem {
	class VmmManager {
	private:
			VMM_HANDLE hVMM;
			VMM_HANDLE initialize();
				// Persistent buffer for patched vmmdll cached PML4 handshake
				QWORD m_cachedPml4[512] = { 0 };

			// ===== DMA REFRESH LOOP CONTROL =====
			// These variables control the manual refresh loop to prevent 5-second freezes
			std::atomic<bool> wants_refresh = true;
			std::atomic<bool> wants_full_refresh = true;
			std::atomic<bool> wants_no_refresh = false;
			std::atomic<bool> manual_refresh = true;
			std::thread refreshThread;
			std::atomic<bool> refreshThreadRunning = false;

			// Simple configurable intervals (in microseconds)
			std::atomic<uint64_t> fastRefreshUs{3000000};    // 3 seconds (default)
			std::atomic<uint64_t> mediumRefreshUs{5000000};  // 5 seconds (default)
			std::atomic<uint64_t> slowRefreshUs{10000000};   // 10 seconds (default)
			std::atomic<uint64_t> memRefreshUs{15000000};    // 15 seconds (default)
			std::atomic<uint64_t> sleepMs{2000};             // 2 seconds (default)

			// Helper function to get current time in microseconds
			uint64_t GetCurrentTime() {
				auto now = std::chrono::high_resolution_clock::now();
				auto duration = now.time_since_epoch();
				return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
			}

			// The critical refresh loop that prevents DMA freezes
			void RefreshLoop();

			// Performance monitoring
			struct PerformanceStats {
				uint64_t totalReads = 0;
				uint64_t successfulReads = 0;
				uint64_t failedReads = 0;
				double averageReadTime = 0.0;
				uint64_t lastResetTime = 0;
			};

			PerformanceStats performanceStats;

	public:
			// Constructor with option to disable refresh loop for maximum performance
			VmmManager(bool enableRefreshLoop = true) {
				manual_refresh = enableRefreshLoop;
			}

			VMM_HANDLE getVmm();
			BOOL readMemory(DWORD pid, QWORD remoteAddress, void* destination, int size, ULONG64 flags = VMMDLL_FLAG_NOCACHE);
			BOOL readMemoryOptimized(DWORD pid, QWORD remoteAddress, void* destination, int size);
			BOOL readMemoryUltraFast(DWORD pid, QWORD remoteAddress, void* destination, int size);

			// CR3 / DTB control and anti-cheat bypass helpers
			BOOL readMemoryAntiCheat(DWORD pid, QWORD remoteAddress, void* destination, int size);
			BOOL readMemoryWithCR3Bypass(DWORD pid, QWORD remoteAddress, void* destination, int size);
			BOOL detectAndBypassCR3Shuffle(DWORD pid);
			QWORD findCorrectCR3(DWORD pid);
			BOOL validateMemoryAccess(DWORD pid, QWORD address);
			// Explicitly override per-process DTB (CR3) via MemProcFS config
			BOOL setProcessDTB(DWORD pid, QWORD dtb);
			BOOL clearProcessDTB(DWORD pid);
            // Prime vmmdll with cached PML4 to defeat CR3 shuffling (requires patched vmmdll)
            BOOL primeCachedPML4ForPid(DWORD pid);
            BOOL primeCachedPML4ForPidWithDTB(DWORD pid, QWORD dtb);

			template <typename T> T readMemoryDirect(DWORD pid, QWORD remoteAddress, ULONG64 flags = VMMDLL_FLAG_NOCACHE);
			VMMDLL_SCATTER_HANDLE initializeScatter(DWORD pid);
			VMMDLL_SCATTER_HANDLE initializeScatter(DWORD pid, ULONG64 flags);

                // TLB refresh helpers (public)
                void refreshTLBFull();
                void refreshTLBPartial();


                // PFN-based DTB discovery
                std::vector<QWORD> discoverDTBCandidatesFromPFN(DWORD pid, DWORD maxCandidates = 16);

                // Enhanced CR3 discovery for EA anti-cheat bypass
                std::vector<QWORD> discoverAllCR3Candidates(DWORD pid, DWORD maxCandidates = 32);
                std::vector<QWORD> scanPhysicalMemoryForCR3s(DWORD pid, DWORD maxCandidates = 16);
                std::vector<QWORD> enumerateProcessCR3s(DWORD pid);
                BOOL validateCR3ForProcess(DWORD pid, QWORD cr3);

                // Process DTB query
                BOOL getProcessDTB(DWORD pid, QWORD* paDTB, QWORD* paDTB_UserOpt);

				// ===== SIGNATURE SCANNING METHODS =====
				/**
				 * \brief Scans the process for the signature pattern
				 * \param signature the signature example "48 8B 0D ? ? ? ?" or "48 ? ? ?"
				 * \param range_start Region to start scan from
				 * \param range_end Region up to where it should scan
				 * \param pid Process ID (0 = use current process)
				 * \return address of first signature match, 0 if not found
				 */
				QWORD FindSignature(const char* signature, QWORD range_start, QWORD range_end, DWORD pid = 0);

				/**
				 * \brief Scans for multiple instances of a signature pattern
				 * \param signature the signature pattern
				 * \param range_start Region to start scan from
				 * \param range_end Region up to where it should scan
				 * \param maxResults Maximum number of results to return
				 * \param pid Process ID (0 = use current process)
				 * \return vector of addresses where signature was found
				 */
				std::vector<QWORD> FindAllSignatures(const char* signature, QWORD range_start, QWORD range_end, DWORD maxResults = 10, DWORD pid = 0);

				/**
				 * \brief Scans for signature within a specific module
				 * \param signature the signature pattern
				 * \param moduleName name of the module to scan
				 * \param pid Process ID (0 = use current process)
				 * \return address of signature within module, 0 if not found
				 */
				QWORD FindSignatureInModule(const char* signature, const char* moduleName, DWORD pid = 0);

				// ===== ENHANCED CR3/DTB BYPASS METHODS =====
				/**
				 * \brief Advanced CR3 fix using DTB enumeration and validation
				 * \param pid Process ID to fix CR3 for
				 * \return true if CR3 was successfully fixed
				 */
				BOOL FixCR3Advanced(DWORD pid);

				/**
				 * \brief Gets all orphaned DTBs (PID = 0) that could be valid process DTBs
				 * \return vector of potential DTB values
				 */
				std::vector<QWORD> GetOrphanedDTBs();

				/**
				 * \brief Validates if a DTB works for a specific process
				 * \param pid Process ID to test
				 * \param dtb DTB value to test
				 * \return true if DTB allows successful memory access
				 */
				BOOL ValidateDTBForProcess(DWORD pid, QWORD dtb);

				/**
				 * \brief Dumps process memory region to file for analysis
				 * \param pid Process ID
				 * \param address Start address (should be valid PE header)
				 * \param size Size to dump
				 * \param filePath Path to save dump file
				 * \return true if dump was successful
				 */
				BOOL DumpProcessMemory(DWORD pid, QWORD address, SIZE_T size, const char* filePath);

			void addScatterRead(VMMDLL_SCATTER_HANDLE handle, QWORD remoteAddress, int size, void* destination);
			void executeScatter(VMMDLL_SCATTER_HANDLE handle);

			// ===== DMA REFRESH CONTROL METHODS =====
			void startRefreshLoop();
			void stopRefreshLoop();
			void setRefreshEnabled(bool enabled) { wants_refresh = enabled; }
			void setFullRefreshEnabled(bool enabled) { wants_full_refresh = enabled; }
			void setManualRefreshEnabled(bool enabled) { manual_refresh = enabled; }
			bool isRefreshLoopRunning() const { return refreshThreadRunning; }

			// ===== SIMPLE REFRESH INTERVAL CONTROLS =====
			void setFastRefreshInterval(uint64_t milliseconds) {
				fastRefreshUs = milliseconds * 1000; // Convert to microseconds
			}

			void setMediumRefreshInterval(uint64_t milliseconds) {
				mediumRefreshUs = milliseconds * 1000;
			}

			void setSlowRefreshInterval(uint64_t milliseconds) {
				slowRefreshUs = milliseconds * 1000;
			}

			void setMemoryRefreshInterval(uint64_t milliseconds) {
				memRefreshUs = milliseconds * 1000;
			}

			void setSleepInterval(uint64_t milliseconds) {
				sleepMs = milliseconds;
			}

			// Performance optimization methods
			void setPerformanceMode(bool enabled) {
				if (enabled) {
					// Disable aggressive refreshes for better ESP performance
					wants_full_refresh = false;
					wants_refresh = false;
				} else {
					// Re-enable normal refresh behavior
					wants_full_refresh = true;
					wants_refresh = true;
				}
			}

			// Ultra-performance mode for maximum ESP performance
			void setUltraPerformanceMode(bool enabled) {
				if (enabled) {
					// Completely disable all refreshes for maximum performance
					wants_full_refresh = false;
					wants_refresh = false;
					// Optionally stop the refresh loop entirely
					if (refreshThreadRunning) {
						stopRefreshLoop();
					}
				} else {
					// Re-enable refresh loop
					wants_full_refresh = true;
					wants_refresh = true;
					if (!refreshThreadRunning && manual_refresh) {
						startRefreshLoop();
					}
				}
			}

			// ===== PRESET CONFIGURATIONS =====
			void applyCompetitiveConfig() {
				setFastRefreshInterval(500);    // 0.5 seconds
				setMediumRefreshInterval(1000); // 1 second
				setSlowRefreshInterval(3000);   // 3 seconds
				setMemoryRefreshInterval(8000); // 8 seconds
				setSleepInterval(500);          // 0.5 seconds
			}

			void applyBalancedConfig() {
				setFastRefreshInterval(2000);    // 2 seconds
				setMediumRefreshInterval(4000);  // 4 seconds
				setSlowRefreshInterval(8000);    // 8 seconds
				setMemoryRefreshInterval(12000); // 12 seconds
				setSleepInterval(1500);          // 1.5 seconds
			}

			void applyStabilityConfig() {
				setFastRefreshInterval(1000);   // 1 second
				setMediumRefreshInterval(2000); // 2 seconds
				setSlowRefreshInterval(4000);   // 4 seconds
				setMemoryRefreshInterval(6000); // 6 seconds
				setSleepInterval(800);          // 0.8 seconds
			}

			// ===== CS2-STYLE SCATTER OPTIMIZATION =====
			struct ScatterBatch {
				VMMDLL_SCATTER_HANDLE handle;
				bool isInitialized;
				std::vector<std::pair<QWORD, std::vector<BYTE>>> readResults;
			};

			// CS2-style scatter batch methods
			ScatterBatch createScatterBatch(DWORD pid, ULONG64 flags = VMMDLL_FLAG_FORCECACHE_READ);
			void addScatterBatchRead(ScatterBatch& batch, QWORD remoteAddress, int size, void* destination);
			bool executeScatterBatch(ScatterBatch& batch);
			void cleanupScatterBatch(ScatterBatch& batch);

			// CS2-style entity batch reading (optimized for multiple entities)
			bool readEntityBatch(DWORD pid, const std::vector<QWORD>& entityAddresses, std::vector<DMARender::Vector3>& positions, std::vector<float>& healths) {
				if (entityAddresses.empty()) return false;

				auto batch = createScatterBatch(pid);
				if (!batch.isInitialized) return false;

				// Resize vectors to match entity count
				positions.resize(entityAddresses.size());
				healths.resize(entityAddresses.size());

				// Add all entity reads to the batch
				for (size_t i = 0; i < entityAddresses.size(); i++) {
					addScatterBatchRead(batch, entityAddresses[i] + 0x10, sizeof(DMARender::Vector3), &positions[i]);
					addScatterBatchRead(batch, entityAddresses[i] + 0x20, sizeof(float), &healths[i]);
				}

				// Execute all reads at once (CS2-style optimization)
				bool success = executeScatterBatch(batch);
				cleanupScatterBatch(batch);

				return success;
			}

			// Performance monitoring methods
			PerformanceStats getPerformanceStats() const { return performanceStats; }
			void resetPerformanceStats() {
				performanceStats = PerformanceStats();
				performanceStats.lastResetTime = GetCurrentTime();
			}

			// Destructor to ensure proper cleanup
			~VmmManager();
	};

	template<typename T>
	inline T VmmManager::readMemoryDirect(DWORD pid, QWORD remoteAddress, ULONG64 flags)
	{
		T obj;
		VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)&obj, sizeof(T), NULL, flags);
		return obj;
	}
}
