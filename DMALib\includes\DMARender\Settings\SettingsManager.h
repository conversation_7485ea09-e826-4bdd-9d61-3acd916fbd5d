#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <vector>

namespace DMARender {

    struct AppSettings {
        // Radar Settings
        bool showDeadPlayers = true;
        bool showVehicles = true;
        bool showGrounditems = false;
        bool showBoats = true;
        bool showDeadAnimals = false;
        bool showClothing = false;
        bool showWeapons = true;
        bool showProxyMagazines = true;
        bool showBackpacks = true;
        bool showRare = true;
        bool showFood = true;
        bool showAmmo = true;
        bool showPlayerList = true;
        bool showServerPlayerList = false;
        bool showZombies = false;
        bool showAnimals = false;
        bool showBots = false;                   // Show bots on radar (default: false)
        std::string playername = "Survivor";
        int BlipSize = 7;
        int BlipSize2 = 4;
        int Aimlinelength = 80;
        bool showOptics = true;
        bool showBase = true;
        bool showMelee = true;
        bool showContainer = true;      // Show storage containers on radar
        bool showCooking = true;        // Show cooking items on radar
        bool showCamping = true;        // Show camping items on radar
        bool showStash = true;          // Show stash containers on radar
        bool showCustomContainer = true; // Show custom/modded containers on radar
        int LootDistanceDeadzone = 0;
        bool showExplosives = true;
        int ZombiesBlipSize = 4;
        int AnimalsBlipSize = 5;
        int FontRADAR = 16;
        int FontRADAR2 = 14;
        bool showPlayerNameRadar = true;
        bool showPlayerDistanceRadar = true;
        bool showPlayerHandRadar = true;

        // ESP/FUSER Settings
        bool showDeadPlayersFUSER = true;
        bool showVehiclesFUSER = true;
        bool showGrounditemsFUSER = true;
        bool showBoatsFUSER = true;
        bool showDeadAnimalsFUSER = false;
        bool showClothingFUSER = false;
        bool showWeaponsFUSER = true;
        bool showProxyMagazinesFUSER = true;
        bool showBackpacksFUSER = true;
        bool showRareFUSER = true;
        bool showFoodFUSER = true;
        bool showAmmoFUSER = true;
        bool showZombiesFUSER = false;
        bool showAnimalsFUSER = false;
        int lootDistanceFUSER = 70;
        int ZombieDistanceFUSER = 100;
        bool showPlayerInfoesp = true;
        bool showPlayerNameFUSER = true;
        bool showPlayerDistanceFUSER = true;
        bool showPlayerHandFUSER = true;
        bool showPlayerBoxFUSER = true;
        bool showBotsFUSER = false;              // Show bots in ESP (default: false)
        bool showAnimalBoxFUSER = true;        // Show animal bounding boxes
        bool showAnimalNameFUSER = true;       // Show animal names
        bool showZombieBoxFUSER = true;        // Show zombie bounding boxes
        bool showZombieNameFUSER = true;       // Show zombie names
        bool showZombieDistanceFUSER = true;   // Show zombie distance
        bool showOpticsFUSER = true;
        bool showDebugFUSER = false;

        // Entity distance settings
        int animalMaxDistance = 200;           // Max distance for animals
        int vehicleMaxDistance = 500;          // Max distance for vehicles/cars
        int boatMaxDistance = 500;             // Max distance for boats
        int deadPlayerMaxDistance = 100;       // Max distance for dead players
        int deadAnimalMaxDistance = 100;       // Max distance for dead animals
        int playerMaxDistance = 500;           // Max distance for players
        bool showBaseFUSER = true;
        bool showMeleeFUSER = true;
        bool showExplosivesFUSER = true;
        bool showContainerFUSER = true;      // Show storage containers
        bool showCookingFUSER = true;        // Show cooking items
        bool showCampingFUSER = true;        // Show camping items
        bool showStashFUSER = true;          // Show stash containers
        bool showCustomContainerFUSER = true; // Show custom/modded containers
        int LootDebugDistance = 70;
        bool enableESPDebugLogging = false;  // Enable ESP debug logging
        float espTextSize = 1.0f;
        float baseFontSize = 28.0f; // Base font pixel size when loading fonts (8-72px)

        // Font settings
        int selectedFontIndex = 1; // Default to Calibri (index 1)
        bool fontBold = false;
        bool fontOutline = true;
        bool fontShadow = false;
        float fontAlpha = 1.0f; // Font alpha transparency (0.0 = transparent, 1.0 = opaque)
        float fontShadowOffset = 1.0f; // Font shadow offset distance (0.0 = no offset, 5.0 = large offset)
        float fontOutlineSize = 1.5f; // Font outline thickness (0.0 = no outline, 5.0 = thick outline)
        float fontOutlineColor[4] = {0.0f, 0.0f, 0.0f, 1.0f}; // Font outline color (RGBA: default black)
        
        // Font quality settings for fuser optimization (optimized for small fonts)
        int fontOversampleH = 3; // Reduced from 8 - better for small fonts
        int fontOversampleV = 2; // Reduced from 4 - better for small fonts
        float fontRasterizerMultiply = 1.1f; // Slightly brighter for small font clarity
        bool fontPixelSnap = true; // Enable for crisp small fonts
        int fontAtlasSize = 1024;
        float fontUIBias = 0.0f;
        
        // Counter font sizes
        float fpsCounterFontSize = 20.0f; // FPS and player counter font size
        
        // FPS Counter position settings
        float fpsCounterHorizontalPadding = 485.0f; // Distance from right edge (increase to move left)
        float fpsCounterVerticalPadding = 27.0f;    // Distance from top edge (increase to move down)
        
        // Outline color settings for fuser compatibility
        int outlineColorR = 32;  // Dark gray R component (0-255)
        int outlineColorG = 32;  // Dark gray G component (0-255)
        int outlineColorB = 32;  // Dark gray B component (0-255)
        int outlineColorA = 255; // Full opacity (0-255)

        // Bounding Box settings
        float boundingBoxThickness = 1.5f;
        bool boundingBoxAntiAliasing = true;
        bool boundingBoxRounded = false;
        float boundingBoxRounding = 2.0f;
        int boundingBoxType = 0;  // 0: Normal, 1: Slim, 2: Corner, 3: 3D
        float boundingBoxMaxDistance = 500.0f; // Maximum distance for bounding box rendering

        // Keybind settings
        int itemESPToggleKey = 0x04; // Middle Mouse (scroll wheel click) by default
        bool itemESPEnabled = true;

        // Container Contents settings
        bool showContainerContents = true;  // Enable/disable container contents display
        int containerContentsMaxItems = 15;  // Maximum items to show per container
        float containerContentsMaxDistance = 100.0f; // Maximum distance to show container contents

        // Font size settings
        float playerNameFontSize = 1.0f;  // Player name text size multiplier
        float itemFontSize = 0.9f;        // Item text size multiplier
        float distanceFontSize = 0.8f;    // Distance text size multiplier

        // ===== ESP COLOR CUSTOMIZATION =====

        // Player ESP Colors
        int playerBoxColorR = 255;      // Red
        int playerBoxColorG = 0;        // Green
        int playerBoxColorB = 0;        // Blue
        int playerBoxColorA = 255;      // Alpha

        int playerTextColorR = 255;     // White text (legacy - kept for compatibility)
        int playerTextColorG = 255;
        int playerTextColorB = 255;
        int playerTextColorA = 255;

        // Individual Player Text Colors
        int playerNameColorR = 255;     // White name text
        int playerNameColorG = 255;
        int playerNameColorB = 255;
        int playerNameColorA = 255;

        int playerDistanceColorR = 255; // White distance text
        int playerDistanceColorG = 255;
        int playerDistanceColorB = 255;
        int playerDistanceColorA = 255;

        int playerHandColorR = 255;     // White hand item text
        int playerHandColorG = 255;
        int playerHandColorB = 255;
        int playerHandColorA = 255;

        int playerSkeletonColorR = 255; // White skeleton
        int playerSkeletonColorG = 255;
        int playerSkeletonColorB = 255;
        int playerSkeletonColorA = 255;

        // Zombie ESP Colors
        int zombieBoxColorR = 255;      // Yellow
        int zombieBoxColorG = 255;
        int zombieBoxColorB = 0;
        int zombieBoxColorA = 255;

        int zombieTextColorR = 255;     // Yellow text
        int zombieTextColorG = 255;
        int zombieTextColorB = 0;
        int zombieTextColorA = 255;

        int zombieSkeletonColorR = 255; // Red skeleton
        int zombieSkeletonColorG = 0;
        int zombieSkeletonColorB = 0;
        int zombieSkeletonColorA = 255;

        // Animal ESP Colors
        int animalBoxColorR = 0;        // Green
        int animalBoxColorG = 255;
        int animalBoxColorB = 0;
        int animalBoxColorA = 200;

        int animalTextColorR = 0;       // Green text
        int animalTextColorG = 255;
        int animalTextColorB = 0;
        int animalTextColorA = 255;

        // Dead Player ESP Colors
        int deadPlayerBoxColorR = 0;    // Cyan
        int deadPlayerBoxColorG = 255;
        int deadPlayerBoxColorB = 255;
        int deadPlayerBoxColorA = 255;

        int deadPlayerTextColorR = 0;   // Cyan text
        int deadPlayerTextColorG = 255;
        int deadPlayerTextColorB = 255;
        int deadPlayerTextColorA = 255;

        // Vehicle ESP Colors
        int vehicleBoxColorR = 255;     // Magenta
        int vehicleBoxColorG = 0;
        int vehicleBoxColorB = 245;
        int vehicleBoxColorA = 255;

        int vehicleTextColorR = 255;    // Magenta text
        int vehicleTextColorG = 0;
        int vehicleTextColorB = 245;
        int vehicleTextColorA = 255;

        // Health Bar Colors
        int healthBarHighColorR = 0;    // Green (high health)
        int healthBarHighColorG = 255;
        int healthBarHighColorB = 0;
        int healthBarHighColorA = 255;

        int healthBarMediumColorR = 255; // Yellow (medium health)
        int healthBarMediumColorG = 255;
        int healthBarMediumColorB = 0;
        int healthBarMediumColorA = 255;

        int healthBarLowColorR = 255;   // Red (low health)
        int healthBarLowColorG = 0;
        int healthBarLowColorB = 0;
        int healthBarLowColorA = 255;

        int healthBarBackgroundColorR = 32;  // Dark background
        int healthBarBackgroundColorG = 32;
        int healthBarBackgroundColorB = 32;
        int healthBarBackgroundColorA = 180;

        // Container Contents Colors
        int containerContentsColorR = 200;  // Light blue
        int containerContentsColorG = 200;
        int containerContentsColorB = 255;
        int containerContentsColorA = 255;

        // ===== LOOT CATEGORY ESP COLORS =====

        // Weapon ESP Colors
        int weaponColorR = 255;      // Pink
        int weaponColorG = 0;
        int weaponColorB = 100;
        int weaponColorA = 255;

        // Clothing ESP Colors
        int clothingColorR = 255;    // Yellow
        int clothingColorG = 255;
        int clothingColorB = 0;
        int clothingColorA = 255;

        // Backpack ESP Colors
        int backpackColorR = 175;    // Light green
        int backpackColorG = 255;
        int backpackColorB = 0;
        int backpackColorA = 255;

        // Food ESP Colors
        int foodColorR = 50;         // Dark green
        int foodColorG = 140;
        int foodColorB = 50;
        int foodColorA = 255;

        // Ammo ESP Colors
        int ammoColorR = 255;        // Orange
        int ammoColorG = 117;
        int ammoColorB = 50;
        int ammoColorA = 255;

        // Proxy Magazine ESP Colors
        int proxyMagazineColorR = 255;  // Orange (same as ammo)
        int proxyMagazineColorG = 117;
        int proxyMagazineColorB = 50;
        int proxyMagazineColorA = 255;

        // Optic ESP Colors
        int opticColorR = 0;         // Blue
        int opticColorG = 150;
        int opticColorB = 255;
        int opticColorA = 255;

        // Base Building ESP Colors
        int baseBuildingColorR = 0;  // Blue
        int baseBuildingColorG = 150;
        int baseBuildingColorB = 255;
        int baseBuildingColorA = 255;

        // Melee ESP Colors
        int meleeColorR = 0;         // Blue
        int meleeColorG = 150;
        int meleeColorB = 255;
        int meleeColorA = 255;

        // Explosive ESP Colors
        int explosiveColorR = 255;   // Red-orange
        int explosiveColorG = 0;
        int explosiveColorB = 50;
        int explosiveColorA = 255;

        // Container ESP Colors
        int containerColorR = 255;   // Yellow
        int containerColorG = 255;
        int containerColorB = 0;
        int containerColorA = 255;

        // Cooking ESP Colors
        int cookingColorR = 255;     // Orange
        int cookingColorG = 165;
        int cookingColorB = 0;
        int cookingColorA = 255;

        // Camping ESP Colors
        int campingColorR = 0;       // Cyan
        int campingColorG = 255;
        int campingColorB = 255;
        int campingColorA = 255;

        // Stash ESP Colors
        int stashColorR = 128;       // Purple
        int stashColorG = 0;
        int stashColorB = 128;
        int stashColorA = 255;

        // Custom Container ESP Colors
        int customContainerColorR = 255;  // Bright orange
        int customContainerColorG = 140;
        int customContainerColorB = 0;
        int customContainerColorA = 255;

        // Ground Item ESP Colors
        int groundItemColorR = 255;  // White
        int groundItemColorG = 255;
        int groundItemColorB = 255;
        int groundItemColorA = 255;

        // UI Settings
        bool followPlayerEnabled = false;

        // Player Exclusion Settings
        std::vector<std::string> excludedPlayerSteamIDs; // Steam IDs of players to exclude from ESP/radar

        // Performance Settings
        bool vSyncEnabled = true; // V-Sync enabled by default

        // Multi-Core Configuration Settings
        int multiCoreProfile = 0; // 0=AUTO_DETECT, 1=MANY_CORE, 2=MEDIUM_CORE, 3=STANDARD, 4=MINIMAL
        bool enableParallelProcessing = true;
        bool enableThreadAffinity = true;
        bool adaptiveTimings = true;
        int maxWorkerThreads = 8;

        // Skeleton ESP Settings (DayZ2-style)
        bool playerSkeletonEnabled = false;
        bool zombieSkeletonEnabled = false;
        float skeletonLineThickness = 0.5f; // DayZ2 default thickness
	float playerSkeletonMaxDistance = 1000.0f; // Default 1000m for players
	float zombieSkeletonMaxDistance = 300.0f; // Default 300m for zombies
	int skeletonDetailLevel = 2; // Default: Medium detail (0=Minimal, 1=Reduced, 2=Medium, 3=Full)

        // Health Bar ESP Settings
        bool playerHealthBarEnabled = false;
        bool zombieHealthBarEnabled = false;
        float healthBarWidth = 40.0f;       // Width of health bar in pixels
        float healthBarHeight = 6.0f;       // Height of health bar in pixels
        float healthBarOffsetY = -10.0f;    // Vertical offset from entity position
        float healthBarMaxDistance = 100.0f; // Maximum distance for health bar rendering
        int healthBarType = 0;               // 0=Health, 1=Blood, 2=Shock
        bool showHealthNumbers = true;       // Show numerical health values
        bool showHealthBar = true;           // Show health bar visual
        bool showAllHealthStats = false;     // Show all health stats (Health, Blood, Shock) // Maximum distance for health bar rendering

        // Personal Health Display settings
        bool showPersonalHealth = false;
        int personalHealthPosition = 0; // 0=TopLeft, 1=TopRight, 2=BottomLeft, 3=BottomRight
        float personalHealthScale = 1.0f;
        bool personalHealthBackground = true;

        // Individual health indicator positioning (0-100% of screen)
        bool useIndividualPositioning = false; // Enable individual positioning mode
        float healthIndicatorX = 5.0f;        // Health indicator X position (0-100%)
        float healthIndicatorY = 5.0f;        // Health indicator Y position (0-100%)
        float bloodIndicatorX = 5.0f;         // Blood indicator X position (0-100%)
        float bloodIndicatorY = 8.0f;         // Blood indicator Y position (0-100%)
        float shockIndicatorX = 5.0f;         // Shock indicator X position (0-100%)
        float shockIndicatorY = 11.0f;        // Shock indicator Y position (0-100%)
        float statusIndicatorX = 5.0f;        // Status indicator X position (0-100%)
        float statusIndicatorY = 14.0f;       // Status indicator Y position (0-100%)

        // Individual health indicator toggles
        bool showHealthIndicator = true;       // Show health percentage indicator
        bool showBloodIndicator = true;        // Show blood level indicator
        bool showShockIndicator = true;        // Show shock level indicator
        bool showStatusIndicator = true;       // Show status text indicator

        // Health display formatting options
        bool showHealthLabels = true;          // Show "Health:", "Blood:", etc. labels
        bool useCustomFont = true;             // Use custom font system instead of default

        // Crosshair Settings
        bool crosshairEnabled = false;         // Enable/disable crosshair
        float crosshairSize = 10.0f;          // Size of crosshair lines
        float crosshairThickness = 2.0f;      // Thickness of crosshair lines
        float crosshairGap = 5.0f;            // Gap from center to start of lines
        float crosshairAlpha = 1.0f;          // Alpha transparency (0.0-1.0)
        bool crosshairOutline = true;         // Enable outline around crosshair
        float crosshairOutlineThickness = 1.0f; // Outline thickness
        bool crosshairDot = false;            // Enable center dot
        float crosshairDotSize = 2.0f;        // Size of center dot

        // Head Circle Settings (DayZ mod.txt style)
        bool headCircleEnabled = true;        // Enable head circle drawing
        float headCircleSize = 0.3f;          // Head circle size multiplier (0.05f to 2.0f)

        float crosshairVerticalOffset = 0.0f; // Vertical position offset (-100 to +100 pixels)
        float crosshairHorizontalOffset = 0.0f; // Horizontal position offset (-100 to +100 pixels)
        int crosshairColorR = 255;            // Red component (0-255)
        int crosshairColorG = 255;            // Green component (0-255)
        int crosshairColorB = 255;            // Blue component (0-255)
        int crosshairColorA = 255;            // Alpha component (0-255)
        int crosshairOutlineColorR = 0;       // Outline red component (0-255)
        int crosshairOutlineColorG = 0;       // Outline green component (0-255)
        int crosshairOutlineColorB = 0;       // Outline blue component (0-255)
        int crosshairOutlineColorA = 255;     // Outline alpha component (0-255)
    };

    class SettingsManager {
    private:
        std::string settingsFilePath;
        AppSettings settings;

    public:
        SettingsManager(const std::string& filePath = "app_settings.ini");
        
        // Load/Save functions
        bool loadSettings();
        bool saveSettings();
        
        // Getters
        const AppSettings& getSettings() const { return settings; }
        AppSettings& getSettings() { return settings; }
        
        // Apply settings to RenderBridge
        void applyToRenderBridge(class RenderBridge* bridge);
        void loadFromRenderBridge(class RenderBridge* bridge);

        // Skeleton Detail Level getters and setters
        int getSkeletonDetailLevel() const { return settings.skeletonDetailLevel; }
        void setSkeletonDetailLevel(int value) { settings.skeletonDetailLevel = value; }

    private:
        void parseSettingValue(const std::string& key, const std::string& value);
        void parseESPAndOtherSettings(const std::string& key, const std::string& value, AppSettings& settings);
        void parseLootCategoryColors(const std::string& key, const std::string& value, AppSettings& settings);
    };

} // namespace DMARender
