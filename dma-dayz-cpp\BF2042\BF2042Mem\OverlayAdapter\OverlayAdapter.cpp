#include "OverlayAdapter.h"
#include <imgui.h>
#include <unordered_set>
#include <chrono>
#include <cmath>
#include <set>
#include <fstream>
#include <iostream>
#include <algorithm>

BF2042::OverlayAdapter::OverlayAdapter(std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater, std::shared_ptr<DMARender::RenderBridge> bridge)
    : memoryUpdater(memoryUpdater), bridge(bridge) {

    // Set the bridge in the base IOverlay class for font style access (CRITICAL!)
    setBridge(bridge);

    lastFrameTime = std::chrono::steady_clock::now();

    // minimal
}

void BF2042::OverlayAdapter::DrawOverlay() {
    if (!memoryUpdater || !bridge) { return; }

    // minimal

    // Draw crosshair (if enabled) - CRITICAL for basic rendering test
    drawCrosshair();

    // Performance tracking
    auto currentTime = std::chrono::steady_clock::now();
    frameTime = std::chrono::duration<float, std::milli>(currentTime - lastFrameTime).count();
    lastFrameTime = currentTime;
    frameCount++;

    if (frameCount % 60 == 0) { // Update average every 60 frames
        avgFrameTime = frameTime;
    }

    // Clear expired cache entries
    clearExpiredCache();

    // Get camera for world-to-screen calculations
    auto camera = memoryUpdater->getCamera();
    // continue regardless of camera

    // Get local player for team comparison
    auto localPlayer = memoryUpdater->getLocalPlayer();
    uint32_t localTeamId = 0;
    if (localPlayer) {
        localTeamId = localPlayer->TeamId;
    }

    // Draw FPS counter (always visible for debugging)
    drawFPSCounter();

    // Draw debug info
    drawDebugInfo();

    // Get all players (declare outside camera check for debug window access)
    auto players = memoryUpdater->getAllPlayers();

    // Debug: Check if memoryUpdater is working
    if (!memoryUpdater) {
        std::cout << "[BF6 OVERLAY] ERROR: memoryUpdater is null!" << std::endl;
    } else {
    // minimal

        // Check if update threads are running
        bool isRunning = memoryUpdater->isRunning();
        // minimal

        // minimal

        // Check player count from the updater directly
        // minimal
    }

    // Only proceed with game-specific rendering if camera is valid
    if (camera && camera->isValid()) {

        // Get ESP settings from bridge (DayZ-style)
        bool showPlayerBoxes = bridge->shouldShowBoundingBoxes();
        bool showPlayerNames = bridge->shouldShowPlayerNameFUSER();
        bool showPlayerDistance = bridge->shouldShowPlayerDistanceFUSER();
        float maxPlayerDistance = bridge->getPlayerMaxDistance();

        // minimal

        // Render player ESP
        if (showPlayerBoxes || showPlayerNames || showPlayerDistance) {
            for (const auto& player : players) {
                if (!player || !player->isValid()) {
                    continue;
                }

                // Skip if player is too far away
                if (player->SoldierEntity) {
                    DMARender::Vector3 playerPos = player->SoldierEntity->getPosition();
                    float distance = camera->getDistanceToCamera(playerPos);

                    if (distance > maxPlayerDistance) {
                        continue;
                    }

                    renderPlayerESP(player, camera);
                }
            }
        }
    }

    // Render performance info in top-right corner
    if (bridge->shouldShowPerformanceInfo()) {
        ImGui::SetNextWindowPos(ImVec2(ImGui::GetIO().DisplaySize.x - 200, 10), ImGuiCond_Always);
        ImGui::SetNextWindowSize(ImVec2(190, 80), ImGuiCond_Always);
        
        if (ImGui::Begin("Performance##BF6", nullptr, 
                        ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | 
                        ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoCollapse)) {
            
            ImGui::Text("FPS: %.1f", 1000.0f / avgFrameTime);
            ImGui::Text("Frame Time: %.2fms", avgFrameTime);
            ImGui::Text("Players: %zu", players.size());
            
            if (localPlayer) {
                ImGui::Text("Team: %u", localPlayer->TeamId);
            }
        }
        ImGui::End();
    }
}

void BF2042::OverlayAdapter::renderPlayerESP(const std::shared_ptr<BF2042::ClientPlayer>& player, const std::shared_ptr<BF2042::Camera>& camera) {
    if (!player->SoldierEntity || !player->SoldierEntity->isValid()) {
        return;
    }

    DMARender::Vector3 playerPos = player->SoldierEntity->getPosition();

    // Calculate head position (DayZ-style)
    DMARender::Vector3 headPos = playerPos;
    headPos.y += 1.8f; // Standard player height for BF6

    // World-to-screen conversion for both feet and head (DayZ SDK method)
    DMARender::Vector2 feetScreen, headScreen;
    if (!camera->worldToScreen(playerPos, feetScreen) || !camera->worldToScreen(headPos, headScreen)) {
        return; // Player not visible on screen
    }

    // Get distance
    float distance = camera->getDistanceToCamera(playerPos);

    // Get team color and visibility info
    auto localPlayer = memoryUpdater->getLocalPlayer();
    uint32_t localTeamId = localPlayer ? localPlayer->TeamId : 0;
    bool isLocalPlayer = (localPlayer && player.get() == localPlayer.get());

    // BF2042 Visibility Detection
    bool isVisible = player->SoldierEntity->isVisible();
    bool isOccluded = player->SoldierEntity->isOccluded();
    bool isPACScanned = player->SoldierEntity->isPACScanned();
    bool shouldHighlight = player->SoldierEntity->shouldHighlight();

    ImU32 teamColor = getTeamColor(player->TeamId, localTeamId, isLocalPlayer);

    // Modify color based on visibility and PAC scan
    if (isPACScanned || shouldHighlight) {
        teamColor = IM_COL32(255, 100, 255, 255); // Bright magenta for PAC scanned
    } else if (isOccluded) {
        // Reduce alpha for occluded players
        ImU32 r = (teamColor >> 0) & 0xFF;
        ImU32 g = (teamColor >> 8) & 0xFF;
        ImU32 b = (teamColor >> 16) & 0xFF;
        teamColor = IM_COL32(r, g, b, 128); // Half transparency
    }

    // Calculate DayZ-style bounding box dimensions
    float box_h = fabs(headScreen.y - feetScreen.y);  // Height = distance between head and feet
    float box_w = box_h / 1.65f;                      // Width = height divided by 1.65 (DayZ aspect ratio)

    // Calculate box corners (DayZ SDK style)
    float left = feetScreen.x - box_w * 0.5f;         // Center the box horizontally on entity
    float right = left + box_w;
    float bottom = feetScreen.y;                      // Bottom at entity feet
    float top = headScreen.y;                         // Top at entity head

    // Render bounding box using DayZ-style method
    if (bridge->shouldShowBoundingBoxes()) {
        renderDayZStyleBoundingBox(DMARender::Vector2{left, top}, DMARender::Vector2{right, bottom}, box_w, teamColor);
    }

    // Render health bar
    if (bridge->shouldShowPlayerInfoesp() && player->SoldierEntity->Health) {
        float healthPercentage = player->SoldierEntity->Health->getHealthPercentage();
        float maxHealth = player->SoldierEntity->Health->MaxHealth;
        renderHealthBar(DMARender::Vector2{left + box_w/2.0f, bottom}, healthPercentage, maxHealth);
    }

    // Render player info (DayZ-style centered above box)
    if (bridge->shouldShowPlayerNameFUSER() || bridge->shouldShowPlayerDistanceFUSER()) {
        renderPlayerInfo(DMARender::Vector2{left + box_w/2.0f, top - 17.0f}, player, distance);
    }
}

void BF2042::OverlayAdapter::renderHealthBar(const DMARender::Vector2& screenPos, float healthPercentage, float maxHealth) {
    if (healthPercentage <= 0.0f) return;

    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    
    // Health bar dimensions
    float barWidth = 60.0f;
    float barHeight = 6.0f;
    float barX = screenPos.x - barWidth / 2.0f;
    float barY = screenPos.y - 40.0f; // Above player

    // Background
    drawList->AddRectFilled(
        ImVec2(barX - 1, barY - 1),
        ImVec2(barX + barWidth + 1, barY + barHeight + 1),
        IM_COL32(0, 0, 0, 180)
    );

    // Health bar color based on percentage
    ImU32 healthColor;
    if (healthPercentage > 75.0f) {
        healthColor = IM_COL32(0, 255, 0, 255); // Green
    } else if (healthPercentage > 50.0f) {
        healthColor = IM_COL32(255, 255, 0, 255); // Yellow
    } else if (healthPercentage > 25.0f) {
        healthColor = IM_COL32(255, 165, 0, 255); // Orange
    } else {
        healthColor = IM_COL32(255, 0, 0, 255); // Red
    }

    // Health bar fill
    float fillWidth = (barWidth * healthPercentage) / 100.0f;
    drawList->AddRectFilled(
        ImVec2(barX, barY),
        ImVec2(barX + fillWidth, barY + barHeight),
        healthColor
    );

    // Health text
    std::string healthText = std::to_string(static_cast<int>(healthPercentage)) + "%";
    ImVec2 textSize = ImGui::CalcTextSize(healthText.c_str());
    drawList->AddText(
        ImVec2(screenPos.x - textSize.x / 2.0f, barY - textSize.y - 2),
        IM_COL32(255, 255, 255, 255),
        healthText.c_str()
    );
}

void BF2042::OverlayAdapter::renderPlayerInfo(const DMARender::Vector2& screenPos, const std::shared_ptr<BF2042::ClientPlayer>& player, float distance) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    std::vector<std::string> infoLines;

    // Player name (use bridge settings)
    if (bridge->shouldShowPlayerNameFUSER()) {
        std::string playerName = player->getName();
        if (!playerName.empty()) {
            infoLines.push_back(playerName);
        }
    }

    // Distance (use bridge settings)
    if (bridge->shouldShowPlayerDistanceFUSER()) {
        infoLines.push_back(formatDistance(distance));
    }

    // Team info (use bridge settings)
    if (bridge->shouldShowPlayerInfoesp()) {
        infoLines.push_back("Team: " + std::to_string(player->TeamId));

        // BF2042 Visibility Status
        if (player->SoldierEntity->isPACScanned()) {
            infoLines.push_back("[PAC SCANNED]");
        } else if (player->SoldierEntity->isSpottedByPAC()) {
            infoLines.push_back("[SPOTTED]");
        } else if (player->SoldierEntity->isOccluded()) {
            infoLines.push_back("[OCCLUDED]");
        } else if (player->SoldierEntity->isVisible()) {
            infoLines.push_back("[VISIBLE]");
        }
    }

    // Weapon info (if available)
    if (player->SoldierEntity->Weapons && player->SoldierEntity->Weapons->hasWeapon()) {
        std::string weaponName = player->SoldierEntity->Weapons->getWeaponName();
        if (!weaponName.empty()) {
            infoLines.push_back("Weapon: " + weaponName);
        }
    }

    // Render info lines
    float lineHeight = ImGui::GetTextLineHeight();
    float startY = screenPos.y + 20.0f; // Below player
    
    auto localPlayer = memoryUpdater->getLocalPlayer();
    uint32_t localTeamId = localPlayer ? localPlayer->TeamId : 0;
    bool isLocalPlayer = (localPlayer && player.get() == localPlayer.get());
    ImU32 textColor = getTeamColor(player->TeamId, localTeamId, isLocalPlayer);

    for (size_t i = 0; i < infoLines.size(); ++i) {
        ImVec2 textSize = ImGui::CalcTextSize(infoLines[i].c_str());
        float textX = screenPos.x - textSize.x / 2.0f;
        float textY = startY + (i * lineHeight);

        // Text background
        drawList->AddRectFilled(
            ImVec2(textX - 2, textY - 1),
            ImVec2(textX + textSize.x + 2, textY + textSize.y + 1),
            IM_COL32(0, 0, 0, 120)
        );

        // Text
        drawList->AddText(
            ImVec2(textX, textY),
            textColor,
            infoLines[i].c_str()
        );
    }
}

void BF2042::OverlayAdapter::renderBoundingBox(const DMARender::Vector2& screenPos, const std::shared_ptr<BF2042::ClientPlayer>& player) {
    // Legacy method - kept for compatibility
    // Use renderDayZStyleBoundingBox for new implementations
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    float boxWidth = 40.0f;
    float boxHeight = 80.0f;

    auto localPlayer = memoryUpdater->getLocalPlayer();
    uint32_t localTeamId = localPlayer ? localPlayer->TeamId : 0;
    bool isLocalPlayer = (localPlayer && player.get() == localPlayer.get());
    ImU32 boxColor = getTeamColor(player->TeamId, localTeamId, isLocalPlayer);

    drawList->AddRect(
        ImVec2(screenPos.x - boxWidth / 2.0f, screenPos.y - boxHeight / 2.0f),
        ImVec2(screenPos.x + boxWidth / 2.0f, screenPos.y + boxHeight / 2.0f),
        boxColor,
        0.0f,
        0,
        bridge->getBoundingBoxThickness()
    );
}

void BF2042::OverlayAdapter::renderDayZStyleBoundingBox(const DMARender::Vector2& top, const DMARender::Vector2& bottom, float width, ImU32 color) {
    // Use the existing DayZ-style bounding box from IOverlay
    // This calls the same method used in DayZ for consistent box rendering
    drawBoundingBox(top, bottom, width, color);
}

ImU32 BF2042::OverlayAdapter::getTeamColor(uint32_t teamId, uint32_t localTeamId, bool isLocalPlayer) const {
    if (isLocalPlayer) {
        return localPlayerColor;
    }
    
    if (teamId == localTeamId) {
        return friendlyTeamColor;
    } else if (teamId != 0) {
        return enemyTeamColor;
    } else {
        return neutralTeamColor;
    }
}

std::string BF2042::OverlayAdapter::formatDistance(float distance) const {
    if (distance < 1000.0f) {
        return std::to_string(static_cast<int>(distance)) + "m";
    } else {
        return std::to_string(static_cast<int>(distance / 1000.0f)) + "." +
               std::to_string(static_cast<int>(distance) % 1000 / 100) + "km";
    }
}

void BF2042::OverlayAdapter::clearExpiredCache() {
    auto now = std::chrono::steady_clock::now();
    
    // Clear expired player cache
    for (auto it = playerCache.begin(); it != playerCache.end();) {
        if (now - it->second.timestamp > PLAYER_CACHE_DURATION) {
            it = playerCache.erase(it);
        } else {
            ++it;
        }
    }
    
    // Clear expired position cache
    for (auto it = positionCache.begin(); it != positionCache.end();) {
        if (now - it->second.timestamp > POSITION_CACHE_DURATION) {
            it = positionCache.erase(it);
        } else {
            ++it;
        }
    }
}

void BF2042::OverlayAdapter::createFonts() {
    std::cout << "[BF2042 OVERLAY] createFonts() called" << std::endl;

    // Use the base class font creation system (same as DayZ)
    // This is critical for proper text rendering
    if (bridge) {
        std::cout << "[BF6 OVERLAY] Bridge exists, creating fonts..." << std::endl;

        // Get ImGui IO for font creation
        ImGuiIO& io = ImGui::GetIO();

        // Clear existing fonts
        io.Fonts->Clear();

        // Add default font as fallback
        ImFont* defaultFont = io.Fonts->AddFontDefault();

        // Build font atlas
        io.Fonts->Build();

        std::cout << "[BF6 OVERLAY] Fonts created successfully" << std::endl;
    } else {
        std::cout << "[BF2042 OVERLAY] Bridge is null, cannot create fonts!" << std::endl;
    }
}

void BF2042::OverlayAdapter::drawFPSCounter() {
    // Draw FPS counter in top-right corner (same as DayZ)
    ImGuiIO& io = ImGui::GetIO();
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    // Calculate FPS
    float fps = 1000.0f / avgFrameTime;
    char fpsBuffer[64];
    sprintf_s(fpsBuffer, "FPS: %.1f", fps);
    std::string fpsText = fpsBuffer;

    // Position in top-right corner
    ImVec2 textSize = ImGui::CalcTextSize(fpsText.c_str());
    ImVec2 textPos = ImVec2(io.DisplaySize.x - textSize.x - 10, 10);

    // Background
    drawList->AddRectFilled(
        ImVec2(textPos.x - 5, textPos.y - 2),
        ImVec2(textPos.x + textSize.x + 5, textPos.y + textSize.y + 2),
        IM_COL32(0, 0, 0, 180)
    );

    // Text
    drawList->AddText(textPos, IM_COL32(255, 255, 255, 255), fpsText.c_str());

    std::cout << "[BF2042 OVERLAY] FPS counter drawn: " << fps << std::endl;
}

void BF2042::OverlayAdapter::drawDebugInfo() {
    // Draw debug info in top-left corner
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    std::vector<std::string> debugLines;
    debugLines.push_back("BF2042 DMA ESP");

    char frameBuffer[64];
    sprintf_s(frameBuffer, "Frame Time: %.2fms", avgFrameTime);
    debugLines.push_back(frameBuffer);

    // Get player count if available
    if (memoryUpdater) {
        auto players = memoryUpdater->getAllPlayers();
        char playerBuffer[64];
        sprintf_s(playerBuffer, "Players: %zu", players.size());
        debugLines.push_back(playerBuffer);
    }

    // Draw each line
    float currentY = 10.0f;
    for (const auto& line : debugLines) {
        ImVec2 textSize = ImGui::CalcTextSize(line.c_str());
        ImVec2 textPos = ImVec2(10, currentY);

        // Background
        drawList->AddRectFilled(
            ImVec2(textPos.x - 2, textPos.y - 1),
            ImVec2(textPos.x + textSize.x + 2, textPos.y + textSize.y + 1),
            IM_COL32(0, 0, 0, 180)
        );

        // Text
        drawList->AddText(textPos, IM_COL32(255, 255, 255, 255), line.c_str());

        currentY += textSize.y + 3;
    }

    std::cout << "[BF6 OVERLAY] Debug info drawn" << std::endl;
}
