#pragma once
#include "../includes.h"
#include <vector>
#include <unordered_map>
#include <mutex>

namespace DMAMem {

    // Page table entry structures for manual translation
    struct PageTableEntry {
        union {
            QWORD value;
            struct {
                QWORD present : 1;
                QWORD write : 1;
                QWORD user : 1;
                QWORD writeThrough : 1;
                QWORD cacheDisable : 1;
                QWORD accessed : 1;
                QWORD dirty : 1;
                QWORD pageSize : 1;
                QWORD global : 1;
                QWORD available : 3;
                QWORD physicalAddress : 40;
                QWORD reserved : 11;
                QWORD executeDisable : 1;
            };
        };
    };

    // CR3 register structure
    struct CR3Register {
        union {
            QWORD value;
            struct {
                QWORD reserved1 : 3;
                QWORD writeThrough : 1;
                QWORD cacheDisable : 1;
                QWORD reserved2 : 7;
                QWORD pml4Base : 40;
                QWORD reserved3 : 12;
            };
        };
    };

    // Virtual address breakdown for 4-level paging
    struct VirtualAddress {
        union {
            QWORD value;
            struct {
                QWORD offset : 12;      // Page offset (bits 0-11)
                QWORD pt : 9;           // Page table index (bits 12-20)
                QWORD pd : 9;           // Page directory index (bits 21-29)
                QWORD pdpt : 9;         // Page directory pointer table index (bits 30-38)
                QWORD pml4 : 9;         // PML4 index (bits 39-47)
                QWORD signExtend : 16;  // Sign extension (bits 48-63)
            };
        };
    };

    class CR3Bypass {
    private:
        VmmManager* vmm;
        DWORD targetPid;
        
        // Cache for page table entries to improve performance
        std::unordered_map<QWORD, QWORD> pageTableCache;
        std::mutex cacheMutex;
        
        // Statistics for debugging
        struct Stats {
            QWORD successfulTranslations = 0;
            QWORD failedTranslations = 0;
            QWORD cacheHits = 0;
            QWORD cacheMisses = 0;
        } stats;

        // Internal methods
        bool ReadPhysicalMemory(QWORD physicalAddress, void* buffer, SIZE_T size);
        bool ReadCR3Register(DWORD pid, QWORD& cr3Value);
        QWORD TranslateVirtualToPhysical(QWORD virtualAddress, QWORD cr3);
        QWORD WalkPageTables(QWORD virtualAddress, QWORD pml4Base);
        
        // Cache management
        void CachePageTableEntry(QWORD virtualPage, QWORD physicalPage);
        bool GetCachedTranslation(QWORD virtualPage, QWORD& physicalPage);
        void ClearCache();

    public:
        CR3Bypass(VmmManager* vmmManager, DWORD processId);
        ~CR3Bypass();

        // Main bypass methods
        bool Initialize();
        bool ReadVirtualMemory(QWORD virtualAddress, void* buffer, SIZE_T size);
        bool ReadVirtualMemoryBatch(const std::vector<std::pair<QWORD, SIZE_T>>& requests, 
                                   std::vector<std::vector<BYTE>>& results);
        
        // Diagnostic methods
        void PrintStatistics() const;
        void ResetStatistics();

        // Public accessors for DMA hardware access
        VmmManager* GetVmmManager() const { return vmm; }
        DWORD GetTargetPid() const { return targetPid; }
        bool TestTranslation(QWORD virtualAddress);
        
        // Advanced features
        bool ReadWithFallback(QWORD virtualAddress, void* buffer, SIZE_T size);
        void SetCacheEnabled(bool enabled) { cacheEnabled = enabled; }
        
    private:
        bool cacheEnabled = true;
        QWORD currentCR3 = 0;
        bool initialized = false;
    };

    // Helper class for BF2042-specific memory reading with CR3 bypass
    class BF2042CR3Reader {
    private:
        std::unique_ptr<CR3Bypass> cr3Bypass;
        VmmManager* vmm;
        DWORD pid;

    public:
        BF2042CR3Reader(VmmManager* vmmManager, DWORD processId);
        ~BF2042CR3Reader();

        bool Initialize();
        
        // BF2042-specific reading methods
        bool ReadGameContext(QWORD& gameContextAddress);
        bool ReadPlayerManager(QWORD gameContext, QWORD& playerManagerAddress);
        bool ScanPhysicalMemoryViaDMA(QWORD gameContext, QWORD& playerManagerAddress);

        // DMA Hardware-Level Methods
        bool GetPhysicalAddressViaDMA(QWORD virtualAddress, QWORD& physicalAddress);
        bool ScanPhysicalRegionForPlayerManager(QWORD physicalBase, QWORD& playerManagerAddress);
        bool AnalyzePhysicalPageForPlayerManager(BYTE* buffer, SIZE_T size, QWORD physicalBase, QWORD& playerManagerAddress);
        bool ValidatePlayerManagerCandidate(QWORD address);
        bool BruteForcePhysicalScan(QWORD& playerManagerAddress);
        bool AnalyzeDMAMemoryMap(QWORD& playerManagerAddress);
        bool ReadClientPlayerArray(QWORD playerManager, std::vector<QWORD>& playerAddresses);
        bool ReadPlayerData(QWORD playerAddress, void* buffer, SIZE_T size);
        
        // Diagnostic methods
        void PrintDiagnostics() const;
        bool TestBF2042Memory();
    };
}
