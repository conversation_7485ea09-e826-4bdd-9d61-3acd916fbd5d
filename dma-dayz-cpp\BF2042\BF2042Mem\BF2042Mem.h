#pragma once

#define EXE_NAME "BF2042.exe"

#include "DMAMemoryManagement/includes.h"
#include "DMAMemoryManagement/BF2042Signatures/BF2042Integration.h"
#include "../Structs/WorldPointer.h"
#include "../Structs/NetworkManager.h"
#include "../Structs/Entity.h"

namespace BF2042 {
	class Mem {
		DMAMem::VmmManager* vmmManager = NULL;
		DMAMem::StaticManager staticManager = NULL;
		DMAMem::BF2042Integration* bf2042Integration = NULL;
		QWORD worldAddress;
		QWORD networkManagerAddress;
		void fetchBaseAddresses();

	public:
		Mem(DMAMem::VmmManager* vmmManager);
		~Mem();
		DMAMem::VmmManager* getVMM();
		DWORD getPid();
		WorldPointer getWorld();
		NetworkManager getNetworkManager();
		std::vector<std::shared_ptr<BF2042::Entity>> getAllUniqueEntities();
		void processESP();
		DMAMem::BF2042Integration* getBF2042Integration() { return bf2042Integration; }


	};
};
