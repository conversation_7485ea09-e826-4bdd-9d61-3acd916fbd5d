#pragma once
#include "ClientPlayer.h"
#include "ClientSoldierEntity.h"

namespace BF2042 {
    // Entity types for BF2042
    enum ENTITY_TYPE {
        NONE,
        PLAYER,
        SOLDIER,
        VEHICLE
    };

    // Compatibility alias - En<PERSON><PERSON> now maps to ClientPlayer for BF2042
    // This maintains compatibility with existing code structure
    using Entity = ClientPlayer;
    
    // Helper functions for entity type detection
    inline ENTITY_TYPE getEntityType(const std::shared_ptr<Entity>& entity) {
        if (!entity || !entity->isValid()) {
            return ENTITY_TYPE::NONE;
        }
        
        if (entity->SoldierEntity && entity->SoldierEntity->isValid()) {
            return ENTITY_TYPE::PLAYER;
        }
        
        if (entity->VehicleEntity && entity->VehicleEntity->isValid()) {
            return ENTITY_TYPE::VEHICLE;
        }
        
        return ENTITY_TYPE::NONE;
    }
    
    // Helper function to check if entity is a valid player
    inline bool isValidPlayer(const std::shared_ptr<Entity>& entity) {
        return entity && entity->isValid() && 
               entity->SoldierEntity && entity->SoldierEntity->isValid();
    }
    
    // Helper function to get entity position
    inline DMARender::Vector3 getEntityPosition(const std::shared_ptr<Entity>& entity) {
        if (entity && entity->SoldierEntity) {
            return entity->SoldierEntity->getPosition();
        }
        return DMARender::Vector3{0.0f, 0.0f, 0.0f};
    }
    
    // Helper function to get entity name
    inline std::string getEntityName(const std::shared_ptr<Entity>& entity) {
        if (entity) {
            return entity->getName();
        }
        return "";
    }
}
