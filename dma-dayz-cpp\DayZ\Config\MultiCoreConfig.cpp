#include "MultiCoreConfig.h"
#include <iostream>
#include <sstream>
#include <fstream>

namespace DayZ {
    
    // Define timing profiles for different system configurations
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MANY_CORE_PROFILE = {
        1,    // nearWorkerMs - Ultra-fast for 16+ cores
        8,    // farWorkerMs - Faster updates
        8,    // itemWorkerMs - Faster item tracking
        400,  // slowWorkerMs - Reduced from 500ms
        800,  // scoreboardMs - Faster scoreboard
        5     // cameraUpdateMs - Ultra-smooth camera
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MEDIUM_CORE_PROFILE = {
        2,    // nearWorkerMs - Fast for 8-15 cores
        12,   // farWorkerMs - Moderate updates
        12,   // itemWorkerMs - Moderate item tracking
        500,  // slowWorkerMs - Standard timing
        1000, // scoreboardMs - Standard scoreboard
        7     // cameraUpdateMs - Smooth camera (144 FPS)
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::STANDARD_PROFILE = {
        4,    // nearWorkerMs - Standard for 4-7 cores
        16,   // farWorkerMs - Standard updates
        16,   // itemWorkerMs - Standard item tracking
        500,  // slowWorkerMs - Standard timing
        1000, // scoreboardMs - Standard scoreboard
        7     // cameraUpdateMs - 144 FPS camera
    };
    
    const MultiCoreConfig::TimingProfile MultiCoreConfig::MINIMAL_PROFILE = {
        7,    // nearWorkerMs - Conservative for <4 cores
        20,   // farWorkerMs - Slower updates
        20,   // itemWorkerMs - Slower item tracking
        1000, // slowWorkerMs - Slower timing
        2000, // scoreboardMs - Slower scoreboard
        10    // cameraUpdateMs - Conservative camera
    };
    
    // Global configuration instance
    MultiCoreConfig g_multiCoreConfig;
    
    MultiCoreConfig::MultiCoreConfig() {
        hardwareCores = std::thread::hardware_concurrency();
        detectOptimalSettings();
    }
    
    void MultiCoreConfig::detectOptimalSettings() {
        // Select timing profile based on core count
        currentProfile = selectProfileForCoreCount(hardwareCores);
        
        // Configure parallel processing
        enableParallelProcessing = hardwareCores >= MIN_CORES_FOR_PARALLEL;
        enableThreadAffinity = hardwareCores >= MIN_CORES_FOR_AFFINITY;
        
        // Set optimal worker thread count
        if (hardwareCores >= 16) {
            maxWorkerThreads = std::min(hardwareCores / 2, MAX_WORKER_THREADS);
        } else if (hardwareCores >= 8) {
            maxWorkerThreads = std::min(hardwareCores / 3, 6);
        } else if (hardwareCores >= 4) {
            maxWorkerThreads = 2;
        } else {
            maxWorkerThreads = 1;
        }
        
        std::cout << "[MultiCore] Detected " << hardwareCores << " cores, configured for "
                  << (hardwareCores >= 16 ? "MANY_CORE" : 
                      hardwareCores >= 8 ? "MEDIUM_CORE" : 
                      hardwareCores >= 4 ? "STANDARD" : "MINIMAL") 
                  << " profile" << std::endl;
    }
    
    MultiCoreConfig::TimingProfile MultiCoreConfig::selectProfileForCoreCount(int cores) const {
        if (cores >= 16) return MANY_CORE_PROFILE;
        if (cores >= 8) return MEDIUM_CORE_PROFILE;
        if (cores >= 4) return STANDARD_PROFILE;
        return MINIMAL_PROFILE;
    }
    
    MultiCoreConfig::TimingProfile MultiCoreConfig::getCurrentProfile() const {
        if (adaptiveTimings) {
            return selectProfileForCoreCount(hardwareCores);
        }
        return currentProfile;
    }
    
    int MultiCoreConfig::getOptimalEntityThreadCount() const {
        return maxWorkerThreads.load();
    }
    
    bool MultiCoreConfig::shouldUseParallelProcessing() const {
        return enableParallelProcessing.load() && hardwareCores >= MIN_CORES_FOR_PARALLEL;
    }
    
    bool MultiCoreConfig::shouldUseThreadAffinity() const {
        return enableThreadAffinity.load() && hardwareCores >= MIN_CORES_FOR_AFFINITY;
    }
    
    std::string MultiCoreConfig::getSystemInfo() const {
        std::stringstream ss;
        ss << "Hardware Cores: " << hardwareCores << "\n";
        ss << "Parallel Processing: " << (shouldUseParallelProcessing() ? "ENABLED" : "DISABLED") << "\n";
        ss << "Thread Affinity: " << (shouldUseThreadAffinity() ? "ENABLED" : "DISABLED") << "\n";
        ss << "Worker Threads: " << maxWorkerThreads.load() << "\n";
        
        auto profile = getCurrentProfile();
        ss << "Timing Profile:\n";
        ss << "  Near Worker: " << profile.nearWorkerMs << "ms\n";
        ss << "  Far Worker: " << profile.farWorkerMs << "ms\n";
        ss << "  Item Worker: " << profile.itemWorkerMs << "ms\n";
        ss << "  Slow Worker: " << profile.slowWorkerMs << "ms\n";
        ss << "  Camera: " << profile.cameraUpdateMs << "ms\n";
        
        return ss.str();
    }
    
    void MultiCoreConfig::loadFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cout << "[MultiCore] Config file not found, using defaults" << std::endl;
            return;
        }
        
        // Simple key=value parser
        std::string line;
        while (std::getline(file, line)) {
            if (line.empty() || line[0] == '#') continue;
            
            size_t pos = line.find('=');
            if (pos == std::string::npos) continue;
            
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);
            
            // Parse configuration values
            if (key == "enableParallelProcessing") {
                enableParallelProcessing = (value == "true" || value == "1");
            } else if (key == "enableThreadAffinity") {
                enableThreadAffinity = (value == "true" || value == "1");
            } else if (key == "maxWorkerThreads") {
                maxWorkerThreads = std::stoi(value);
            } else if (key == "adaptiveTimings") {
                adaptiveTimings = (value == "true" || value == "1");
            } else if (key == "selectedProfile") {
                if (value == "MANY_CORE") setProfile(MANY_CORE);
                else if (value == "MEDIUM_CORE") setProfile(MEDIUM_CORE);
                else if (value == "STANDARD") setProfile(STANDARD);
                else if (value == "MINIMAL") setProfile(MINIMAL);
                else setProfile(AUTO_DETECT);
            }
        }
        
        std::cout << "[MultiCore] Configuration loaded from " << filename << std::endl;
    }
    
    void MultiCoreConfig::saveToFile(const std::string& filename) const {
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cout << "[MultiCore] Failed to save config to " << filename << std::endl;
            return;
        }
        
        file << "# DayZ DMA Multi-Core Configuration\n";
        file << "# Auto-generated configuration file\n\n";
        file << "enableParallelProcessing=" << (enableParallelProcessing.load() ? "true" : "false") << "\n";
        file << "enableThreadAffinity=" << (enableThreadAffinity.load() ? "true" : "false") << "\n";
        file << "maxWorkerThreads=" << maxWorkerThreads.load() << "\n";
        file << "adaptiveTimings=" << (adaptiveTimings.load() ? "true" : "false") << "\n";

        // Save selected profile
        std::string profileName;
        switch (getCurrentProfileType()) {
            case MANY_CORE: profileName = "MANY_CORE"; break;
            case MEDIUM_CORE: profileName = "MEDIUM_CORE"; break;
            case STANDARD: profileName = "STANDARD"; break;
            case MINIMAL: profileName = "MINIMAL"; break;
            default: profileName = "AUTO_DETECT"; break;
        }
        file << "selectedProfile=" << profileName << "\n";
        file << "\n# System Information:\n";
        file << "# Hardware Cores: " << hardwareCores << "\n";
        
        std::cout << "[MultiCore] Configuration saved to " << filename << std::endl;
    }

    void MultiCoreConfig::runPerformanceTest() {
        std::cout << "\n=== Multi-Core Performance Test ===" << std::endl;
        std::cout << "Hardware Cores: " << hardwareCores << std::endl;
        std::cout << "Selected Profile: ";

        if (hardwareCores >= 16) std::cout << "MANY_CORE (Ultra-Performance)";
        else if (hardwareCores >= 8) std::cout << "MEDIUM_CORE (High-Performance)";
        else if (hardwareCores >= 4) std::cout << "STANDARD (Balanced)";
        else std::cout << "MINIMAL (Conservative)";

        std::cout << std::endl;

        auto profile = getCurrentProfile();
        std::cout << "Timing Configuration:" << std::endl;
        std::cout << "  Near Worker: " << profile.nearWorkerMs << "ms (Target: " << (1000/profile.nearWorkerMs) << " FPS)" << std::endl;
        std::cout << "  Camera: " << profile.cameraUpdateMs << "ms (Target: " << (1000/profile.cameraUpdateMs) << " FPS)" << std::endl;
        std::cout << "  Parallel Processing: " << (shouldUseParallelProcessing() ? "ENABLED" : "DISABLED") << std::endl;
        std::cout << "  Thread Affinity: " << (shouldUseThreadAffinity() ? "ENABLED" : "DISABLED") << std::endl;
        std::cout << "=================================" << std::endl;
    }

    void MultiCoreConfig::setProfile(ProfileType profile) {
        selectedProfile = profile;

        switch (profile) {
            case MANY_CORE:
                currentProfile = MANY_CORE_PROFILE;
                adaptiveTimings = false;
                std::cout << "[MultiCore] Manually set to MANY_CORE profile (1ms near, 200 FPS camera)" << std::endl;
                break;
            case MEDIUM_CORE:
                currentProfile = MEDIUM_CORE_PROFILE;
                adaptiveTimings = false;
                std::cout << "[MultiCore] Manually set to MEDIUM_CORE profile (2ms near, 144 FPS camera)" << std::endl;
                break;
            case STANDARD:
                currentProfile = STANDARD_PROFILE;
                adaptiveTimings = false;
                std::cout << "[MultiCore] Manually set to STANDARD profile (4ms near, 144 FPS camera)" << std::endl;
                break;
            case MINIMAL:
                currentProfile = MINIMAL_PROFILE;
                adaptiveTimings = false;
                std::cout << "[MultiCore] Manually set to MINIMAL profile (7ms near, conservative)" << std::endl;
                break;
            case AUTO_DETECT:
            default:
                adaptiveTimings = true;
                detectOptimalSettings();
                std::cout << "[MultiCore] Set to AUTO_DETECT - using optimal profile for " << hardwareCores << " cores" << std::endl;
                break;
        }
    }

    MultiCoreConfig::ProfileType MultiCoreConfig::getCurrentProfileType() const {
        if (!adaptiveTimings.load()) {
            return static_cast<ProfileType>(selectedProfile.load());
        }

        // Return auto-detected profile type
        if (hardwareCores >= 16) return MANY_CORE;
        if (hardwareCores >= 8) return MEDIUM_CORE;
        if (hardwareCores >= 4) return STANDARD;
        return MINIMAL;
    }
}
