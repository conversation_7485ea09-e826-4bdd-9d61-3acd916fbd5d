#include "HealthBarESP.h"
#include "SkeletonESP.h" // For head position utility
#include <algorithm>

namespace DayZ {

    // ===== MAIN HEALTH BAR RENDERING FUNCTIONS =====

    void HealthBarESP::DrawPlayerHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                         float width, float height, float offsetY, float maxDistance,
                                         ImU32 highColor, ImU32 mediumColor, ImU32 lowColor, ImU32 backgroundColor,
                                         int healthType, bool showBar, bool showNumbers, bool showAllStats) {
        if (!camera || !entity) {
            return;
        }

        // Check distance before processing
        if (!IsValidHealthBarDistance(camera, entity->FutureVisualStatePtr->position, maxDistance)) {
            return;
        }

        // Get health percentage based on selected type
        float healthPercent = GetEntityHealthByType(entity, healthType);

        // Get head position for health bar placement
        DMARender::Vector3 headPos = GetEntityHeadPosition(entity);
        
        // Offset the health bar above the head
        headPos.y += offsetY;

        // Draw health bar if enabled
        if (showBar) {
            ImU32 healthColor = GetHealthBarColor(healthPercent, highColor, mediumColor, lowColor);
            DrawHealthBar(camera, headPos, healthPercent, width, height, healthColor, backgroundColor);
        }

        // Draw health text if enabled
        if (showNumbers) {
            DMARender::Vector3 textPos = headPos;
            if (showBar) {
                textPos.y += height + 0.1f; // Position text below health bar
            }
            DrawHealthText(camera, textPos, entity, healthType, showAllStats);
        }
    }

    void HealthBarESP::DrawZombieHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                         float width, float height, float offsetY, float maxDistance,
                                         ImU32 highColor, ImU32 mediumColor, ImU32 lowColor, ImU32 backgroundColor,
                                         int healthType, bool showBar, bool showNumbers, bool showAllStats) {
        if (!camera || !entity) {
            return;
        }

        // Check distance before processing
        if (!IsValidHealthBarDistance(camera, entity->FutureVisualStatePtr->position, maxDistance)) {
            return;
        }

        // Get health percentage based on selected type
        float healthPercent = GetEntityHealthByType(entity, healthType);

        // Get head position for health bar placement
        DMARender::Vector3 headPos = GetEntityHeadPosition(entity);
        
        // Offset the health bar above the head
        headPos.y += offsetY;

        // Draw health bar if enabled
        if (showBar) {
            ImU32 healthColor = GetHealthBarColor(healthPercent, highColor, mediumColor, lowColor);
            DrawHealthBar(camera, headPos, healthPercent, width, height, healthColor, backgroundColor);
        }

        // Draw health text if enabled
        if (showNumbers) {
            DMARender::Vector3 textPos = headPos;
            if (showBar) {
                textPos.y += height + 0.1f; // Position text below health bar
            }
            DrawHealthText(camera, textPos, entity, healthType, showAllStats);
        }
    }

    // Enhanced health bar rendering with real memory access
    void HealthBarESP::DrawPlayerHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                         DMAMem::VmmManager* vmm, DWORD pid,
                                         float width, float height, float offsetY, float maxDistance,
                                         ImU32 highColor, ImU32 mediumColor, ImU32 lowColor, ImU32 backgroundColor,
                                         int healthType, bool showBar, bool showNumbers, bool showAllStats) {
        if (!camera || !entity || !vmm || !pid) {
            // Fallback to basic version if VMM/PID not available
            DrawPlayerHealthBar(camera, entity, width, height, offsetY, maxDistance,
                              highColor, mediumColor, lowColor, backgroundColor,
                              healthType, showBar, showNumbers, showAllStats);
            return;
        }

        // Check distance before processing
        if (!IsValidHealthBarDistance(camera, entity->FutureVisualStatePtr->position, maxDistance)) {
            return;
        }

        // Get health percentage using enhanced memory reading
        float healthPercent = GetEntityHealthByType(entity, healthType, vmm, pid);

        // Get head position for health bar placement
        DMARender::Vector3 headPos = GetEntityHeadPosition(entity);

        // Offset the health bar above the head
        headPos.y += offsetY;

        // Draw health bar if enabled
        if (showBar) {
            ImU32 healthColor = GetHealthBarColor(healthPercent, highColor, mediumColor, lowColor);
            DrawHealthBar(camera, headPos, healthPercent, width, height, healthColor, backgroundColor);
        }

        // Draw health text if enabled
        if (showNumbers) {
            DMARender::Vector3 textPos = headPos;
            if (showBar) {
                textPos.y += height + 0.1f; // Position text below health bar
            }
            DrawHealthText(camera, textPos, entity, healthType, showAllStats, vmm, pid);
        }
    }

    void HealthBarESP::DrawZombieHealthBar(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity,
                                         DMAMem::VmmManager* vmm, DWORD pid,
                                         float width, float height, float offsetY, float maxDistance,
                                         ImU32 highColor, ImU32 mediumColor, ImU32 lowColor, ImU32 backgroundColor,
                                         int healthType, bool showBar, bool showNumbers, bool showAllStats) {
        if (!camera || !entity || !vmm || !pid) {
            // Fallback to basic version if VMM/PID not available
            DrawZombieHealthBar(camera, entity, width, height, offsetY, maxDistance,
                              highColor, mediumColor, lowColor, backgroundColor,
                              healthType, showBar, showNumbers, showAllStats);
            return;
        }

        // Check distance before processing
        if (!IsValidHealthBarDistance(camera, entity->FutureVisualStatePtr->position, maxDistance)) {
            return;
        }

        // Get health percentage using enhanced memory reading
        float healthPercent = GetEntityHealthByType(entity, healthType, vmm, pid);

        // Get head position for health bar placement
        DMARender::Vector3 headPos = GetEntityHeadPosition(entity);

        // Offset the health bar above the head
        headPos.y += offsetY;

        // Draw health bar if enabled
        if (showBar) {
            ImU32 healthColor = GetHealthBarColor(healthPercent, highColor, mediumColor, lowColor);
            DrawHealthBar(camera, headPos, healthPercent, width, height, healthColor, backgroundColor);
        }

        // Draw health text if enabled
        if (showNumbers) {
            DMARender::Vector3 textPos = headPos;
            if (showBar) {
                textPos.y += height + 0.1f; // Position text below health bar
            }
            DrawHealthText(camera, textPos, entity, healthType, showAllStats, vmm, pid);
        }
    }

    // ===== UTILITY FUNCTIONS =====

    float HealthBarESP::GetEntityHealth(std::shared_ptr<DayZ::Entity> entity) {
        if (!entity) {
            return 0.0f;
        }

        // Try to read real health values from DayZ memory
        if (entity->ReadHealthValues()) {
            // Use the actual health value read from memory
            return entity->health;
        }

        // Fallback: use isDead status for basic health indication
        if (entity->isDead) {
            return 0.0f;
        }

        // Return placeholder health value if reading failed
        return entity->health;
    }

    // Enhanced health reading with memory access
    float HealthBarESP::GetEntityHealth(std::shared_ptr<DayZ::Entity> entity, DMAMem::VmmManager* vmm, DWORD pid) {
        if (!entity) {
            return 0.0f;
        }

        // Try to read real health values from DayZ memory using enhanced method
        if (entity->ReadHealthValues(vmm, pid)) {
            // Use the actual health value read from memory
            return entity->health;
        }

        // Fallback to basic version
        return GetEntityHealth(entity);
    }

    float HealthBarESP::GetEntityHealthByType(std::shared_ptr<DayZ::Entity> entity, int healthType) {
        if (!entity) {
            return 0.0f;
        }

        // Try to read real health values from DayZ memory
        entity->ReadHealthValues();

        // Return the appropriate health type as percentage
        switch (healthType) {
            case 0: // Health (0-100)
                return entity->health;
            case 1: // Blood (0-5000) - convert to percentage
                return (entity->blood / 5000.0f) * 100.0f;
            case 2: // Shock (0-100)
                return entity->shock;
            default:
                return entity->health;
        }
    }

    // Enhanced health reading by type with memory access
    float HealthBarESP::GetEntityHealthByType(std::shared_ptr<DayZ::Entity> entity, int healthType, DMAMem::VmmManager* vmm, DWORD pid) {
        if (!entity) {
            return 0.0f;
        }

        // Try to read real health values from DayZ memory using enhanced method
        entity->ReadHealthValues(vmm, pid);

        // Return the appropriate health type as percentage
        switch (healthType) {
            case 0: // Health (0-100)
                return entity->health;
            case 1: // Blood (0-5000) - convert to percentage
                return (entity->blood / 5000.0f) * 100.0f;
            case 2: // Shock (0-100)
                return entity->shock;
            default:
                return entity->health;
        }
    }

    ImU32 HealthBarESP::GetHealthBarColor(float healthPercent, ImU32 highColor, ImU32 mediumColor, ImU32 lowColor) {
        if (healthPercent > 66.0f) {
            return highColor;   // Green for high health
        } else if (healthPercent > 33.0f) {
            return mediumColor; // Yellow for medium health
        } else {
            return lowColor;    // Red for low health
        }
    }

    bool HealthBarESP::WorldToScreen(DayZ::Camera* camera, const DMARender::Vector3& worldPos, DMARender::Vector2* screenPos) {
        if (!camera || !screenPos) {
            return false;
        }

        // Use the same WorldToScreen implementation as SkeletonESP
        DMARender::Vector3 temp = DMARender::Vector3(
            worldPos.x - camera->InvertedViewTranslation.x,
            worldPos.y - camera->InvertedViewTranslation.y,
            worldPos.z - camera->InvertedViewTranslation.z
        );

        double x = temp.Dot(camera->InvertedViewRight);
        double y = temp.Dot(camera->InvertedViewUp);
        double z = temp.Dot(camera->InvertedViewForward);

        // Better near plane handling
        if (z < 0.1) return false;

        ImVec2 windowSize = ImGui::GetWindowSize();

        // Higher precision calculations for better accuracy
        double normalizedX = (x / camera->GetProjectionD1.x) / z;
        double normalizedY = (y / camera->GetProjectionD2.y) / z;

        // More precise screen coordinate calculation
        screenPos->x = (float)((windowSize.x * 0.5) + (normalizedX * (windowSize.x * 0.5)));
        screenPos->y = (float)((windowSize.y * 0.5) - (normalizedY * (windowSize.y * 0.5)));

        return (screenPos->x >= 0 && screenPos->x <= windowSize.x &&
                screenPos->y >= 0 && screenPos->y <= windowSize.y);
    }

    // ===== HELPER FUNCTIONS =====

    void HealthBarESP::DrawHealthBar(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                   float healthPercent, float width, float height,
                                   ImU32 healthColor, ImU32 backgroundColor) {
        if (!camera) return;

        // Convert world position to screen coordinates
        DMARender::Vector2 screenPos;
        if (!WorldToScreen(camera, worldPos, &screenPos)) {
            return;
        }

        // Calculate health bar dimensions
        float barWidth = width;
        float barHeight = height;
        float healthWidth = barWidth * (healthPercent / 100.0f);

        // Calculate positions
        float left = screenPos.x - (barWidth / 2.0f);
        float top = screenPos.y;
        float right = left + barWidth;
        float bottom = top + barHeight;

        // Draw background
        ImGui::GetBackgroundDrawList()->AddRectFilled(
            ImVec2(left, top),
            ImVec2(right, bottom),
            backgroundColor
        );

        // Draw health bar (only if health > 0)
        if (healthPercent > 0.0f) {
            ImGui::GetBackgroundDrawList()->AddRectFilled(
                ImVec2(left, top),
                ImVec2(left + healthWidth, bottom),
                healthColor
            );
        }

        // Draw border
        ImGui::GetBackgroundDrawList()->AddRect(
            ImVec2(left, top),
            ImVec2(right, bottom),
            IM_COL32(255, 255, 255, 100), // Light border
            0.0f, 0, 1.0f
        );
    }

    // DayZ SDK-style health bar positioning (left side of bounding box)
    void HealthBarESP::DrawHealthBarSDKStyle(DayZ::Camera* camera, float left, float top, float bottom,
                                           float healthPercent, ImU32 healthColor, ImU32 backgroundColor) {
        if (!camera) return;

        // DayZ SDK health bar positioning: 6 pixels to the left of the box, 4 pixels wide
        float healthBarWidth = 4.0f;
        float healthBarOffset = 6.0f;
        float healthBarHeight = fabs(bottom - top);  // Same height as bounding box

        // Calculate health bar fill height based on health percentage
        float healthFillHeight = (healthBarHeight * healthPercent) / 100.0f;

        // Position health bar to the left of the bounding box
        float healthBarLeft = left - healthBarOffset;
        float healthBarRight = healthBarLeft + healthBarWidth;
        float healthBarTop = bottom;  // Start from bottom
        float healthBarBottom = top;  // End at top

        // Draw background (black)
        ImGui::GetBackgroundDrawList()->AddRectFilled(
            ImVec2(healthBarLeft, healthBarTop),
            ImVec2(healthBarRight, healthBarBottom),
            IM_COL32(0, 0, 0, 255)
        );

        // Draw health bar (colored based on health) - grows from bottom to top
        if (healthPercent > 0.0f) {
            ImGui::GetBackgroundDrawList()->AddRectFilled(
                ImVec2(healthBarLeft + 1, healthBarTop - 1),
                ImVec2(healthBarRight - 1, healthBarTop - healthFillHeight + 2),
                healthColor
            );
        }
    }

    DMARender::Vector3 HealthBarESP::GetEntityHeadPosition(std::shared_ptr<DayZ::Entity> entity) {
        if (!entity || !entity->FutureVisualStatePtr) {
            return DMARender::Vector3(0, 0, 0);
        }

        // Try to get head position from skeleton system
        // This will use the existing SkeletonESP head position function
        // For now, use entity position + estimated head offset
        DMARender::Vector3 headPos = entity->FutureVisualStatePtr->position;
        headPos.y += 1.7f; // Approximate head height offset

        return headPos;
    }

    bool HealthBarESP::IsValidHealthBarDistance(DayZ::Camera* camera, const DMARender::Vector3& entityPos, float maxDistance) {
        if (!camera) {
            return false;
        }

        float distance = camera->InvertedViewTranslation.Dist(entityPos);
        return distance <= maxDistance && distance >= 1.0f; // Minimum 1m distance
    }

    void HealthBarESP::DrawHealthText(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                    std::shared_ptr<DayZ::Entity> entity, int healthType, bool showAllStats,
                                    ImU32 textColor) {
        if (!camera || !entity) return;

        // Convert world position to screen coordinates
        DMARender::Vector2 screenPos;
        if (!WorldToScreen(camera, worldPos, &screenPos)) {
            return;
        }

        std::string healthText;

        if (showAllStats) {
            // Show all health stats in detailed format
            entity->ReadHealthValues(); // Update health values
            healthText = "H:" + FormatHealthValue(entity->health, 0) +
                        " B:" + FormatHealthValue(entity->blood, 1) +
                        " S:" + FormatHealthValue(entity->shock, 2);
        } else {
            // Show single health type
            float healthValue = GetEntityHealthByType(entity, healthType);
            std::string typeName = GetHealthTypeString(healthType);
            healthText = typeName + ": " + FormatHealthValue(healthValue, healthType);
        }

        // Draw text with background for better visibility
        ImVec2 textSize = ImGui::CalcTextSize(healthText.c_str());
        ImVec2 textPos = ImVec2(screenPos.x - textSize.x / 2, screenPos.y);

        // Draw background
        ImGui::GetBackgroundDrawList()->AddRectFilled(
            ImVec2(textPos.x - 2, textPos.y - 1),
            ImVec2(textPos.x + textSize.x + 2, textPos.y + textSize.y + 1),
            IM_COL32(0, 0, 0, 150) // Semi-transparent black background
        );

        // Draw text
        ImGui::GetBackgroundDrawList()->AddText(textPos, textColor, healthText.c_str());
    }

    // Enhanced health text rendering with memory access
    void HealthBarESP::DrawHealthText(DayZ::Camera* camera, const DMARender::Vector3& worldPos,
                                    std::shared_ptr<DayZ::Entity> entity, int healthType, bool showAllStats,
                                    DMAMem::VmmManager* vmm, DWORD pid, ImU32 textColor) {
        if (!camera || !entity) return;

        // Convert world position to screen coordinates
        DMARender::Vector2 screenPos;
        if (!WorldToScreen(camera, worldPos, &screenPos)) {
            return;
        }

        std::string healthText;

        if (showAllStats) {
            // Show all health stats in detailed format using enhanced memory reading
            entity->ReadHealthValues(vmm, pid); // Update health values with real memory access
            healthText = "H:" + FormatHealthValue(entity->health, 0) +
                        " B:" + FormatHealthValue(entity->blood, 1) +
                        " S:" + FormatHealthValue(entity->shock, 2);
        } else {
            // Show single health type using enhanced memory reading
            float healthValue = GetEntityHealthByType(entity, healthType, vmm, pid);
            std::string typeName = GetHealthTypeString(healthType);
            healthText = typeName + ": " + FormatHealthValue(healthValue, healthType);
        }

        // Draw text with background for better visibility
        ImVec2 textSize = ImGui::CalcTextSize(healthText.c_str());
        ImVec2 textPos = ImVec2(screenPos.x - textSize.x / 2, screenPos.y);

        // Draw background
        ImGui::GetBackgroundDrawList()->AddRectFilled(
            ImVec2(textPos.x - 2, textPos.y - 1),
            ImVec2(textPos.x + textSize.x + 2, textPos.y + textSize.y + 1),
            IM_COL32(0, 0, 0, 150) // Semi-transparent black background
        );

        // Draw text
        ImGui::GetBackgroundDrawList()->AddText(textPos, textColor, healthText.c_str());
    }

    std::string HealthBarESP::GetHealthTypeString(int healthType) {
        switch (healthType) {
            case 0: return "Health";
            case 1: return "Blood";
            case 2: return "Shock";
            default: return "Health";
        }
    }

    std::string HealthBarESP::FormatHealthValue(float value, int healthType) {
        switch (healthType) {
            case 0: // Health (0-100)
                return std::to_string((int)value);
            case 1: // Blood (0-5000)
                return std::to_string((int)value);
            case 2: // Shock (0-100)
                return std::to_string((int)value);
            default:
                return std::to_string((int)value);
        }
    }

} // namespace DayZ
