#include "SkeletonESP.h"
#include "../DayZMem/DayZMem.h"
#include "../DayzUtil.h"
#include <imgui.h>
#include <algorithm>
#include <unordered_map>
#include <chrono>
#include <thread>
#include <condition_variable>

namespace DayZ {

    // ===== DETAIL LEVEL FUNCTIONS =====
    std::vector<DayZ::BoneConnection> SkeletonESP::GetPlayerBoneConnections(int detailLevel) {
        switch (detailLevel) {
            case 0: // Minimal - Just basic structure
                return {
                    // Main spine (pelvis to head)
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::spine3},
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::head},
                    
                    // Arms (shoulder to hand)
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::leftshoulder},
                    {(uint32_t)PlayerBoneId::leftshoulder, (uint32_t)PlayerBoneId::lefthand},
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::rightshoulder},
                    {(uint32_t)PlayerBoneId::rightshoulder, (uint32_t)PlayerBoneId::righthand},
                    
                    // Legs (hip to foot)
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::leftupleg},
                    {(uint32_t)PlayerBoneId::leftupleg, (uint32_t)PlayerBoneId::leftfoot},
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::rightupleg},
                    {(uint32_t)PlayerBoneId::rightupleg, (uint32_t)PlayerBoneId::rightfoot}
                };
                
            case 1: // Reduced - Basic + some detail
                return {
                    // Main spine chain
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::spine},
                    {(uint32_t)PlayerBoneId::spine, (uint32_t)PlayerBoneId::spine3},
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::head},
                    
                    // Arms with forearm
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::leftshoulder},
                    {(uint32_t)PlayerBoneId::leftshoulder, (uint32_t)PlayerBoneId::leftarm},
                    {(uint32_t)PlayerBoneId::leftarm, (uint32_t)PlayerBoneId::lefthand},
                    {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::rightshoulder},
                    {(uint32_t)PlayerBoneId::rightshoulder, (uint32_t)PlayerBoneId::rightarm},
                    {(uint32_t)PlayerBoneId::rightarm, (uint32_t)PlayerBoneId::righthand},
                    
                    // Legs with lower leg
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::leftupleg},
                    {(uint32_t)PlayerBoneId::leftupleg, (uint32_t)PlayerBoneId::leftleg},
                    {(uint32_t)PlayerBoneId::leftleg, (uint32_t)PlayerBoneId::leftfoot},
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::rightupleg},
                    {(uint32_t)PlayerBoneId::rightupleg, (uint32_t)PlayerBoneId::rightleg},
                    {(uint32_t)PlayerBoneId::rightleg, (uint32_t)PlayerBoneId::rightfoot}
                };
                
            case 2: // Medium - CheatsBuddy style (default)
                return GetCheatsBuddyPlayerBoneConnections();
                
            case 3: // Full - Maximum detail with hand bones
                return {
                    // Main spine chain (pelvis to head)
                    {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::spine},
                    {(uint32_t)PlayerBoneId::spine, (uint32_t)PlayerBoneId::spine1},
                    {(uint32_t)PlayerBoneId::spine1, (uint32_t)PlayerBoneId::spine2},
                    {(uint32_t)PlayerBoneId::spine2, (uint32_t)PlayerBoneId::spine3},
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::neck},
        {(uint32_t)PlayerBoneId::neck, (uint32_t)PlayerBoneId::head},

                    // Left arm chain with hand detail
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::leftshoulder},
        {(uint32_t)PlayerBoneId::leftshoulder, (uint32_t)PlayerBoneId::leftarm},
        {(uint32_t)PlayerBoneId::leftarm, (uint32_t)PlayerBoneId::leftforearm},
        {(uint32_t)PlayerBoneId::leftforearm, (uint32_t)PlayerBoneId::lefthand},

                    // Left hand detail
                    {(uint32_t)PlayerBoneId::lefthand, (uint32_t)PlayerBoneId::lefthandthumb1},
                    {(uint32_t)PlayerBoneId::lefthandthumb1, (uint32_t)PlayerBoneId::lefthandthumb2},
                    {(uint32_t)PlayerBoneId::lefthand, (uint32_t)PlayerBoneId::lefthandindex1},
                    {(uint32_t)PlayerBoneId::lefthandindex1, (uint32_t)PlayerBoneId::lefthandindex2},
                    {(uint32_t)PlayerBoneId::lefthand, (uint32_t)PlayerBoneId::lefthandmiddle1},
                    {(uint32_t)PlayerBoneId::lefthandmiddle1, (uint32_t)PlayerBoneId::lefthandmiddle2},

                    // Right arm chain with hand detail
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::rightshoulder},
        {(uint32_t)PlayerBoneId::rightshoulder, (uint32_t)PlayerBoneId::rightarm},
        {(uint32_t)PlayerBoneId::rightarm, (uint32_t)PlayerBoneId::rightforearm},
        {(uint32_t)PlayerBoneId::rightforearm, (uint32_t)PlayerBoneId::righthand},

                    // Right hand detail
                    {(uint32_t)PlayerBoneId::righthand, (uint32_t)PlayerBoneId::righthandthumb1},
                    {(uint32_t)PlayerBoneId::righthandthumb1, (uint32_t)PlayerBoneId::righthandthumb2},
                    {(uint32_t)PlayerBoneId::righthand, (uint32_t)PlayerBoneId::righthandindex1},
                    {(uint32_t)PlayerBoneId::righthandindex1, (uint32_t)PlayerBoneId::righthandindex2},
                    {(uint32_t)PlayerBoneId::righthand, (uint32_t)PlayerBoneId::righthandmiddle1},
                    {(uint32_t)PlayerBoneId::righthandmiddle1, (uint32_t)PlayerBoneId::righthandmiddle2},

                    // Left leg chain
        {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::leftupleg},
        {(uint32_t)PlayerBoneId::leftupleg, (uint32_t)PlayerBoneId::leftleg},
        {(uint32_t)PlayerBoneId::leftleg, (uint32_t)PlayerBoneId::leftfoot},

                    // Right leg chain
        {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::rightupleg},
        {(uint32_t)PlayerBoneId::rightupleg, (uint32_t)PlayerBoneId::rightleg},
        {(uint32_t)PlayerBoneId::rightleg, (uint32_t)PlayerBoneId::rightfoot}
    };

            default:
                return PLAYER_BONE_CONNECTIONS; // Default to medium
        }
    }

    std::vector<DayZ::BoneConnection> SkeletonESP::GetZombieBoneConnections(int detailLevel) {
        switch (detailLevel) {
            case 0: // Minimal - Just basic structure
                return {
                    // Main spine (pelvis to head)
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::spine3},
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::head},
                    
                    // Arms (shoulder to hand)
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::leftshoulder},
                    {(uint32_t)InfectedBoneId::leftshoulder, (uint32_t)InfectedBoneId::lefthand},
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::rightshoulder},
                    {(uint32_t)InfectedBoneId::rightshoulder, (uint32_t)InfectedBoneId::righthand},
                    
                    // Legs (hip to foot)
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::leftupleg},
                    {(uint32_t)InfectedBoneId::leftupleg, (uint32_t)InfectedBoneId::leftfoot},
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::rightupleg},
                    {(uint32_t)InfectedBoneId::rightupleg, (uint32_t)InfectedBoneId::rightfoot}
                };
                
            case 1: // Reduced - Basic + some detail
                return {
                    // Main spine chain
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::spine},
                    {(uint32_t)InfectedBoneId::spine, (uint32_t)InfectedBoneId::spine3},
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::head},
                    
                    // Arms with forearm
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::leftshoulder},
                    {(uint32_t)InfectedBoneId::leftshoulder, (uint32_t)InfectedBoneId::leftarm},
                    {(uint32_t)InfectedBoneId::leftarm, (uint32_t)InfectedBoneId::lefthand},
                    {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::rightshoulder},
                    {(uint32_t)InfectedBoneId::rightshoulder, (uint32_t)InfectedBoneId::rightarm},
                    {(uint32_t)InfectedBoneId::rightarm, (uint32_t)InfectedBoneId::righthand},
                    
                    // Legs with lower leg
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::leftupleg},
                    {(uint32_t)InfectedBoneId::leftupleg, (uint32_t)InfectedBoneId::leftleg},
                    {(uint32_t)InfectedBoneId::leftleg, (uint32_t)InfectedBoneId::leftfoot},
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::rightupleg},
                    {(uint32_t)InfectedBoneId::rightupleg, (uint32_t)InfectedBoneId::rightleg},
                    {(uint32_t)InfectedBoneId::rightleg, (uint32_t)InfectedBoneId::rightfoot}
                };
                
            case 2: // Medium - CheatsBuddy style (default)
                return GetCheatsBuddyZombieBoneConnections();
                
            case 3: // Full - Maximum detail with hand bones
                return {
                    // Main spine chain (pelvis to head)
                    {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::spine},
                    {(uint32_t)InfectedBoneId::spine, (uint32_t)InfectedBoneId::spine1},
                    {(uint32_t)InfectedBoneId::spine1, (uint32_t)InfectedBoneId::spine2},
                    {(uint32_t)InfectedBoneId::spine2, (uint32_t)InfectedBoneId::spine3},
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::neck},
        {(uint32_t)InfectedBoneId::neck, (uint32_t)InfectedBoneId::head},

                    // Left arm chain with hand detail
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::leftshoulder},
        {(uint32_t)InfectedBoneId::leftshoulder, (uint32_t)InfectedBoneId::leftarm},
        {(uint32_t)InfectedBoneId::leftarm, (uint32_t)InfectedBoneId::leftforearm},
        {(uint32_t)InfectedBoneId::leftforearm, (uint32_t)InfectedBoneId::lefthand},

                    // Left hand detail
                    {(uint32_t)InfectedBoneId::lefthand, (uint32_t)InfectedBoneId::lefthandthumb1},
                    {(uint32_t)InfectedBoneId::lefthandthumb1, (uint32_t)InfectedBoneId::lefthandthumb2},
                    {(uint32_t)InfectedBoneId::lefthand, (uint32_t)InfectedBoneId::lefthandindex1},
                    {(uint32_t)InfectedBoneId::lefthandindex1, (uint32_t)InfectedBoneId::lefthandindex2},
                    {(uint32_t)InfectedBoneId::lefthand, (uint32_t)InfectedBoneId::lefthandmiddle1},
                    {(uint32_t)InfectedBoneId::lefthandmiddle1, (uint32_t)InfectedBoneId::lefthandmiddle2},

                    // Right arm chain with hand detail
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::rightshoulder},
        {(uint32_t)InfectedBoneId::rightshoulder, (uint32_t)InfectedBoneId::rightarm},
        {(uint32_t)InfectedBoneId::rightarm, (uint32_t)InfectedBoneId::rightforearm},
        {(uint32_t)InfectedBoneId::rightforearm, (uint32_t)InfectedBoneId::righthand},

                    // Right hand detail
                    {(uint32_t)InfectedBoneId::righthand, (uint32_t)InfectedBoneId::righthandthumb1},
                    {(uint32_t)InfectedBoneId::righthandthumb1, (uint32_t)InfectedBoneId::righthandthumb2},
                    {(uint32_t)InfectedBoneId::righthand, (uint32_t)InfectedBoneId::righthandindex1},
                    {(uint32_t)InfectedBoneId::righthandindex1, (uint32_t)InfectedBoneId::righthandindex2},
                    {(uint32_t)InfectedBoneId::righthand, (uint32_t)InfectedBoneId::righthandmiddle1},
                    {(uint32_t)InfectedBoneId::righthandmiddle1, (uint32_t)InfectedBoneId::righthandmiddle2},

                    // Left leg chain
        {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::leftupleg},
        {(uint32_t)InfectedBoneId::leftupleg, (uint32_t)InfectedBoneId::leftleg},
        {(uint32_t)InfectedBoneId::leftleg, (uint32_t)InfectedBoneId::leftfoot},

                    // Right leg chain
        {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::rightupleg},
        {(uint32_t)InfectedBoneId::rightupleg, (uint32_t)InfectedBoneId::rightleg},
        {(uint32_t)InfectedBoneId::rightleg, (uint32_t)InfectedBoneId::rightfoot}
    };
                
            default:
                return ZOMBIE_BONE_CONNECTIONS; // Default to medium
        }
    }





    // ===== CORE BONE POSITION FUNCTION (1:1 REBUILD FROM GAME) =====
    bool SkeletonESP::GetBonePositionWS(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton, uintptr_t visState, uint32_t pivot, DMARender::Vector3* pos) {
        if (!vmm || !skeleton || !visState || !pos) {
            return false;
        }

        // Get animation class
        uintptr_t animClass = vmm->readMemoryDirect<uintptr_t>(pid, skeleton + ANIM_CLASS_OFFSET);
        if (!DayZUtil::isPointerValid(animClass)) {
            return false;
        }

        // Get matrix class
        uintptr_t matrixClass = vmm->readMemoryDirect<uintptr_t>(pid, animClass + MATRIX_CLASS_OFFSET);
        if (!DayZUtil::isPointerValid(matrixClass)) {
            return false;
        }

        // Read bone matrix data (36 floats)
        float v8[36];
        if (!vmm->readMemory(pid, matrixClass + sizeof(Matrix4x4) * pivot, &v8, sizeof(v8))) {
            return false;
        }

        // Read visual state matrix
        Matrix4x4 m1;
        if (!vmm->readMemory(pid, visState + 0x8, &m1, sizeof(Matrix4x4))) {
            return false;
        }

        // Calculate world space position using matrix transformation
        pos->z = (v8[10] * m1.m[5]) + (v8[9] * m1.m[2]) + (v8[11] * m1.m[8]) + m1.m[11];
        pos->y = (v8[10] * m1.m[4]) + (v8[9] * m1.m[1]) + (v8[11] * m1.m[7]) + m1.m[10];
        pos->x = (v8[10] * m1.m[3]) + (v8[9] * m1.m[0]) + (v8[11] * m1.m[6]) + m1.m[9];

        return true;
    }

    // ===== PLAYER SKELETON FUNCTIONS =====
    bool SkeletonESP::GetPlayerBonePosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity, PlayerBoneId boneId, DMARender::Vector3* pos) {
        if (!vmm || !entity || !entity->FutureVisualStatePtr) {
            return false;
        }

        // DayZ2-style direct calculation (no caching)
        uintptr_t skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + PLAYER_SKELETON_OFFSET);
        if (!IsValidSkeleton(vmm, pid, skeleton)) {
            return false;
        }

        return GetBonePositionWS(vmm, pid, skeleton, entity->FutureVisualStatePtr->_remoteAddress, static_cast<uint32_t>(boneId), pos);
    }

    std::vector<DMARender::Vector3> SkeletonESP::GetPlayerSkeleton(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        std::vector<DMARender::Vector3> bones;

        if (!vmm || !entity) {
            return bones;
        }

        // Get all major bone positions
        std::vector<PlayerBoneId> majorBones = {
            PlayerBoneId::head, PlayerBoneId::neck, PlayerBoneId::spine3, PlayerBoneId::spine2,
            PlayerBoneId::spine1, PlayerBoneId::spine, PlayerBoneId::pelvis,
            PlayerBoneId::leftshoulder, PlayerBoneId::leftarm, PlayerBoneId::leftforearm, PlayerBoneId::lefthand,
            PlayerBoneId::rightshoulder, PlayerBoneId::rightarm, PlayerBoneId::rightforearm, PlayerBoneId::righthand,
            PlayerBoneId::leftupleg, PlayerBoneId::leftleg, PlayerBoneId::leftfoot, PlayerBoneId::lefttoebase,
            PlayerBoneId::rightupleg, PlayerBoneId::rightleg, PlayerBoneId::rightfoot, PlayerBoneId::righttoebase
        };

        for (auto boneId : majorBones) {
            DMARender::Vector3 bonePos;
            if (GetPlayerBonePosition(vmm, pid, entity, boneId, &bonePos)) {
                bones.push_back(bonePos);
            }
        }

        return bones;
    }

    // ===== ZOMBIE SKELETON FUNCTIONS =====
    bool SkeletonESP::GetZombieBonePosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity, InfectedBoneId boneId, DMARender::Vector3* pos) {
        if (!vmm || !entity || !entity->FutureVisualStatePtr) {
            return false;
        }

        // DayZ2-style direct calculation (no caching)
        uintptr_t skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + ZOMBIE_SKELETON_OFFSET);
        if (!IsValidSkeleton(vmm, pid, skeleton)) {
            return false;
        }

        return GetBonePositionWS(vmm, pid, skeleton, entity->FutureVisualStatePtr->_remoteAddress, static_cast<uint32_t>(boneId), pos);
    }

    std::vector<DMARender::Vector3> SkeletonESP::GetZombieSkeleton(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        std::vector<DMARender::Vector3> bones;

        if (!vmm || !entity) {
            return bones;
        }

        // Get all major bone positions
        std::vector<InfectedBoneId> majorBones = {
            InfectedBoneId::head, InfectedBoneId::neck, InfectedBoneId::spine3, InfectedBoneId::spine2,
            InfectedBoneId::spine1, InfectedBoneId::spine, InfectedBoneId::pelvis,
            InfectedBoneId::leftshoulder, InfectedBoneId::leftarm, InfectedBoneId::leftforearm, InfectedBoneId::lefthand,
            InfectedBoneId::rightshoulder, InfectedBoneId::rightarm, InfectedBoneId::rightforearm, InfectedBoneId::righthand,
            InfectedBoneId::leftupleg, InfectedBoneId::leftleg, InfectedBoneId::leftfoot, InfectedBoneId::lefttoebase,
            InfectedBoneId::rightupleg, InfectedBoneId::rightleg, InfectedBoneId::rightfoot, InfectedBoneId::righttoebase
        };

        for (auto boneId : majorBones) {
            DMARender::Vector3 bonePos;
            if (GetZombieBonePosition(vmm, pid, entity, boneId, &bonePos)) {
                bones.push_back(bonePos);
            }
        }

        return bones;
    }

    // ===== DAYZ2-STYLE RENDERING FUNCTIONS (HIGH PERFORMANCE) =====
    void SkeletonESP::DrawPlayerSkeleton(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // DayZ2-style simple approach - no distance checks, no caching, direct rendering
        const auto& connections = PLAYER_BONE_CONNECTIONS;

        // Draw bone connections directly (DayZ2 style)
        for (const auto& connection : connections) {
            DMARender::Vector3 fromPos, toPos;

            // Get bone positions directly (no caching like DayZ2)
            if (!GetPlayerBonePosition(vmm, pid, entity, static_cast<PlayerBoneId>(connection.from), &fromPos) ||
                !GetPlayerBonePosition(vmm, pid, entity, static_cast<PlayerBoneId>(connection.to), &toPos)) {
                continue;
            }

            DrawBoneLine(camera, fromPos, toPos, color, thickness);
        }
    }

    void SkeletonESP::DrawZombieSkeleton(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // DayZ2-style simple approach - no distance checks, no caching, direct rendering
        const auto& connections = ZOMBIE_BONE_CONNECTIONS;

        // Draw bone connections directly (DayZ2 style)
        for (const auto& connection : connections) {
            DMARender::Vector3 fromPos, toPos;

            // Get bone positions directly (no caching like DayZ2)
            if (!GetZombieBonePosition(vmm, pid, entity, static_cast<InfectedBoneId>(connection.from), &fromPos) ||
                !GetZombieBonePosition(vmm, pid, entity, static_cast<InfectedBoneId>(connection.to), &toPos)) {
                continue;
            }

            DrawBoneLine(camera, fromPos, toPos, color, thickness);
        }
    }



    // ===== UTILITY FUNCTIONS =====
    bool SkeletonESP::IsValidSkeleton(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton) {
        if (!vmm || !skeleton || !DayZUtil::isPointerValid(skeleton)) {
            return false;
        }

        uintptr_t animClass = vmm->readMemoryDirect<uintptr_t>(pid, skeleton + ANIM_CLASS_OFFSET);
        return DayZUtil::isPointerValid(animClass);
    }

    DMARender::Vector3 SkeletonESP::GetHeadPosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        DMARender::Vector3 headPos = {0, 0, 0};

        // Try player head first
        if (GetPlayerBonePosition(vmm, pid, entity, PlayerBoneId::head, &headPos)) {
            return headPos;
        }

        // Try zombie head
        GetZombieBonePosition(vmm, pid, entity, InfectedBoneId::head, &headPos);
        return headPos;
    }

    DMARender::Vector3 SkeletonESP::GetChestPosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        DMARender::Vector3 chestPos = {0, 0, 0};

        // Try player chest (spine3)
        if (GetPlayerBonePosition(vmm, pid, entity, PlayerBoneId::spine3, &chestPos)) {
            return chestPos;
        }

        // Try zombie chest
        GetZombieBonePosition(vmm, pid, entity, InfectedBoneId::spine3, &chestPos);
        return chestPos;
    }

    // ===== HELPER FUNCTIONS =====
    bool SkeletonESP::ShouldSkipSkeletonProcessing(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, bool isPlayer, float maxDistance) {
        if (!camera || !entity || !entity->FutureVisualStatePtr) {
            return true; // Skip if invalid parameters
        }

        // Calculate distance from camera to entity position
        float distance = camera->InvertedViewTranslation.Dist(entity->FutureVisualStatePtr->position);
        
        // Skip processing for entities beyond the specified distance limit
        if (distance > maxDistance) {
            return true;
        }

        // Additional performance checks
        if (distance < 1.0f) {
            return true; // Skip entities too close (likely invalid)
        }

        return false; // Process this entity
    }

    bool SkeletonESP::WorldToScreen(DayZ::Camera* camera, const DMARender::Vector3& worldPos, DMARender::Vector2* screenPos) {
        if (!camera || !screenPos) {
            return false;
        }

        // Use the same WorldToScreen implementation as OverlayAdapter
        // IMPROVED PRECISION: Use double precision for better accuracy at distance
        DMARender::Vector3 temp = DMARender::Vector3(
            worldPos.x - camera->InvertedViewTranslation.x,
            worldPos.y - camera->InvertedViewTranslation.y,
            worldPos.z - camera->InvertedViewTranslation.z
        );

        double x = temp.Dot(camera->InvertedViewRight);
        double y = temp.Dot(camera->InvertedViewUp);
        double z = temp.Dot(camera->InvertedViewForward);

        // IMPROVED DEPTH CHECK: Better near plane handling
        if (z < 0.1) return false;

        ImVec2 windowSize = ImGui::GetWindowSize();

        // IMPROVED PROJECTION: Higher precision calculations for better accuracy
        double normalizedX = (x / camera->GetProjectionD1.x) / z;
        double normalizedY = (y / camera->GetProjectionD2.y) / z;

        // IMPROVED SCREEN MAPPING: More precise screen coordinate calculation
        screenPos->x = (float)((windowSize.x * 0.5) + (normalizedX * (windowSize.x * 0.5)));
        screenPos->y = (float)((windowSize.y * 0.5) - (normalizedY * (windowSize.y * 0.5)));

        // BOUNDS CHECK: Ensure coordinates are within reasonable screen bounds
        if (screenPos->x < -100 || screenPos->x > windowSize.x + 100 ||
            screenPos->y < -100 || screenPos->y > windowSize.y + 100) {
            return false;
        }

        return true;
    }

    void SkeletonESP::DrawBoneLine(DayZ::Camera* camera, const DMARender::Vector3& from, const DMARender::Vector3& to, ImU32 color, float thickness) {
        if (!camera) return;

        // Convert world positions to screen coordinates
        DMARender::Vector2 fromScreen, toScreen;

        // Use the same WorldToScreen function as the overlay
        if (SkeletonESP::WorldToScreen(camera, from, &fromScreen) &&
            SkeletonESP::WorldToScreen(camera, to, &toScreen)) {

            // Draw the bone line on screen
            ImGui::GetWindowDrawList()->AddLine(
                ImVec2(fromScreen.x, fromScreen.y),
                ImVec2(toScreen.x, toScreen.y),
                color,
                thickness
            );
        }
    }

    // ===== HEAD CIRCLE DRAWING (DayZ mod.txt style) =====
    void SkeletonESP::DrawHeadCircle(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness, float radiusMultiplier, float sizeMultiplier) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // Get head and neck positions (like DayZ mod.txt)
        DMARender::Vector3 headPos, neckPos;
        bool gotHead = false, gotNeck = false;

        // Try player bones first
        gotHead = GetPlayerBonePosition(vmm, pid, entity, PlayerBoneId::head, &headPos);
        gotNeck = GetPlayerBonePosition(vmm, pid, entity, PlayerBoneId::neck, &neckPos);

        // If not player, try zombie bones
        if (!gotHead || !gotNeck) {
            gotHead = GetZombieBonePosition(vmm, pid, entity, InfectedBoneId::head, &headPos);
            gotNeck = GetZombieBonePosition(vmm, pid, entity, InfectedBoneId::neck, &neckPos);
        }

        if (!gotHead || !gotNeck) {
            return; // Can't draw circle without both positions
        }

        // Convert to screen coordinates
        DMARender::Vector2 headScreen, neckScreen;
        if (!SkeletonESP::WorldToScreen(camera, headPos, &headScreen) ||
            !SkeletonESP::WorldToScreen(camera, neckPos, &neckScreen)) {
            return; // Can't draw if not visible on screen
        }

        // Calculate distance from camera to head
        float distanceToHead = camera->InvertedViewTranslation.Dist(headPos);
        
        // Base radius that scales inversely with distance
        float baseRadius = 8.0f; // Base radius at 1 meter distance
        
        // Scale radius inversely with distance (bigger when close, smaller when far)
        float radius = baseRadius * (1.0f / distanceToHead) * 0.5f; // 0.5f scaling factor
        
        // Apply size multiplier from settings
        radius *= sizeMultiplier;
        
        // Clamp to reasonable bounds
        if (radius < 2.0f) radius = 2.0f;   // Minimum visible size
        if (radius > 30.0f) radius = 30.0f; // Maximum size

        // Draw circle around head (DayZ mod.txt style)
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        const int segments = 36; // 36 segments for smooth circle (10 degrees each)
        
        for (int i = 0; i < segments; i++) {
            float angle1 = (float)i * (2.0f * 3.14159f / segments);
            float angle2 = (float)(i + 1) * (2.0f * 3.14159f / segments);
            
            float x1 = headScreen.x + (radius * cosf(angle1));
            float y1 = headScreen.y + (radius * sinf(angle1));
            float x2 = headScreen.x + (radius * cosf(angle2));
            float y2 = headScreen.y + (radius * sinf(angle2));
            
            drawList->AddLine(
                ImVec2(x1, y1),
                ImVec2(x2, y2),
                color,
                thickness
            );
        }
    }

    // ===== NEW BATCHED BONE READING IMPLEMENTATION (HIGH PERFORMANCE) =====

    std::vector<uint32_t> SkeletonESP::GetPlayerBoneIds() {
        // Return all bone IDs needed for skeleton rendering
        return {
            // Main body chain
            static_cast<uint32_t>(PlayerBoneId::head),
            static_cast<uint32_t>(PlayerBoneId::neck),
            static_cast<uint32_t>(PlayerBoneId::spine3),
            static_cast<uint32_t>(PlayerBoneId::spine2),
            static_cast<uint32_t>(PlayerBoneId::spine1),
            static_cast<uint32_t>(PlayerBoneId::spine),
            static_cast<uint32_t>(PlayerBoneId::pelvis),
            
            // Left arm chain
            static_cast<uint32_t>(PlayerBoneId::leftshoulder),
            static_cast<uint32_t>(PlayerBoneId::leftarm),
            static_cast<uint32_t>(PlayerBoneId::leftforearm),
            static_cast<uint32_t>(PlayerBoneId::lefthand),
            
            // Right arm chain
            static_cast<uint32_t>(PlayerBoneId::rightshoulder),
            static_cast<uint32_t>(PlayerBoneId::rightarm),
            static_cast<uint32_t>(PlayerBoneId::rightforearm),
            static_cast<uint32_t>(PlayerBoneId::righthand),
            
            // Left leg chain
            static_cast<uint32_t>(PlayerBoneId::leftupleg),
            static_cast<uint32_t>(PlayerBoneId::leftleg),
            static_cast<uint32_t>(PlayerBoneId::leftfoot),
            static_cast<uint32_t>(PlayerBoneId::lefttoebase),
            
            // Right leg chain
            static_cast<uint32_t>(PlayerBoneId::rightupleg),
            static_cast<uint32_t>(PlayerBoneId::rightleg),
            static_cast<uint32_t>(PlayerBoneId::rightfoot),
            static_cast<uint32_t>(PlayerBoneId::righttoebase)
        };
    }

    std::vector<uint32_t> SkeletonESP::GetZombieBoneIds() {
        // Return all bone IDs needed for skeleton rendering
        return {
            // Main body chain
            static_cast<uint32_t>(InfectedBoneId::head),
            static_cast<uint32_t>(InfectedBoneId::neck),
            static_cast<uint32_t>(InfectedBoneId::spine3),
            static_cast<uint32_t>(InfectedBoneId::spine2),
            static_cast<uint32_t>(InfectedBoneId::spine1),
            static_cast<uint32_t>(InfectedBoneId::spine),
            static_cast<uint32_t>(InfectedBoneId::pelvis),
            
            // Left arm chain
            static_cast<uint32_t>(InfectedBoneId::leftshoulder),
            static_cast<uint32_t>(InfectedBoneId::leftarm),
            static_cast<uint32_t>(InfectedBoneId::leftforearm),
            static_cast<uint32_t>(InfectedBoneId::lefthand),
            
            // Right arm chain
            static_cast<uint32_t>(InfectedBoneId::rightshoulder),
            static_cast<uint32_t>(InfectedBoneId::rightarm),
            static_cast<uint32_t>(InfectedBoneId::rightforearm),
            static_cast<uint32_t>(InfectedBoneId::righthand),
            
            // Left leg chain
            static_cast<uint32_t>(InfectedBoneId::leftupleg),
            static_cast<uint32_t>(InfectedBoneId::leftleg),
            static_cast<uint32_t>(InfectedBoneId::leftfoot),
            static_cast<uint32_t>(InfectedBoneId::lefttoebase),
            
            // Right leg chain
            static_cast<uint32_t>(InfectedBoneId::rightupleg),
            static_cast<uint32_t>(InfectedBoneId::rightleg),
            static_cast<uint32_t>(InfectedBoneId::rightfoot),
            static_cast<uint32_t>(InfectedBoneId::righttoebase)
        };
    }

    // ===== CORE BATCHED BONE READING FUNCTION =====
    // This is the key optimization - reads ALL bone matrices in a single DMA operation
    bool SkeletonESP::GetBonePositionsBatched(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton, uintptr_t visState, 
                                             const std::vector<uint32_t>& boneIds, BatchedBoneData* result) {
        if (!vmm || !skeleton || !visState || !result || boneIds.empty()) {
            return false;
        }

        // Initialize result
        result->bonePositions.clear();
        result->boneIndexMap.clear();
        result->isValid = false;
        result->timestamp = std::chrono::steady_clock::now();

        // STEP 1: Get animation class (1 DMA read)
        uintptr_t animClass = vmm->readMemoryDirect<uintptr_t>(pid, skeleton + ANIM_CLASS_OFFSET);
        if (!DayZUtil::isPointerValid(animClass)) {
            return false;
        }

        // STEP 2: Get matrix class (1 DMA read)
        uintptr_t matrixClass = vmm->readMemoryDirect<uintptr_t>(pid, animClass + MATRIX_CLASS_OFFSET);
        if (!DayZUtil::isPointerValid(matrixClass)) {
            return false;
        }

        // STEP 3: Read visual state matrix (1 DMA read - shared for all bones)
        Matrix4x4 visStateMatrix;
        if (!vmm->readMemory(pid, visState + 0x8, &visStateMatrix, sizeof(Matrix4x4))) {
            return false;
        }

        // STEP 4: Calculate total size needed for all bone matrices
        size_t maxBoneId = 0;
        for (uint32_t boneId : boneIds) {
            if (boneId > maxBoneId) maxBoneId = boneId;
        }
        
        // Calculate total memory size for all bone matrices up to maxBoneId
        size_t totalMatrixSize = (maxBoneId + 1) * sizeof(Matrix4x4);
        
        // STEP 5: Read ALL bone matrices in a single DMA operation (MASSIVE OPTIMIZATION)
        std::vector<Matrix4x4> allBoneMatrices(maxBoneId + 1);
        if (!vmm->readMemory(pid, matrixClass, allBoneMatrices.data(), totalMatrixSize)) {
            return false;
        }

        // STEP 6: Process all bone positions using the batched data
        result->bonePositions.reserve(boneIds.size());
        
        for (uint32_t boneId : boneIds) {
            if (boneId >= allBoneMatrices.size()) {
                continue; // Skip invalid bone IDs
            }

            const Matrix4x4& boneMatrix = allBoneMatrices[boneId];
            
            // Calculate world space position using matrix transformation
            DMARender::Vector3 bonePos;
            bonePos.z = (boneMatrix.m[10] * visStateMatrix.m[5]) + (boneMatrix.m[9] * visStateMatrix.m[2]) + (boneMatrix.m[11] * visStateMatrix.m[8]) + visStateMatrix.m[11];
            bonePos.y = (boneMatrix.m[10] * visStateMatrix.m[4]) + (boneMatrix.m[9] * visStateMatrix.m[1]) + (boneMatrix.m[11] * visStateMatrix.m[7]) + visStateMatrix.m[10];
            bonePos.x = (boneMatrix.m[10] * visStateMatrix.m[3]) + (boneMatrix.m[9] * visStateMatrix.m[0]) + (boneMatrix.m[11] * visStateMatrix.m[6]) + visStateMatrix.m[9];

            // Store bone position and create index mapping
            result->boneIndexMap[boneId] = result->bonePositions.size();
            result->bonePositions.push_back(bonePos);
        }

        result->isValid = true;
        return true;
    }

    // ===== BATCHED SKELETON FUNCTIONS =====
    BatchedBoneData SkeletonESP::GetPlayerSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        BatchedBoneData result;
        
        if (!vmm || !entity || !entity->FutureVisualStatePtr) {
            return result;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any DMA operations
        // This prevents expensive bone matrix reads for distant entities
        // Note: We need camera for distance calculation, but we can use a reasonable hard limit
        // The actual distance check happens in the rendering function with proper camera access
        // This is a secondary safety check to prevent extreme distance processing

        // Get skeleton pointer
        uintptr_t skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + PLAYER_SKELETON_OFFSET);
        if (!IsValidSkeleton(vmm, pid, skeleton)) {
            return result;
        }

        // Get bone IDs based on detail level (we'll use full detail for batched reading)
        std::vector<uint32_t> boneIds = GetPlayerBoneIds();
        
        // Read all bone positions in single batch operation
        GetBonePositionsBatched(vmm, pid, skeleton, entity->FutureVisualStatePtr->_remoteAddress, boneIds, &result);
        
        return result;
    }

    BatchedBoneData SkeletonESP::GetZombieSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity) {
        BatchedBoneData result;
        
        if (!vmm || !entity || !entity->FutureVisualStatePtr) {
            return result;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any DMA operations
        // This prevents expensive bone matrix reads for distant entities
        // Note: We need camera for distance calculation, but we can use a reasonable hard limit
        // The actual distance check happens in the rendering function with proper camera access
        // This is a secondary safety check to prevent extreme distance processing

        // Get skeleton pointer
        uintptr_t skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + ZOMBIE_SKELETON_OFFSET);
        if (!IsValidSkeleton(vmm, pid, skeleton)) {
            return result;
        }

        // Get bone IDs based on detail level (we'll use full detail for batched reading)
        std::vector<uint32_t> boneIds = GetZombieBoneIds();
        
        // Read all bone positions in single batch operation
        GetBonePositionsBatched(vmm, pid, skeleton, entity->FutureVisualStatePtr->_remoteAddress, boneIds, &result);
        
        return result;
    }

    // ===== OPTIMIZED RENDERING FUNCTIONS USING BATCHED DATA =====
    void SkeletonESP::DrawPlayerSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // Get all bone positions in single batch operation
        BatchedBoneData boneData = GetPlayerSkeletonBatched(vmm, pid, entity);
        if (!boneData.isValid || boneData.bonePositions.empty()) {
            return;
        }

        // Draw bone connections using pre-calculated positions
        const auto& connections = PLAYER_BONE_CONNECTIONS;
        
        for (const auto& connection : connections) {
            // Get bone positions from batched data
            auto fromIt = boneData.boneIndexMap.find(connection.from);
            auto toIt = boneData.boneIndexMap.find(connection.to);
            
            if (fromIt == boneData.boneIndexMap.end() || toIt == boneData.boneIndexMap.end()) {
                continue; // Skip if bone not found in batch
            }

            const DMARender::Vector3& fromPos = boneData.bonePositions[fromIt->second];
            const DMARender::Vector3& toPos = boneData.bonePositions[toIt->second];

            // Draw the bone line
            DrawBoneLine(camera, fromPos, toPos, color, thickness);
        }
    }

    void SkeletonESP::DrawZombieSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // Get all bone positions in single batch operation
        BatchedBoneData boneData = GetZombieSkeletonBatched(vmm, pid, entity);
        if (!boneData.isValid || boneData.bonePositions.empty()) {
            return;
        }

        // Draw bone connections using pre-calculated positions
        const auto& connections = ZOMBIE_BONE_CONNECTIONS;
        
        for (const auto& connection : connections) {
            // Get bone positions from batched data
            auto fromIt = boneData.boneIndexMap.find(connection.from);
            auto toIt = boneData.boneIndexMap.find(connection.to);
            
            if (fromIt == boneData.boneIndexMap.end() || toIt == boneData.boneIndexMap.end()) {
                continue; // Skip if bone not found in batch
            }

            const DMARender::Vector3& fromPos = boneData.bonePositions[fromIt->second];
            const DMARender::Vector3& toPos = boneData.bonePositions[toIt->second];

            // Draw the bone line
            DrawBoneLine(camera, fromPos, toPos, color, thickness);
        }
    }

    // ===== ULTRA-HIGH PERFORMANCE BATCHED WORLD TO SCREEN IMPLEMENTATION =====

    // ===== CORE BATCHED WORLD TO SCREEN FUNCTION =====
    // This is the key optimization - calculates ALL screen positions in a single operation
    bool SkeletonESP::WorldToScreenBatched(DayZ::Camera* camera, const std::vector<DMARender::Vector3>& worldPositions, 
                                          BatchedScreenData* result) {
        if (!camera || !result || worldPositions.empty()) {
            return false;
        }

        // Initialize result
        result->screenPositions.clear();
        result->isVisible.clear();
        result->boneIndexMap.clear();
        result->isValid = false;
        result->timestamp = std::chrono::steady_clock::now();

        // Pre-allocate vectors for performance
        result->screenPositions.reserve(worldPositions.size());
        result->isVisible.reserve(worldPositions.size());

        // Get window size once (shared for all calculations)
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Pre-calculate camera transformation constants (shared for all positions)
        double windowHalfX = windowSize.x * 0.5;
        double windowHalfY = windowSize.y * 0.5;
        double projectionD1X = camera->GetProjectionD1.x;
        double projectionD2Y = camera->GetProjectionD2.y;

        // Process all world positions in a single batch
        for (size_t i = 0; i < worldPositions.size(); ++i) {
            const DMARender::Vector3& worldPos = worldPositions[i];
            
            // Calculate relative position to camera
            DMARender::Vector3 temp = DMARender::Vector3(
                worldPos.x - camera->InvertedViewTranslation.x,
                worldPos.y - camera->InvertedViewTranslation.y,
                worldPos.z - camera->InvertedViewTranslation.z
            );

            // Calculate dot products (shared calculations)
            double x = temp.Dot(camera->InvertedViewRight);
            double y = temp.Dot(camera->InvertedViewUp);
            double z = temp.Dot(camera->InvertedViewForward);

            // Check if position is behind camera or too close
            if (z < 0.1) {
                result->screenPositions.push_back(DMARender::Vector2(0, 0));
                result->isVisible.push_back(false);
                continue;
            }

            // Calculate normalized screen coordinates
            double normalizedX = (x / projectionD1X) / z;
            double normalizedY = (y / projectionD2Y) / z;

            // Calculate final screen coordinates
            float screenX = (float)(windowHalfX + (normalizedX * windowHalfX));
            float screenY = (float)(windowHalfY - (normalizedY * windowHalfY));

            // Check if position is within reasonable screen bounds
            bool isVisible = (screenX >= -100 && screenX <= windowSize.x + 100 &&
                             screenY >= -100 && screenY <= windowSize.y + 100);

            result->screenPositions.push_back(DMARender::Vector2(screenX, screenY));
            result->isVisible.push_back(isVisible);
        }

        result->isValid = true;
        return true;
    }

    // ===== ULTRA-HIGH PERFORMANCE SKELETON FUNCTIONS =====
    BatchedScreenData SkeletonESP::GetPlayerSkeletonScreenBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity) {
        BatchedScreenData result;
        
        if (!vmm || !camera || !entity) {
            return result;
        }

        // Get all bone positions in single batch operation
        BatchedBoneData boneData = GetPlayerSkeletonBatched(vmm, pid, entity);
        if (!boneData.isValid || boneData.bonePositions.empty()) {
            return result;
        }

        // Calculate all screen positions in single batch operation
        WorldToScreenBatched(camera, boneData.bonePositions, &result);
        
        // Copy bone index mapping
        result.boneIndexMap = boneData.boneIndexMap;
        
        return result;
    }

    BatchedScreenData SkeletonESP::GetZombieSkeletonScreenBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity) {
        BatchedScreenData result;
        
        if (!vmm || !camera || !entity) {
            return result;
        }

        // Get all bone positions in single batch operation
        BatchedBoneData boneData = GetZombieSkeletonBatched(vmm, pid, entity);
        if (!boneData.isValid || boneData.bonePositions.empty()) {
            return result;
        }

        // Calculate all screen positions in single batch operation
        WorldToScreenBatched(camera, boneData.bonePositions, &result);
        
        // Copy bone index mapping
        result.boneIndexMap = boneData.boneIndexMap;
        
        return result;
    }

    // ===== ULTRA-OPTIMIZED RENDERING FUNCTIONS =====
    void SkeletonESP::DrawPlayerSkeletonUltraBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness, int detailLevel) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any bone processing
        // Use intelligent distance-based processing control
        if (ShouldSkipSkeletonProcessing(camera, entity, true, 1000.0f)) { // 1000m hard limit for players
            return;
        }

        // Get all bone screen positions in single ultra-batch operation
        BatchedScreenData screenData = GetPlayerSkeletonScreenBatched(vmm, pid, camera, entity);
        if (!screenData.isValid || screenData.screenPositions.empty()) {
            return;
        }

        // Draw bone connections using pre-calculated screen positions
        const auto& connections = GetPlayerBoneConnections(detailLevel);
        
        for (const auto& connection : connections) {
            // Get bone screen positions from batched data
            auto fromIt = screenData.boneIndexMap.find(connection.from);
            auto toIt = screenData.boneIndexMap.find(connection.to);
            
            if (fromIt == screenData.boneIndexMap.end() || toIt == screenData.boneIndexMap.end()) {
                continue; // Skip if bone not found in batch
            }

            size_t fromIndex = fromIt->second;
            size_t toIndex = toIt->second;

            // Check if both bone positions are visible
            if (!screenData.isVisible[fromIndex] || !screenData.isVisible[toIndex]) {
                continue; // Skip if either bone is not visible
            }

            const DMARender::Vector2& fromScreen = screenData.screenPositions[fromIndex];
            const DMARender::Vector2& toScreen = screenData.screenPositions[toIndex];

            // Draw the bone line directly using pre-calculated screen coordinates
            ImGui::GetWindowDrawList()->AddLine(
                ImVec2(fromScreen.x, fromScreen.y),
                ImVec2(toScreen.x, toScreen.y),
                color,
                thickness
            );
        }
    }

    void SkeletonESP::DrawZombieSkeletonUltraBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness, int detailLevel) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any bone processing
        // Use intelligent distance-based processing control
        if (ShouldSkipSkeletonProcessing(camera, entity, false, 500.0f)) { // 500m hard limit for zombies
            return;
        }

        // Get all bone screen positions in single ultra-batch operation
        BatchedScreenData screenData = GetZombieSkeletonScreenBatched(vmm, pid, camera, entity);
        if (!screenData.isValid || screenData.screenPositions.empty()) {
            return;
        }

        // Draw bone connections using pre-calculated screen positions
        const auto& connections = GetZombieBoneConnections(detailLevel);
        
        for (const auto& connection : connections) {
            // Get bone screen positions from batched data
            auto fromIt = screenData.boneIndexMap.find(connection.from);
            auto toIt = screenData.boneIndexMap.find(connection.to);
            
            if (fromIt == screenData.boneIndexMap.end() || toIt == screenData.boneIndexMap.end()) {
                continue; // Skip if bone not found in batch
            }

            size_t fromIndex = fromIt->second;
            size_t toIndex = toIt->second;

            // Check if both bone positions are visible
            if (!screenData.isVisible[fromIndex] || !screenData.isVisible[toIndex]) {
                continue; // Skip if either bone is not visible
            }

            const DMARender::Vector2& fromScreen = screenData.screenPositions[fromIndex];
            const DMARender::Vector2& toScreen = screenData.screenPositions[toIndex];

            // Draw the bone line directly using pre-calculated screen coordinates
            ImGui::GetWindowDrawList()->AddLine(
                ImVec2(fromScreen.x, fromScreen.y),
                ImVec2(toScreen.x, toScreen.y),
                color,
                thickness
            );
        }
    }

    // ===== SKELETON CACHE IMPLEMENTATION =====

    // ===== SKELETON CACHE IMPLEMENTATION =====

    SkeletonCacheEntry* SkeletonCache::GetCachedSkeleton(uint32_t networkId) {
        auto lock = LockCache();
        auto it = cache.find(networkId);
        if (it != cache.end() && it->second.isValid) {
            return &it->second;
        }
        return nullptr;
    }

    void SkeletonCache::UpdateSkeletonCache(uint32_t networkId, const SkeletonCacheEntry& entry) {
        auto lock = LockCache();
        
        // Check cache size limit
        if (cache.size() >= MAX_CACHE_SIZE) {
            // Remove oldest entry
            auto oldestIt = cache.begin();
            for (auto it = cache.begin(); it != cache.end(); ++it) {
                if (it->second.lastUpdate < oldestIt->second.lastUpdate) {
                    oldestIt = it;
                }
            }
            cache.erase(oldestIt);
        }
        
        cache[networkId] = entry;
    }

    void SkeletonCache::ClearExpiredCache() {
        auto lock = LockCache();
        auto now = std::chrono::steady_clock::now();
        
        for (auto it = cache.begin(); it != cache.end();) {
            auto age = now - it->second.lastUpdate;
            if (age > SkeletonCacheConstants::CACHE_DURATION) {
                it = cache.erase(it);
            } else {
                ++it;
            }
        }
    }

    void SkeletonCache::ClearAllCache() {
        auto lock = LockCache();
        cache.clear();
    }

    // ===== CACHED BONE READING IMPLEMENTATION =====
    bool SkeletonESP::GetBonePositionsCached(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity,
                                            const std::vector<uint32_t>& boneIds, SkeletonCacheEntry* cacheEntry) {
        printf("[SKELETON DEBUG] GetBonePositionsCached called for entity %u with %zu bone IDs\n", 
               entity ? entity->NetworkID : 0, boneIds.size());
        
        if (!vmm || !entity || !entity->FutureVisualStatePtr || !cacheEntry || boneIds.empty()) {
            printf("[SKELETON DEBUG] Invalid parameters - vmm: %p, entity: %p, visState: %p, cacheEntry: %p, boneIds empty: %d\n",
                   vmm, entity.get(), entity ? entity->FutureVisualStatePtr.get() : nullptr, cacheEntry, boneIds.empty());
            return false;
        }

        // Validate entity has valid remote address
        if (entity->_remoteAddress == 0) {
            printf("[SKELETON DEBUG] Invalid remote address: 0\n");
            return false;
        }

        // Initialize cache entry
        cacheEntry->entityNetworkId = entity->NetworkID;
        cacheEntry->isValid = false;
        cacheEntry->lastUpdate = std::chrono::steady_clock::now();

        // Get skeleton pointer with safety checks
        uintptr_t skeleton = 0;
        try {
            skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + PLAYER_SKELETON_OFFSET);
            printf("[SKELETON DEBUG] Player skeleton address: 0x%llX\n", skeleton);
            if (skeleton != 0 && IsValidSkeleton(vmm, pid, skeleton)) {
                cacheEntry->isPlayer = true;
                printf("[SKELETON DEBUG] Using player skeleton\n");
            } else {
                // Try zombie skeleton
                skeleton = vmm->readMemoryDirect<uintptr_t>(pid, entity->_remoteAddress + ZOMBIE_SKELETON_OFFSET);
                printf("[SKELETON DEBUG] Zombie skeleton address: 0x%llX\n", skeleton);
                if (skeleton == 0 || !IsValidSkeleton(vmm, pid, skeleton)) {
                    printf("[SKELETON DEBUG] Invalid zombie skeleton\n");
                    return false;
                }
                cacheEntry->isPlayer = false;
                printf("[SKELETON DEBUG] Using zombie skeleton\n");
            }
        } catch (...) {
            printf("[SKELETON DEBUG] Exception reading skeleton address\n");
            return false; // Handle any memory read exceptions
        }

        // Check if we can reuse cached bone matrices
        auto now = std::chrono::steady_clock::now();
        bool canReuseMatrices = false;
        
        if (cacheEntry->boneMatrices.isValid && 
            cacheEntry->boneMatrices.skeletonAddress == skeleton &&
            cacheEntry->boneMatrices.visStateAddress == entity->FutureVisualStatePtr->_remoteAddress &&
            !cacheEntry->boneMatrices.boneMatrices.empty()) {
            
            auto matrixAge = now - cacheEntry->boneMatrices.timestamp;
            if (matrixAge < SkeletonCacheConstants::MATRIX_CACHE_DURATION) {
                canReuseMatrices = true;
            }
        }

        if (!canReuseMatrices) {
            printf("[SKELETON DEBUG] Reading new bone matrices\n");
            // Need to read new bone matrices
            cacheEntry->boneMatrices.skeletonAddress = skeleton;
            cacheEntry->boneMatrices.visStateAddress = entity->FutureVisualStatePtr->_remoteAddress;
            cacheEntry->boneMatrices.timestamp = now;
            cacheEntry->boneMatrices.isValid = false;

            try {
                // Get animation class
                uintptr_t animClass = vmm->readMemoryDirect<uintptr_t>(pid, skeleton + ANIM_CLASS_OFFSET);
                printf("[SKELETON DEBUG] Animation class address: 0x%llX\n", animClass);
                if (!DayZUtil::isPointerValid(animClass)) {
                    printf("[SKELETON DEBUG] Invalid animation class\n");
                    return false;
                }

                // Get matrix class
                uintptr_t matrixClass = vmm->readMemoryDirect<uintptr_t>(pid, animClass + MATRIX_CLASS_OFFSET);
                printf("[SKELETON DEBUG] Matrix class address: 0x%llX\n", matrixClass);
                if (!DayZUtil::isPointerValid(matrixClass)) {
                    printf("[SKELETON DEBUG] Invalid matrix class\n");
                    return false;
                }

                // Read visual state matrix
                if (!vmm->readMemory(pid, entity->FutureVisualStatePtr->_remoteAddress + 0x8, 
                                    &cacheEntry->boneMatrices.visStateMatrix, sizeof(Matrix4x4))) {
                    printf("[SKELETON DEBUG] Failed to read visual state matrix\n");
                    return false;
                }
                printf("[SKELETON DEBUG] Successfully read visual state matrix\n");

                // Calculate total size needed for all bone matrices
                size_t maxBoneId = 0;
                for (uint32_t boneId : boneIds) {
                    if (boneId > maxBoneId) maxBoneId = boneId;
                }
                
                // Safety check for reasonable bone ID
                if (maxBoneId > 1000) {
                    printf("[SKELETON DEBUG] Invalid max bone ID: %zu\n", maxBoneId);
                    return false; // Invalid bone ID
                }
                
                cacheEntry->boneMatrices.maxBoneId = maxBoneId;
                size_t totalMatrixSize = (maxBoneId + 1) * sizeof(Matrix4x4);
                printf("[SKELETON DEBUG] Reading %zu bone matrices, total size: %zu bytes\n", maxBoneId + 1, totalMatrixSize);
                
                // Read ALL bone matrices in a single DMA operation
                cacheEntry->boneMatrices.boneMatrices.resize(maxBoneId + 1);
                if (!vmm->readMemory(pid, matrixClass, cacheEntry->boneMatrices.boneMatrices.data(), totalMatrixSize)) {
                    printf("[SKELETON DEBUG] Failed to read bone matrices\n");
                    return false;
                }

                cacheEntry->boneMatrices.isValid = true;
                printf("[SKELETON DEBUG] Successfully read %zu bone matrices\n", cacheEntry->boneMatrices.boneMatrices.size());
            } catch (...) {
                printf("[SKELETON DEBUG] Exception reading bone matrices\n");
                return false; // Handle any memory read exceptions
            }
        } else {
            printf("[SKELETON DEBUG] Reusing cached bone matrices\n");
        }

        // Calculate bone positions using cached matrices
        printf("[SKELETON DEBUG] Calculating bone positions for %zu bone IDs\n", boneIds.size());
        cacheEntry->bonePositions.bonePositions.clear();
        cacheEntry->bonePositions.boneIndexMap.clear();
        cacheEntry->bonePositions.isValid = false;
        cacheEntry->bonePositions.timestamp = now;

        cacheEntry->bonePositions.bonePositions.reserve(boneIds.size());
        
        try {
            for (uint32_t boneId : boneIds) {
                if (boneId >= cacheEntry->boneMatrices.boneMatrices.size()) {
                    printf("[SKELETON DEBUG] Skipping invalid bone ID: %u (max: %zu)\n", boneId, cacheEntry->boneMatrices.boneMatrices.size());
                    continue; // Skip invalid bone IDs
                }

                const Matrix4x4& boneMatrix = cacheEntry->boneMatrices.boneMatrices[boneId];
                const Matrix4x4& visStateMatrix = cacheEntry->boneMatrices.visStateMatrix;
                
                // Calculate world space position using matrix transformation
                DMARender::Vector3 bonePos;
                bonePos.z = (boneMatrix.m[10] * visStateMatrix.m[5]) + (boneMatrix.m[9] * visStateMatrix.m[2]) + (boneMatrix.m[11] * visStateMatrix.m[8]) + visStateMatrix.m[11];
                bonePos.y = (boneMatrix.m[10] * visStateMatrix.m[4]) + (boneMatrix.m[9] * visStateMatrix.m[1]) + (boneMatrix.m[11] * visStateMatrix.m[7]) + visStateMatrix.m[10];
                bonePos.x = (boneMatrix.m[10] * visStateMatrix.m[3]) + (boneMatrix.m[9] * visStateMatrix.m[0]) + (boneMatrix.m[11] * visStateMatrix.m[6]) + visStateMatrix.m[9];

                printf("[SKELETON DEBUG] Bone %u position: (%.2f, %.2f, %.2f)\n", boneId, bonePos.x, bonePos.y, bonePos.z);

                // Store bone position and create index mapping
                cacheEntry->bonePositions.boneIndexMap[boneId] = cacheEntry->bonePositions.bonePositions.size();
                cacheEntry->bonePositions.bonePositions.push_back(bonePos);
            }

            cacheEntry->bonePositions.isValid = true;
            cacheEntry->isValid = true;
            printf("[SKELETON DEBUG] Successfully calculated %zu bone positions\n", cacheEntry->bonePositions.bonePositions.size());
            return true;
        } catch (...) {
            printf("[SKELETON DEBUG] Exception during bone position calculation\n");
            return false; // Handle any calculation exceptions
        }
    }

    // ===== CACHED SKELETON FUNCTIONS =====
    void SkeletonESP::DrawPlayerSkeletonCached(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any bone processing
        // Use intelligent distance-based processing control
        if (ShouldSkipSkeletonProcessing(camera, entity, true, 1000.0f)) { // 1000m hard limit for players
            return;
        }

        // Check cache first
        SkeletonCacheEntry* cachedEntry = skeletonCache.GetCachedSkeleton(entity->NetworkID);
        bool needUpdate = true;

        if (cachedEntry && cachedEntry->isValid && cachedEntry->isPlayer) {
            auto now = std::chrono::steady_clock::now();
            auto age = now - cachedEntry->lastUpdate;
            
            if (age < SkeletonCacheConstants::CACHE_DURATION) {
                // Use cached data
                needUpdate = false;
                printf("[SKELETON DEBUG] Using cached skeleton data\n");
                
                // Check if we need to update screen positions
                auto screenAge = now - cachedEntry->screenPositions.timestamp;
                if (screenAge >= SkeletonCacheConstants::CACHE_DURATION) {
                    // Update screen positions only
                    try {
                        WorldToScreenBatched(camera, cachedEntry->bonePositions.bonePositions, &cachedEntry->screenPositions);
                        cachedEntry->screenPositions.boneIndexMap = cachedEntry->bonePositions.boneIndexMap;
                        cachedEntry->screenPositions.timestamp = now;
                        printf("[SKELETON DEBUG] Updated screen positions for cached data\n");
                    } catch (...) {
                        needUpdate = true; // Force update if screen calculation fails
                        printf("[SKELETON DEBUG] Exception updating screen positions, forcing update\n");
                    }
                }
            }
        }

        if (needUpdate) {
            printf("[SKELETON DEBUG] Creating new cache entry\n");
            // Create new cache entry
            SkeletonCacheEntry newEntry;
            std::vector<uint32_t> boneIds = GetPlayerBoneIds();
            
            try {
                if (GetBonePositionsCached(vmm, pid, entity, boneIds, &newEntry)) {
                    printf("[SKELETON DEBUG] Got bone positions, calculating screen positions\n");
                    // Calculate screen positions
                    WorldToScreenBatched(camera, newEntry.bonePositions.bonePositions, &newEntry.screenPositions);
                    newEntry.screenPositions.boneIndexMap = newEntry.bonePositions.boneIndexMap;
                    newEntry.screenPositions.timestamp = std::chrono::steady_clock::now();
                    
                    // Update cache
                    skeletonCache.UpdateSkeletonCache(entity->NetworkID, newEntry);
                    cachedEntry = &newEntry;
                    printf("[SKELETON DEBUG] Successfully updated cache with %zu bone positions\n", newEntry.bonePositions.bonePositions.size());
                } else {
                    printf("[SKELETON DEBUG] Failed to get bone positions\n");
                    return; // Failed to get bone positions
                }
            } catch (...) {
                printf("[SKELETON DEBUG] Exception during cache update\n");
                return; // Handle any exceptions during cache update
            }
        }

        // Draw bone connections using cached data
        if (!cachedEntry || !cachedEntry->isValid) {
            printf("[SKELETON DEBUG] Cached entry is invalid or null\n");
            return; // Safety check
        }

        const auto& connections = PLAYER_BONE_CONNECTIONS;
        int linesDrawn = 0;
        
        try {
            for (const auto& connection : connections) {
                // Get bone screen positions from cached data
                auto fromIt = cachedEntry->screenPositions.boneIndexMap.find(connection.from);
                auto toIt = cachedEntry->screenPositions.boneIndexMap.find(connection.to);
                
                if (fromIt == cachedEntry->screenPositions.boneIndexMap.end() || 
                    toIt == cachedEntry->screenPositions.boneIndexMap.end()) {
                    continue; // Skip if bone not found in cache
                }

                size_t fromIndex = fromIt->second;
                size_t toIndex = toIt->second;

                // Check if both bone positions are visible
                if (fromIndex >= cachedEntry->screenPositions.isVisible.size() || 
                    toIndex >= cachedEntry->screenPositions.isVisible.size() ||
                    !cachedEntry->screenPositions.isVisible[fromIndex] || 
                    !cachedEntry->screenPositions.isVisible[toIndex]) {
                    continue; // Skip if either bone is not visible
                }

                const DMARender::Vector2& fromScreen = cachedEntry->screenPositions.screenPositions[fromIndex];
                const DMARender::Vector2& toScreen = cachedEntry->screenPositions.screenPositions[toIndex];

                // Draw the bone line directly using cached screen coordinates
                ImGui::GetWindowDrawList()->AddLine(
                    ImVec2(fromScreen.x, fromScreen.y),
                    ImVec2(toScreen.x, toScreen.y),
                    color,
                    thickness
                );
                linesDrawn++;
            }

        } catch (...) {

            // Handle any drawing exceptions silently
        }
    }

    void SkeletonESP::DrawZombieSkeletonCached(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color, float thickness) {
        if (!vmm || !camera || !entity) {
            return;
        }

        // CRITICAL PERFORMANCE FIX: Early distance check before any bone processing
        // Use intelligent distance-based processing control
        if (ShouldSkipSkeletonProcessing(camera, entity, false, 500.0f)) { // 500m hard limit for zombies
            return;
        }

        // Check cache first
        SkeletonCacheEntry* cachedEntry = skeletonCache.GetCachedSkeleton(entity->NetworkID);
        bool needUpdate = true;

        if (cachedEntry && cachedEntry->isValid && !cachedEntry->isPlayer) {
            auto now = std::chrono::steady_clock::now();
            auto age = now - cachedEntry->lastUpdate;
            
            if (age < SkeletonCacheConstants::CACHE_DURATION) {
                // Use cached data
                needUpdate = false;
                printf("[SKELETON DEBUG] Using cached data, age: %lld ms\n", 
                       std::chrono::duration_cast<std::chrono::milliseconds>(age).count());
                
                // Check if we need to update screen positions
                auto screenAge = now - cachedEntry->screenPositions.timestamp;
                if (screenAge >= SkeletonCacheConstants::CACHE_DURATION) {
                    // Update screen positions only
                    printf("[SKELETON DEBUG] Updating screen positions\n");
                    WorldToScreenBatched(camera, cachedEntry->bonePositions.bonePositions, &cachedEntry->screenPositions);
                    cachedEntry->screenPositions.boneIndexMap = cachedEntry->bonePositions.boneIndexMap;
                    cachedEntry->screenPositions.timestamp = now;
                }
            }
        }

        if (needUpdate) {
            printf("[SKELETON DEBUG] Creating new cache entry\n");
            // Create new cache entry
            SkeletonCacheEntry newEntry;
            std::vector<uint32_t> boneIds = GetZombieBoneIds();
            printf("[SKELETON DEBUG] Getting %zu bone positions\n", boneIds.size());
            
            if (GetBonePositionsCached(vmm, pid, entity, boneIds, &newEntry)) {
                // Calculate screen positions
                WorldToScreenBatched(camera, newEntry.bonePositions.bonePositions, &newEntry.screenPositions);
                newEntry.screenPositions.boneIndexMap = newEntry.bonePositions.boneIndexMap;
                newEntry.screenPositions.timestamp = std::chrono::steady_clock::now();

                // Update cache
                skeletonCache.UpdateSkeletonCache(entity->NetworkID, newEntry);
                cachedEntry = &newEntry;
            } else {
                return; // Failed to get bone positions
            }
        }

        // Draw bone connections using cached data
        if (!cachedEntry || !cachedEntry->isValid) {
            return; // Safety check
        }

        const auto& connections = ZOMBIE_BONE_CONNECTIONS;
        int linesDrawn = 0;
        
        for (const auto& connection : connections) {
            // Get bone screen positions from cached data
            auto fromIt = cachedEntry->screenPositions.boneIndexMap.find(connection.from);
            auto toIt = cachedEntry->screenPositions.boneIndexMap.find(connection.to);
            
            if (fromIt == cachedEntry->screenPositions.boneIndexMap.end() || 
                toIt == cachedEntry->screenPositions.boneIndexMap.end()) {
                printf("[SKELETON DEBUG] Bone not found in cache: from=%u, to=%u\n", connection.from, connection.to);
                continue; // Skip if bone not found in cache
            }

            size_t fromIndex = fromIt->second;
            size_t toIndex = toIt->second;

            // Check if both bone positions are visible
            if (fromIndex >= cachedEntry->screenPositions.isVisible.size() ||
                toIndex >= cachedEntry->screenPositions.isVisible.size() ||
                !cachedEntry->screenPositions.isVisible[fromIndex] ||
                !cachedEntry->screenPositions.isVisible[toIndex]) {
                continue; // Skip if either bone is not visible
            }

            const DMARender::Vector2& fromScreen = cachedEntry->screenPositions.screenPositions[fromIndex];
            const DMARender::Vector2& toScreen = cachedEntry->screenPositions.screenPositions[toIndex];

            // Draw the bone line directly using cached screen coordinates
            ImGui::GetWindowDrawList()->AddLine(
                ImVec2(fromScreen.x, fromScreen.y),
                ImVec2(toScreen.x, toScreen.y),
                color,
                thickness
            );
            linesDrawn++;
        }
        printf("[SKELETON DEBUG] Drew %d bone lines\n", linesDrawn);
    }

    // ===== CACHE MANAGEMENT FUNCTIONS =====
    void SkeletonESP::ClearSkeletonCache() {
        skeletonCache.ClearAllCache();
    }

    void SkeletonESP::UpdateSkeletonCache() {
        skeletonCache.ClearExpiredCache();
    }

    // ===== MULTITHREADED SKELETON PROCESSING IMPLEMENTATION =====

    // ===== SKELETON WORKER SYSTEM IMPLEMENTATION =====
    SkeletonWorkerSystem::SkeletonWorkerSystem(size_t numWorkers) 
        : shouldStop(false), activeWorkers(0), workerCount(numWorkers), 
          currentVmm(nullptr), currentPid(0), currentCamera(nullptr) {
        printf("[SKELETON WORKER] Initialized with %zu workers\n", workerCount);
    }

    SkeletonWorkerSystem::~SkeletonWorkerSystem() {
        StopWorkers();
    }

    void SkeletonWorkerSystem::StartWorkers(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera) {
        if (!workers.empty()) {
            printf("[SKELETON WORKER] Workers already running\n");
            return;
        }

        // Store current parameters
        currentVmm = vmm;
        currentPid = pid;
        currentCamera = camera;

        shouldStop = false;
        activeWorkers = 0;
        
        printf("[SKELETON WORKER] Starting %zu worker threads\n", workerCount);
        
        for (size_t i = 0; i < workerCount; ++i) {
            workers.emplace_back(&SkeletonWorkerSystem::WorkerThread, this);
        }
        
        printf("[SKELETON WORKER] All workers started\n");
    }

    void SkeletonWorkerSystem::StopWorkers() {
        if (workers.empty()) {
            return;
        }

        printf("[SKELETON WORKER] Stopping workers\n");
        shouldStop = true;
        queueCondition.notify_all();

        for (auto& worker : workers) {
            if (worker.joinable()) {
                worker.join();
            }
        }

        workers.clear();
        printf("[SKELETON WORKER] All workers stopped\n");
    }

    void SkeletonWorkerSystem::WaitForCompletion() {
        while (activeWorkers > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    void SkeletonWorkerSystem::AddWorkItem(const SkeletonWorkItem& item) {
        std::lock_guard<std::mutex> lock(queueMutex);
        
        if (workQueue.size() < MAX_QUEUE_SIZE) {
            workQueue.push(item);
            queueCondition.notify_one();
        } else {
            printf("[SKELETON WORKER] Work queue full, dropping item\n");
        }
    }

    void SkeletonWorkerSystem::ProcessWorkItems(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera) {
        if (workers.empty()) {
            printf("[SKELETON WORKER] No workers available, processing sequentially\n");
            // Fallback to sequential processing
            std::lock_guard<std::mutex> lock(queueMutex);
            while (!workQueue.empty()) {
                SkeletonWorkItem item = workQueue.front();
                workQueue.pop();
                
                SkeletonWorkResult result = ProcessWorkItem(item, vmm, pid, camera);
                
                std::lock_guard<std::mutex> resultLock(resultsMutex);
                results.push_back(result);
            }
            return;
        }

        // Start workers if not already running
        if (shouldStop) {
            StartWorkers(vmm, pid, camera);
        }

        // Wait for all work to complete
        WaitForCompletion();
    }

    std::vector<SkeletonWorkResult> SkeletonWorkerSystem::GetResults() {
        std::lock_guard<std::mutex> lock(resultsMutex);
        return results;
    }

    void SkeletonWorkerSystem::ClearResults() {
        std::lock_guard<std::mutex> lock(resultsMutex);
        results.clear();
    }

    size_t SkeletonWorkerSystem::GetQueueSize() const {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queueMutex));
        return workQueue.size();
    }

    void SkeletonWorkerSystem::WorkerThread() {
        activeWorkers++;
        printf("[SKELETON WORKER] Worker thread started\n");

        while (!shouldStop) {
            SkeletonWorkItem item;
            bool hasWork = false;

            {
                std::unique_lock<std::mutex> lock(queueMutex);
                queueCondition.wait(lock, [this] { return shouldStop || !workQueue.empty(); });
                
                if (shouldStop) {
                    break;
                }

                if (!workQueue.empty()) {
                    item = workQueue.front();
                    workQueue.pop();
                    hasWork = true;
                }
            }

            if (hasWork) {
                SkeletonWorkResult result = ProcessWorkItem(item, currentVmm, currentPid, currentCamera);
                
                std::lock_guard<std::mutex> resultLock(resultsMutex);
                results.push_back(result);
            }
        }

        activeWorkers--;
        printf("[SKELETON WORKER] Worker thread stopped\n");
    }

    SkeletonWorkResult SkeletonWorkerSystem::ProcessWorkItem(const SkeletonWorkItem& item, DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera) {
        SkeletonWorkResult result;
        result.networkId = item.networkId;
        result.isPlayer = item.isPlayer;
        result.isZombie = item.isZombie;
        result.color = item.color;
        result.thickness = item.thickness;
        result.isValid = false;
        result.errorMessage = "";

        try {
            if (!item.shouldProcess) {
                result.errorMessage = "Processing skipped";
                return result;
            }

            // Get bone connections based on entity type and detail level
            if (item.isPlayer) {
                result.connections = SkeletonESP::GetPlayerBoneConnections(item.detailLevel);
            } else if (item.isZombie) {
                result.connections = SkeletonESP::GetZombieBoneConnections(item.detailLevel);
            } else {
                result.errorMessage = "Unknown entity type";
                return result;
            }

            // Get batched screen data
            if (item.isPlayer) {
                result.screenData = SkeletonESP::GetPlayerSkeletonScreenBatched(vmm, pid, camera, item.entity);
            } else {
                result.screenData = SkeletonESP::GetZombieSkeletonScreenBatched(vmm, pid, camera, item.entity);
            }

            result.isValid = result.screenData.isValid && !result.screenData.screenPositions.empty();
            
            if (result.isValid) {
                printf("[SKELETON WORKER] Processed entity %u: %zu bone positions\n", 
                       item.networkId, result.screenData.screenPositions.size());
            } else {
                result.errorMessage = "Failed to get bone positions";
            }

        } catch (const std::exception& e) {
            result.errorMessage = std::string("Exception: ") + e.what();
            printf("[SKELETON WORKER] Exception processing entity %u: %s\n", item.networkId, e.what());
        } catch (...) {
            result.errorMessage = "Unknown exception";
            printf("[SKELETON WORKER] Unknown exception processing entity %u\n", item.networkId);
        }

        return result;
    }

    // ===== SKELETON ESP MULTITHREADED FUNCTIONS =====
    
    void SkeletonESP::InitializeWorkerSystem(size_t workerCount) {
        if (workerSystem) {
            printf("[SKELETON ESP] Worker system already initialized\n");
            return;
        }

        printf("[SKELETON ESP] Initializing worker system with %zu workers\n", workerCount);
        workerSystem = std::make_unique<SkeletonWorkerSystem>(workerCount);
        // Workers will be started when first used with proper parameters
    }

    void SkeletonESP::ShutdownWorkerSystem() {
        if (workerSystem) {
            printf("[SKELETON ESP] Shutting down worker system\n");
            workerSystem->StopWorkers();
            workerSystem.reset();
        }
    }

    SkeletonWorkerSystem* SkeletonESP::GetWorkerSystem() {
        return workerSystem.get();
    }

    void SkeletonESP::ProcessSkeletonsMultithreaded(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera,
                                                   const std::vector<std::shared_ptr<DayZ::Entity>>& entities,
                                                   bool showPlayerSkeleton, bool showZombieSkeleton,
                                                   float playerMaxDistance, float zombieMaxDistance,
                                                   ImU32 playerColor, ImU32 zombieColor, float thickness, int detailLevel) {
        
        if (!workerSystem) {
            printf("[SKELETON ESP] Worker system not initialized, initializing with default settings\n");
            InitializeWorkerSystem();
        }

        // Performance monitoring
        auto startTime = std::chrono::high_resolution_clock::now();

        // Clear previous results
        workerSystem->ClearResults();

        // Prepare work items
        std::vector<SkeletonWorkItem> workItems;
        workItems.reserve(entities.size());

        for (const auto& entity : entities) {
            if (!entity || !entity->FutureVisualStatePtr) {
                continue;
            }

            // Determine entity type
            bool isPlayer = entity->isPlayer();
            bool isZombie = entity->isZombie();
            
            if (!isPlayer && !isZombie) {
                continue; // Skip non-player/non-zombie entities
            }

            // Check if skeleton rendering is enabled for this entity type
            if ((isPlayer && !showPlayerSkeleton) || (isZombie && !showZombieSkeleton)) {
                continue;
            }

            // Calculate distance and check limits
            float distance = camera->InvertedViewTranslation.Dist(entity->FutureVisualStatePtr->position);
            float maxDistance = isPlayer ? playerMaxDistance : zombieMaxDistance;
            
            if (distance > maxDistance) {
                continue; // Skip entities beyond max distance
            }

            // Create work item
            SkeletonWorkItem item;
            item.entity = entity;
            item.isPlayer = isPlayer;
            item.isZombie = isZombie;
            item.networkId = entity->NetworkID;
            item.distance = distance;
            item.color = isPlayer ? playerColor : zombieColor;
            item.thickness = thickness;
            item.detailLevel = detailLevel;
            item.shouldProcess = true;

            workItems.push_back(item);
        }

        printf("[SKELETON ESP] Prepared %zu work items for multithreaded processing\n", workItems.size());

        // Add work items to queue
        for (const auto& item : workItems) {
            workerSystem->AddWorkItem(item);
        }

        // Process all work items
        workerSystem->ProcessWorkItems(vmm, pid, camera);

        // Performance monitoring
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        
        printf("[SKELETON ESP] Multithreaded processing completed in %lld microseconds (%zu entities)\n", 
               duration.count(), workItems.size());
    }

    void SkeletonESP::RenderSkeletonResults(const std::vector<SkeletonWorkResult>& results) {
        if (!workerSystem) {
            printf("[SKELETON ESP] Worker system not available for rendering\n");
            return;
        }

        auto drawList = ImGui::GetWindowDrawList();
        if (!drawList) {
            printf("[SKELETON ESP] No draw list available\n");
            return;
        }

        int totalLinesDrawn = 0;

        for (const auto& result : results) {
            if (!result.isValid || result.screenData.screenPositions.empty()) {
                continue;
            }

            int linesDrawn = 0;

            // Draw bone connections using pre-calculated screen positions
            for (const auto& connection : result.connections) {
                // Get bone positions from batched data
                std::unordered_map<uint32_t, size_t>::const_iterator fromIt = result.screenData.boneIndexMap.find(connection.from);
                std::unordered_map<uint32_t, size_t>::const_iterator toIt = result.screenData.boneIndexMap.find(connection.to);
                
                if (fromIt == result.screenData.boneIndexMap.end() || toIt == result.screenData.boneIndexMap.end()) {
                    continue; // Skip if bone not found in batch
                }

                size_t fromIndex = fromIt->second;
                size_t toIndex = toIt->second;

                // Check if both bone positions are visible
                if (fromIndex >= result.screenData.isVisible.size() || 
                    toIndex >= result.screenData.isVisible.size() ||
                    !result.screenData.isVisible[fromIndex] || 
                    !result.screenData.isVisible[toIndex]) {
                    continue; // Skip if either bone is not visible
                }

                const DMARender::Vector2& fromScreen = result.screenData.screenPositions[fromIndex];
                const DMARender::Vector2& toScreen = result.screenData.screenPositions[toIndex];

                // Draw the bone line
                drawList->AddLine(
                    ImVec2(fromScreen.x, fromScreen.y),
                    ImVec2(toScreen.x, toScreen.y),
                    result.color,
                    result.thickness
                );
                linesDrawn++;
            }

            totalLinesDrawn += linesDrawn;
        }

        printf("[SKELETON ESP] Rendered %d total bone lines from %zu results\n", totalLinesDrawn, results.size());
    }

    // ===== DAYZ64 CHEATSBUDDY STYLE SKELETON =====
    std::vector<DayZ::BoneConnection> SkeletonESP::GetCheatsBuddyPlayerBoneConnections() {
        return {
            // Main spine (head to pelvis) - simple like CheatsBuddy
            {(uint32_t)PlayerBoneId::head, (uint32_t)PlayerBoneId::pelvis},
            
            // Left leg chain (pelvis to foot)
            {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::leftupleg},
            {(uint32_t)PlayerBoneId::leftupleg, (uint32_t)PlayerBoneId::leftleg},
            {(uint32_t)PlayerBoneId::leftleg, (uint32_t)PlayerBoneId::leftfoot},
            
            // Right leg chain (pelvis to foot)
            {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::rightupleg},
            {(uint32_t)PlayerBoneId::rightupleg, (uint32_t)PlayerBoneId::rightleg},
            {(uint32_t)PlayerBoneId::rightleg, (uint32_t)PlayerBoneId::rightfoot},
            
            // Left arm chain (neck to hand)
            {(uint32_t)PlayerBoneId::neck, (uint32_t)PlayerBoneId::leftarm},
            {(uint32_t)PlayerBoneId::leftarm, (uint32_t)PlayerBoneId::leftforearm},
            {(uint32_t)PlayerBoneId::leftforearm, (uint32_t)PlayerBoneId::lefthand},
            
            // Right arm chain (neck to hand)
            {(uint32_t)PlayerBoneId::neck, (uint32_t)PlayerBoneId::rightarm},
            {(uint32_t)PlayerBoneId::rightarm, (uint32_t)PlayerBoneId::rightforearm},
            {(uint32_t)PlayerBoneId::rightforearm, (uint32_t)PlayerBoneId::righthand}
        };
    }

    std::vector<DayZ::BoneConnection> SkeletonESP::GetCheatsBuddyZombieBoneConnections() {
        return {
            // Main spine (head to pelvis) - simple like CheatsBuddy
            {(uint32_t)InfectedBoneId::head, (uint32_t)InfectedBoneId::pelvis},
            
            // Left leg chain (pelvis to foot)
            {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::leftupleg},
            {(uint32_t)InfectedBoneId::leftupleg, (uint32_t)InfectedBoneId::leftleg},
            {(uint32_t)InfectedBoneId::leftleg, (uint32_t)InfectedBoneId::leftfoot},
            
            // Right leg chain (pelvis to foot)
            {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::rightupleg},
            {(uint32_t)InfectedBoneId::rightupleg, (uint32_t)InfectedBoneId::rightleg},
            {(uint32_t)InfectedBoneId::rightleg, (uint32_t)InfectedBoneId::rightfoot},
            
            // Left arm chain (neck to hand)
            {(uint32_t)InfectedBoneId::neck, (uint32_t)InfectedBoneId::leftarm},
            {(uint32_t)InfectedBoneId::leftarm, (uint32_t)InfectedBoneId::leftforearm},
            {(uint32_t)InfectedBoneId::leftforearm, (uint32_t)InfectedBoneId::lefthand},
            
            // Right arm chain (neck to hand)
            {(uint32_t)InfectedBoneId::neck, (uint32_t)InfectedBoneId::rightarm},
            {(uint32_t)InfectedBoneId::rightarm, (uint32_t)InfectedBoneId::rightforearm},
            {(uint32_t)InfectedBoneId::rightforearm, (uint32_t)InfectedBoneId::righthand}
        };
    }

    // ===== STATIC MEMBER DEFINITIONS =====
    
    // Static member definitions
    SkeletonCache SkeletonESP::skeletonCache;
    std::unique_ptr<SkeletonWorkerSystem> SkeletonESP::workerSystem;

    // Bone connection definitions (DayZ2-style minimal)
    const std::vector<DayZ::BoneConnection> SkeletonESP::PLAYER_BONE_CONNECTIONS = {
        // Main spine chain (pelvis to head)
        {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::spine},
        {(uint32_t)PlayerBoneId::spine, (uint32_t)PlayerBoneId::spine1},
        {(uint32_t)PlayerBoneId::spine1, (uint32_t)PlayerBoneId::spine2},
        {(uint32_t)PlayerBoneId::spine2, (uint32_t)PlayerBoneId::spine3},
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::neck},
        {(uint32_t)PlayerBoneId::neck, (uint32_t)PlayerBoneId::head},

        // Left arm chain
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::leftshoulder},
        {(uint32_t)PlayerBoneId::leftshoulder, (uint32_t)PlayerBoneId::leftarm},
        {(uint32_t)PlayerBoneId::leftarm, (uint32_t)PlayerBoneId::leftforearm},
        {(uint32_t)PlayerBoneId::leftforearm, (uint32_t)PlayerBoneId::lefthand},

        // Right arm chain
        {(uint32_t)PlayerBoneId::spine3, (uint32_t)PlayerBoneId::rightshoulder},
        {(uint32_t)PlayerBoneId::rightshoulder, (uint32_t)PlayerBoneId::rightarm},
        {(uint32_t)PlayerBoneId::rightarm, (uint32_t)PlayerBoneId::rightforearm},
        {(uint32_t)PlayerBoneId::rightforearm, (uint32_t)PlayerBoneId::righthand},

        // Left leg chain
        {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::leftupleg},
        {(uint32_t)PlayerBoneId::leftupleg, (uint32_t)PlayerBoneId::leftleg},
        {(uint32_t)PlayerBoneId::leftleg, (uint32_t)PlayerBoneId::leftfoot},

        // Right leg chain
        {(uint32_t)PlayerBoneId::pelvis, (uint32_t)PlayerBoneId::rightupleg},
        {(uint32_t)PlayerBoneId::rightupleg, (uint32_t)PlayerBoneId::rightleg},
        {(uint32_t)PlayerBoneId::rightleg, (uint32_t)PlayerBoneId::rightfoot}
    };

    const std::vector<DayZ::BoneConnection> SkeletonESP::ZOMBIE_BONE_CONNECTIONS = {
        // Main spine chain (pelvis to head)
        {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::spine},
        {(uint32_t)InfectedBoneId::spine, (uint32_t)InfectedBoneId::spine1},
        {(uint32_t)InfectedBoneId::spine1, (uint32_t)InfectedBoneId::spine2},
        {(uint32_t)InfectedBoneId::spine2, (uint32_t)InfectedBoneId::spine3},
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::neck},
        {(uint32_t)InfectedBoneId::neck, (uint32_t)InfectedBoneId::head},

        // Left arm chain
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::leftshoulder},
        {(uint32_t)InfectedBoneId::leftshoulder, (uint32_t)InfectedBoneId::leftarm},
        {(uint32_t)InfectedBoneId::leftarm, (uint32_t)InfectedBoneId::leftforearm},
        {(uint32_t)InfectedBoneId::leftforearm, (uint32_t)InfectedBoneId::lefthand},

        // Right arm chain
        {(uint32_t)InfectedBoneId::spine3, (uint32_t)InfectedBoneId::rightshoulder},
        {(uint32_t)InfectedBoneId::rightshoulder, (uint32_t)InfectedBoneId::rightarm},
        {(uint32_t)InfectedBoneId::rightarm, (uint32_t)InfectedBoneId::rightforearm},
        {(uint32_t)InfectedBoneId::rightforearm, (uint32_t)InfectedBoneId::righthand},

        // Left leg chain
        {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::leftupleg},
        {(uint32_t)InfectedBoneId::leftupleg, (uint32_t)InfectedBoneId::leftleg},
        {(uint32_t)InfectedBoneId::leftleg, (uint32_t)InfectedBoneId::leftfoot},

        // Right leg chain
        {(uint32_t)InfectedBoneId::pelvis, (uint32_t)InfectedBoneId::rightupleg},
        {(uint32_t)InfectedBoneId::rightupleg, (uint32_t)InfectedBoneId::rightleg},
        {(uint32_t)InfectedBoneId::rightleg, (uint32_t)InfectedBoneId::rightfoot}
    };

} // namespace DayZ
