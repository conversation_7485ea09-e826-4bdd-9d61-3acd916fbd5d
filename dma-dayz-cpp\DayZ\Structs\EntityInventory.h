#pragma once
#include "DMAMemoryManagement/includes.h"
#include "InventoryItem.h"
#include "CargoGrid.h"

namespace DayZ {
	struct EntityInventory : public DMAMem::MemoryObject {
		std::shared_ptr<DayZ::InventoryItem> handItem;
		std::shared_ptr<DayZ::CargoGrid> cargoGrid;
		bool isHandItemValid;

		EntityInventory() {
			handItem = std::shared_ptr<DayZ::InventoryItem>(new DayZ::InventoryItem());
			cargoGrid = std::shared_ptr<DayZ::CargoGrid>(new DayZ::CargoGrid());

			// Hand item at 0x1B0 (working offset)
			this->registerPointer(0x1B0, handItem.get());
			this->registerOffset(0x1CC, &isHandItemValid, sizeof(bool));

			// Cargo grid at 0x148 (from forum info: ItemInventory::CargoGrid -> 0x148)
			this->registerPointer(0x148, cargoGrid.get());
		}
	};
}