#pragma once
#include "DMAMemoryManagement/includes.h"
#include "ClientPlayer.h"
#include "ClientSoldierEntity.h"
#include <vector>
#include <memory>
#include <cmath>

namespace BF6 {
    class PlayerManager : public DMAMem::MemoryObject {
    public:
        // Player array data (wired to BF2042 offsets - Forum confirmed 2025)
        QWORD ClientPlayerArrayPtr{};  // +0x570 ✅ BF2042 Forum confirmed
        QWORD LocalPlayerPtr{};        // +0x560 ✅ BF2042 Forum confirmed
        uint32_t PlayerCount{};        // +0x578 (may be unreliable due to CR3 shuffling)

        // Resolved player objects
        std::vector<std::shared_ptr<ClientPlayer>> resolvedPlayers;
        std::shared_ptr<ClientPlayer> localPlayer;

        PlayerManager() {
            this->registerOffset(0x570, &ClientPlayerArrayPtr, sizeof(QWORD));  // BF2042 Forum: 0x570
            this->registerOffset(0x560, &LocalPlayerPtr, sizeof(QWORD));        // BF2042 Forum: 0x560
            this->registerOffset(0x578, &PlayerCount, sizeof(uint32_t));
        }

        bool isValid() const { return ClientPlayerArrayPtr != 0; }

        std::vector<std::shared_ptr<ClientPlayer>> getAllPlayers() const { return resolvedPlayers; }
        std::shared_ptr<ClientPlayer> getLocalPlayer() const { return localPlayer; }
        uint32_t getPlayerCount() const { return static_cast<uint32_t>(resolvedPlayers.size()); }

        bool resolvePlayerArray(DMAMem::VmmManager* vmm, DWORD pid) {
            if (!isValid()) { resolvedPlayers.clear(); return false; }
            resolvedPlayers.clear();
            try {
                uint32_t safePlayerCount = PlayerCount;
                if (safePlayerCount == 0 || safePlayerCount > 64) safePlayerCount = 64;

                std::vector<QWORD> playerPointers(safePlayerCount, 0);
                if (!vmm->readMemory(pid, ClientPlayerArrayPtr, playerPointers.data(),
                                     safePlayerCount * sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
                    return false;
                }

                for (uint32_t i = 0; i < safePlayerCount; ++i) {
                    if (playerPointers[i] == 0) continue;
                    auto player = std::make_shared<ClientPlayer>();
                    player->resolveObject(vmm, pid, playerPointers[i]);
                    if (player->isValid()) resolvedPlayers.push_back(player);
                }

                if (LocalPlayerPtr != 0) {
                    QWORD localPlayerAddress = 0;
                    if (vmm->readMemory(pid, LocalPlayerPtr, &localPlayerAddress, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
                        if (localPlayerAddress != 0) {
                            localPlayer = std::make_shared<ClientPlayer>();
                            localPlayer->resolveObject(vmm, pid, localPlayerAddress);
                        }
                    }
                }
                return true;
            } catch (...) {
                resolvedPlayers.clear();
                localPlayer = nullptr;
                return false;
            }
        }

        std::vector<std::shared_ptr<ClientPlayer>> getPlayersInRange(const DMARender::Vector3& center, float maxDistance) const {
            std::vector<std::shared_ptr<ClientPlayer>> nearbyPlayers;
            for (const auto& player : resolvedPlayers) {
                if (player && player->SoldierEntity) {
                    float dx = player->SoldierEntity->Position.x - center.x;
                    float dy = player->SoldierEntity->Position.y - center.y;
                    float dz = player->SoldierEntity->Position.z - center.z;
                    float distance = sqrt(dx*dx + dy*dy + dz*dz);
                    if (distance <= maxDistance) nearbyPlayers.push_back(player);
                }
            }
            return nearbyPlayers;
        }

        std::vector<std::shared_ptr<ClientPlayer>> getEnemyPlayers(uint32_t localTeamId) const {
            std::vector<std::shared_ptr<ClientPlayer>> enemies;
            for (const auto& player : resolvedPlayers) {
                if (player && player->isEnemy(localTeamId)) enemies.push_back(player);
            }
            return enemies;
        }
    };
}



