#pragma once
#include "DMARender/includes.h"
#include "Entity.h"
#include <memory>
#include <string>

namespace DayZ {

    class PersonalHealthDisplay {
    public:
        // Main personal health display function (original grouped mode)
        static void DrawPersonalHealth(std::shared_ptr<DayZ::Entity> localPlayer,
                                     int position = 0, float scale = 1.0f, bool showBackground = true);

        // Individual positioning mode
        static void DrawPersonalHealthIndividual(std::shared_ptr<DayZ::Entity> localPlayer,
                                                float scale, bool showBackground,
                                                float healthX, float healthY,
                                                float bloodX, float bloodY,
                                                float shockX, float shockY,
                                                float statusX, float statusY,
                                                bool showHealth, bool showBlood, bool showShock, bool showStatus,
                                                bool showLabels, bool useCustomFont);

        // Individual indicator drawing functions
        static void DrawHealthIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                      float scale, bool showBackground, bool showLabels, bool useCustomFont);
        static void DrawBloodIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                     float scale, bool showBackground, bool showLabels, bool useCustomFont);
        static void DrawShockIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                     float scale, bool showBackground, bool showLabels, bool useCustomFont);
        static void DrawStatusIndicator(std::shared_ptr<DayZ::Entity> localPlayer, float x, float y,
                                      float scale, bool showBackground, bool showLabels, bool useCustomFont);

        // Utility functions
        static void DrawHealthText(const std::string& text, ImVec2 position, float scale,
                                 bool showBackground, bool useCustomFont, ImU32 textColor = IM_COL32(255, 255, 255, 255));
        static ImVec2 GetPositionOnScreen(int position, const ImVec2& textSize);
        static ImVec2 GetPositionFromPercentage(float xPercent, float yPercent, const ImVec2& textSize);
        static std::string FormatPersonalHealthText(std::shared_ptr<DayZ::Entity> localPlayer);
        static std::string GetHealthStatus(std::shared_ptr<DayZ::Entity> localPlayer);
        static ImU32 GetHealthTextColor(float healthPercent);
        static std::string FormatHealthValue(std::shared_ptr<DayZ::Entity> localPlayer, const std::string& type, bool showLabels);

    private:
        // Position constants
        static constexpr float MARGIN = 20.0f;  // Margin from screen edges
    };

} // namespace DayZ
