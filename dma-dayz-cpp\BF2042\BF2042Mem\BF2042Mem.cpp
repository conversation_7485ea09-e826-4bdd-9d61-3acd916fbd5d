#include "BF2042Mem.h"
#include <iostream>
#include <sstream>
#include <vector>

#define EXE_NAME "BF2042.exe"

// External debug logging function
extern void DebugLog(const std::string& message);

DMAMem::VmmManager* BF2042::Mem::getVMM()
{
	return vmmManager;
}

BF2042::Mem::Mem(DMAMem::VmmManager* vmmManager)
{
    DebugLog("[BF2042] Initializing memory manager");
	this->staticManager = DMAMem::StaticManager(vmmManager);
	this->vmmManager = vmmManager;

	// Initialize BF2042 signature scanning integration
	DWORD integrationPid = staticManager.getPid(EXE_NAME);
	this->bf2042Integration = new DMAMem::BF2042Integration(vmmManager, integrationPid);
	if (this->bf2042Integration->Initialize()) {
		DebugLog("[BF2042] BF2042Integration initialized successfully with signature scanning");
	} else {
		DebugLog("[BF2042] BF2042Integration initialization failed, falling back to hardcoded addresses");
	}

    // Prime cached PML4 if patched vmmdll is present
    DWORD pid = getPid();
    if (pid) {
        // Try MemProcFS-provided DTB first
        QWORD paDTB = 0, paDTB_User = 0;
        bool primed = false;
        if (this->vmmManager->getProcessDTB(pid, &paDTB, &paDTB_User) && paDTB) {
            primed = this->vmmManager->primeCachedPML4ForPidWithDTB(pid, paDTB);
        }
        if (!primed) {
            primed = this->vmmManager->primeCachedPML4ForPid(pid);
        }
        DebugLog(primed ? "[BF2042] Cached PML4 primed" : "[BF2042] Cached PML4 not primed (continuing without)");
    }

	fetchBaseAddresses();
}

BF2042::Mem::~Mem() {
	if (bf2042Integration) {
		delete bf2042Integration;
		bf2042Integration = nullptr;
	}
}



static inline bool IsCanonicalUserPointer(QWORD p) {
    return p >= 0x10000ULL && p <= 0x7FFFFFFFFFFFULL;
}

void BF2042::Mem::fetchBaseAddresses() {
	auto pid = getPid();
	if (pid == 0) {
        DebugLog(std::string("[BF2042] ERROR: Process not found: ") + EXE_NAME);
		return;
	}

    DebugLog("[BF2042] PID OK. Attempting signature scanning for GameContext...");

    // Try signature scanning first
    QWORD gameContext = 0;
    if (bf2042Integration) {
        DebugLog("[BF2042] BF2042Integration is available, calling GetGameContext()...");
        gameContext = bf2042Integration->GetGameContext();
        DebugLog("[BF2042] GetGameContext() returned: 0x" + std::to_string(gameContext));

        if (gameContext != 0) {
            DebugLog("[BF2042] ✅ GameContext found via signature scanning!");
            this->worldAddress = gameContext;
            this->networkManagerAddress = 0x62CF960; // GameRenderer address

            // Print diagnostic info
            bf2042Integration->PrintDiagnostics();
            return;
        } else {
            DebugLog("[BF2042] GetGameContext() returned 0, signature scanning failed");
        }
    } else {
        DebugLog("[BF2042] BF2042Integration is null!");
    }

    DebugLog("[BF2042] ⚠️ Signature scanning failed, falling back to hardcoded seed scanning...");

    // Fallback to hardcoded addresses if signature scanning fails
    const std::vector<QWORD> seedAddresses = {
        0x621BE48,    // ✅ Forum confirmed GameContext (wasd2 Nov 2021)
        0x14621ADC8,  // ✅ Alternative GameContext (wasd2 Nov 2021)
        0x621BE38,    // ✅ Backup seed (±0x10)
        0x621BE58     // ✅ Backup seed (±0x10)
    };

    // Build candidate list by scanning ±0x2000 around each seed (step 0x10)
    std::vector<QWORD> candidateAddresses;
    candidateAddresses.reserve(seedAddresses.size() * (0x2000 / 0x10) * 2 + seedAddresses.size());
    for (QWORD seed : seedAddresses) {
        candidateAddresses.push_back(seed);
        for (QWORD off = 0x10; off <= 0x2000; off += 0x10) {
            candidateAddresses.push_back(seed + off);
            candidateAddresses.push_back(seed - off);
        }
    }

    DebugLog("[BF6] Probing hardcoded GameContext candidates...");

    bool found = false;
    for (QWORD gc : candidateAddresses) {
        // Optional: peek raw value at GC
        QWORD raw = 0;
        getVMM()->readMemory(pid, gc, &raw, sizeof(raw));

        QWORD playerMgr = 0;
        if (!getVMM()->readMemory(pid, gc + 0xA8, &playerMgr, sizeof(playerMgr))) {
            continue;
        }
        if (!IsCanonicalUserPointer(playerMgr)) {
            continue;
        }

        // Read PlayerManager offsets
        QWORD clientPlayerArray = 0;
        QWORD localPlayer = 0;
        DWORD playerCount = 0;

        bool ok1 = getVMM()->readMemory(pid, playerMgr + 0x570, &clientPlayerArray, sizeof(clientPlayerArray));
        bool ok2 = getVMM()->readMemory(pid, playerMgr + 0x560, &localPlayer, sizeof(localPlayer));
        bool ok3 = getVMM()->readMemory(pid, playerMgr + 0x578, &playerCount, sizeof(playerCount));

        if (!(ok1 && ok2 && ok3)) {
            continue;
        }
        if (!IsCanonicalUserPointer(clientPlayerArray)) {
            continue;
        }

        // Sanity checks: local player pointer and player array contents
        bool localOk = IsCanonicalUserPointer(localPlayer);
        bool anyPlayerOk = false;
        {
            // Probe first few entries for canonical pointers
            const uint32_t probeCount = 16;
            for (uint32_t i = 0; i < probeCount; ++i) {
                QWORD p = 0;
                if (!getVMM()->readMemory(pid, clientPlayerArray + i * sizeof(QWORD), &p, sizeof(p))) {
                    continue;
                }
                if (IsCanonicalUserPointer(p)) { anyPlayerOk = true; break; }
            }
        }
        if (!localOk && !anyPlayerOk) {
            // Likely a false-positive GameContext; keep scanning
            continue;
        }

        // Success: wire base addresses
        this->worldAddress = gc;
        // BF2042 GameRenderer address (Forum confirmed - wasd2 Nov 2021)
        this->networkManagerAddress = 0x62CF960;

        std::ostringstream oss;
        oss << std::hex;
        oss << "[BF2042] GameContext=0x" << gc
            << ", PlayerManager=0x" << playerMgr
            << ", ClientPlayerArray=0x" << clientPlayerArray
            << ", LocalPlayer=0x" << localPlayer
            << std::dec
            << ", PlayerCount=" << playerCount;
        DebugLog(oss.str());

        found = true;
        break;
    }

    if (!found) {
        DebugLog("[BF2042] FAILED: Could not validate GameContext + offsets with provided candidates (scanned ±0x2000 around seeds)");
        // Fallback to first candidate to keep app running
        this->worldAddress = seedAddresses.front();
        this->networkManagerAddress = 0x62CF960;
    }
}

DWORD BF2042::Mem::getPid() {
	auto pid = staticManager.getPid(EXE_NAME);
	if (pid == 0) {
		DebugLog("[BF2042 DEBUG] WARNING: Process '" + std::string(EXE_NAME) + "' not found");
	}
	return pid;
}

BF2042::WorldPointer BF2042::Mem::getWorld()
{
	BF2042::WorldPointer wp;
    // Resolve with standard flags only
    wp.resolveObject(getVMM(), getPid(), this->worldAddress, VMMDLL_FLAG_NOCACHE);
	return wp;
}

BF2042::NetworkManager BF2042::Mem::getNetworkManager()
{
	BF2042::NetworkManager nm;
	nm.resolveObject(getVMM(), getPid(), this->networkManagerAddress);
	return nm;
}

std::vector<std::shared_ptr<BF2042::Entity>> BF2042::Mem::getAllUniqueEntities()
{
	// For BF2042, entities are players from the PlayerManager
	std::vector<std::shared_ptr<BF2042::Entity>> entities;

	try {
		auto world = getWorld();
		if (!world.isValid()) {
			return entities;
		}

		auto gameContext = world.GameContextPtr;
		if (!gameContext || !gameContext->isValid()) {
			return entities;
		}

		auto playerManager = gameContext->PlayerMgr;
		if (!playerManager || !playerManager->isValid()) {
			return entities;
		}

		// Resolve player array
		if (playerManager->resolvePlayerArray(getVMM(), getPid())) {
			auto players = playerManager->getAllPlayers();

			// Convert ClientPlayer to Entity (they're the same type via alias)
			for (const auto& player : players) {
				if (player && player->isValid()) {
					entities.push_back(player);
				}
			}
		}
	}
	catch (const std::exception& e) {
		std::cerr << "[BF2042] Error getting entities: " << e.what() << std::endl;
	}

	return entities;
}

void BF2042::Mem::processESP()
{
	// This method can be used for any additional ESP processing if needed
	// For now, the main ESP processing is handled by the OverlayAdapter
}
