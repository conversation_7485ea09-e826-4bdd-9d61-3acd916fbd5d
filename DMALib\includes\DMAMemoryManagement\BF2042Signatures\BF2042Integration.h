#pragma once
#include "BF2042Signatures.h"
#include "../VmmManager/VmmManager.h"
#include <unordered_map>
#include <chrono>
#include <vector>

namespace DMAMem {

// Forward declarations
class DynamicCR3Manager;

// Vector3 structure for 3D coordinates
struct Vector3 {
    float x, y, z;
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
};

// BF2042 Structure Definitions (Forum Confirmed)
struct BF2042Offsets {
    // GameContext offsets
    static constexpr QWORD GAMECONTEXT_PLAYERMANAGER = 0xA8;
    static constexpr QWORD GAMECONTEXT_GAMERENDERER = 0x60;
    
    // PlayerManager offsets  
    static constexpr QWORD PLAYERMANAGER_LOCALPLAYER = 0x560;
    static constexpr QWORD PLAYERMANAGER_CLIENTARRAY = 0x570;
    
    // ClientSoldierEntity structure (Size: 0x2058)
    static constexpr QWORD CLIENTSOLDIER_CLIENTPLAYER = 0xE8;
    static constexpr QWORD CLIENTSOLDIER_HEALTHCOMPONENT = 0xE00;
    static constexpr QWORD CLIENTSOLDIER_WEAPONSCOMPONENT = 0x1570;
    static constexpr QWORD CLIENTSOLDIER_SPOTTINGCOMPONENT = 0x1E00;
    static constexpr QWORD CLIENTSOLDIER_BONECOLLISION = 0x1BC0;
    static constexpr QWORD CLIENTSOLDIER_POSITION = 0x20E0;
    
    // Enhanced component offsets (Forum confirmed)
    static constexpr QWORD WEAPONSCOMPONENT_ENHANCED = 0x1590;
    static constexpr QWORD BONECOLLISION_ENHANCED = 0x1BC0;
    static constexpr QWORD SPOTTINGCOMPONENT_ENHANCED = 0x1E00;
    static constexpr QWORD MESHOVERLAY_ENHANCED = 0x1EA0;
    static constexpr QWORD HEALTHCOMPONENT_ENHANCED = 0x1C60;
    
    // Visibility system
    static constexpr QWORD SPOTTING_OCCLUDEDTYPE = 0xB9;
    static constexpr QWORD MESHOVERLAY_PACSCAN = 0x50;
    
    // Weapon system chain
    static constexpr QWORD WEAPONS_CLIENTWEAPON = 0x4AB8;
    static constexpr QWORD CLIENTWEAPON_OFFSET = 0x38;
    static constexpr QWORD WEAPONFIRING_OFFSET = 0x3CF8;
    static constexpr QWORD FIRINGDATA_OFFSET = 0x130;
    static constexpr QWORD BULLETSPEED_CHAIN = 0x20;
    static constexpr QWORD BULLETSPEED_OFFSET = 0x208;
    static constexpr QWORD RECOIL_CHAIN = 0x18;
    static constexpr QWORD RECOIL_OFFSET = 0xA0;
    
    // Vehicle system
    static constexpr QWORD CLIENTPLAYER_VEHICLE = 0xB8;
    static constexpr QWORD CLIENTPLAYER_SOLDIER = 0xC8;
    static constexpr QWORD VEHICLE_HEALTH = 0x70;
    static constexpr QWORD VEHICLE_MAXHEALTH = 0x15C;
    
    // Bone system
    static constexpr QWORD BONECOLLISION_UPDATEPOSE = 0x20;
    
    // W2S Matrix chain
    static constexpr QWORD GAMERENDERER_RENDERVIEW = 0x60;
    static constexpr QWORD RENDERVIEW_MATRIX = 0x480;
    
    // Movement prediction
    static constexpr QWORD WEAPONFIRING_VELOCITY = 0x68;
    static constexpr QWORD VELOCITY_OFFSET = 0x148;
    static constexpr QWORD CLIENTSOLDIER_YAW = 0x1318;
};

// Visibility states
enum class VisibilityState : BYTE {
    VISIBLE = 0x30,
    OCCLUDED = 0x20
};

// PAC scan types (Paik detection)
enum class SpotType : BYTE {
    PASSIVE_RADAR = 0x01,
    DRONE = 0x06,
    PROXIMITY = 0x08,
    PAC_SCAN = 0x12,  // Paik chams scan
    PING = 0x20
};

// PAC scan flags
enum class PACScanFlags : BYTE {
    NOT_SCANNED = 0x0,
    ACTIVELY_SCANNED = 0x2
};

/**
 * \brief Comprehensive BF2042 Integration with Forum-Confirmed Structures
 * Implements all known BF2042 offsets, anti-cheat bypass, and signature scanning
 */
class BF2042Integration {
private:
    VmmManager* vmmManager;
    BF2042Signatures* signatures;
    DWORD targetPID;
    bool isInitialized;



    // Cached addresses with validation timestamps
    struct CachedAddress {
        QWORD address;
        std::chrono::steady_clock::time_point lastValidated;
        bool isValid;
        int validationFailures;
    };
    
    std::unordered_map<std::string, CachedAddress> addressCache;
    
    // Performance tracking
    std::chrono::steady_clock::time_point lastScanTime;
    static constexpr auto CACHE_VALIDITY_DURATION = std::chrono::minutes(5);
    static constexpr int MAX_VALIDATION_FAILURES = 3;

    // ===== PRIVATE HELPER METHODS =====
    void CacheAddresses();
    bool ScanForAddresses();
    bool ValidateAddressCache();
    void InvalidateCache();
    bool IsCanonicalAddress(QWORD address);
    bool ValidateStructureChain(QWORD baseAddress, const std::vector<QWORD>& offsets);
    template<typename T>
    bool SafeRead(QWORD address, T& value);
    bool SafeReadBytes(QWORD address, void* buffer, SIZE_T size);
    QWORD GetCachedAddress(const std::string& key);
    void SetCachedAddress(const std::string& key, QWORD address);

public:
    /**
     * \brief Constructor with enhanced initialization
     * \param vmm Pointer to existing VmmManager
     * \param pid Target process ID
     */
    BF2042Integration(VmmManager* vmm, DWORD pid);
    ~BF2042Integration();

    /**
     * \brief Initialize comprehensive signature scanning with anti-cheat bypass
     * \return true if initialization successful
     */
    BOOL Initialize();



    // ===== CORE ADDRESS RESOLUTION =====
    
    /**
     * \brief Get GameContext using signature scanning with validation
     * \return GameContext address, 0 if not found
     */
    QWORD GetGameContext();
    
    /**
     * \brief Get PlayerManager (GameContext + 0xA8)
     * \return PlayerManager address, 0 if not found
     */
    QWORD GetPlayerManager();

    /**
     * \brief Check if EA anti-cheat corruption has been detected
     * \return true if corruption detected, false otherwise
     */
    bool IsCorruptionDetected();
    
    /**
     * \brief Get ClientPlayerArray (PlayerManager + 0x570)
     * \return ClientPlayerArray address, 0 if not found
     */
    QWORD GetClientPlayerArray();
    
    /**
     * \brief Get LocalPlayer (PlayerManager + 0x560)
     * \return LocalPlayer address, 0 if not found
     */
    QWORD GetLocalPlayer();
    
    /**
     * \brief Get GameRenderer for W2S matrix
     * \return GameRenderer address, 0 if not found
     */
    QWORD GetGameRenderer();

    // ===== ENHANCED STRUCTURE ACCESS =====
    
    /**
     * \brief Get player's health component using forum-confirmed offsets
     * \param clientSoldier ClientSoldierEntity address
     * \return HealthComponent address
     */
    QWORD GetPlayerHealthComponent(QWORD clientSoldier);
    
    /**
     * \brief Get player's weapons component (enhanced offset 0x1590)
     * \param clientSoldier ClientSoldierEntity address
     * \return WeaponsComponent address
     */
    QWORD GetPlayerWeaponsComponent(QWORD clientSoldier);
    
    /**
     * \brief Get player's spotting component for visibility detection
     * \param clientSoldier ClientSoldierEntity address
     * \return SpottingComponent address
     */
    QWORD GetPlayerSpottingComponent(QWORD clientSoldier);
    
    /**
     * \brief Get player's bone collision component for bone access
     * \param clientSoldier ClientSoldierEntity address
     * \return BoneCollisionComponent address
     */
    QWORD GetPlayerBoneCollision(QWORD clientSoldier);
    
    /**
     * \brief Get player's mesh overlay component for PAC scan detection
     * \param clientSoldier ClientSoldierEntity address
     * \return MeshOverlayComponent address
     */
    QWORD GetPlayerMeshOverlay(QWORD clientSoldier);
    
    /**
     * \brief Get player's position using enhanced offset
     * \param clientSoldier ClientSoldierEntity address
     * \return Position address
     */
    QWORD GetPlayerPosition(QWORD clientSoldier);

    // ===== VISIBILITY SYSTEM =====
    
    /**
     * \brief Get player's visibility state (occluded/visible)
     * \param clientSoldier ClientSoldierEntity address
     * \return VisibilityState enum
     */
    VisibilityState GetPlayerVisibility(QWORD clientSoldier);
    
    /**
     * \brief Get player's spot type (radar, drone, PAC scan, etc.)
     * \param clientSoldier ClientSoldierEntity address
     * \return SpotType enum
     */
    SpotType GetPlayerSpotType(QWORD clientSoldier);
    
    /**
     * \brief Check if player is actively PAC scanned by Paik
     * \param clientSoldier ClientSoldierEntity address
     * \return true if player is PAC scanned
     */
    bool IsPlayerPACScanned(QWORD clientSoldier);

    // ===== WEAPON SYSTEM =====
    
    /**
     * \brief Get player's current weapon using weapon chain
     * \param clientSoldier ClientSoldierEntity address
     * \return ClientWeapon address
     */
    QWORD GetPlayerWeapon(QWORD clientSoldier);
    
    /**
     * \brief Get weapon's bullet speed for prediction
     * \param weapon ClientWeapon address
     * \return Bullet speed value
     */
    float GetWeaponBulletSpeed(QWORD weapon);
    
    /**
     * \brief Get weapon's recoil vector
     * \param weapon ClientWeapon address
     * \return Recoil vector (x, y, z)
     */
    Vector3 GetWeaponRecoil(QWORD weapon);
    
    /**
     * \brief Get soldier velocity for movement prediction
     * \param clientSoldier ClientSoldierEntity address
     * \return Velocity vector
     */
    Vector3 GetSoldierVelocity(QWORD clientSoldier);
    
    /**
     * \brief Get authoritative yaw for prediction
     * \param clientSoldier ClientSoldierEntity address
     * \return Yaw value
     */
    float GetAuthoritativeYaw(QWORD clientSoldier);

    // ===== VEHICLE SYSTEM =====

    /**
     * \brief Get player's vehicle if in one
     * \param clientPlayer ClientPlayer address
     * \return ClientVehicleEntity address, 0 if not in vehicle
     */
    QWORD GetPlayerVehicle(QWORD clientPlayer);

    /**
     * \brief Check if player is in a vehicle
     * \param clientPlayer ClientPlayer address
     * \return true if in vehicle
     */
    bool IsPlayerInVehicle(QWORD clientPlayer);

    /**
     * \brief Get vehicle's current health
     * \param vehicle ClientVehicleEntity address
     * \return Health value
     */
    float GetVehicleHealth(QWORD vehicle);

    /**
     * \brief Get vehicle's maximum health
     * \param vehicle ClientVehicleEntity address
     * \return Max health value
     */
    float GetVehicleMaxHealth(QWORD vehicle);

    // ===== BONE SYSTEM =====

    /**
     * \brief Get bone transforms with ForceUpdate support
     * \param clientSoldier ClientSoldierEntity address
     * \return UpdatePoseResultData address
     */
    QWORD GetBoneTransforms(QWORD clientSoldier);

    /**
     * \brief Force bone update for accurate positions
     * \param clientSoldier ClientSoldierEntity address
     * \return true if update successful
     */
    bool ForceBoneUpdate(QWORD clientSoldier);

    // ===== W2S MATRIX SYSTEM =====

    /**
     * \brief Get view matrix for world-to-screen conversion
     * \return Matrix address, 0 if not found
     */
    QWORD GetViewMatrix();

    /**
     * \brief Get render view from GameRenderer
     * \return RenderView address
     */
    QWORD GetRenderView();

    // ===== ANTI-CHEAT BYPASS =====

    /**
     * \brief Attempt advanced CR3/DTB bypass for EAAC
     * \return true if bypass successful
     */
    bool TryFixCR3();

    /**
     * \brief Validate memory access stability
     * \return true if memory reads are working
     */
    bool ValidateMemoryAccess();

    /**
     * \brief Get orphaned DTBs for bypass attempts
     * \return vector of potential DTB values
     */
    std::vector<QWORD> GetOrphanedDTBs();

    /**
     * \brief Validate DTB for specific process
     * \param pid Process ID
     * \param dtb DTB value to test
     * \return true if DTB is valid
     */
    bool ValidateDTBForProcess(DWORD pid, QWORD dtb);

    // ===== VALIDATION METHODS =====

    /**
     * \brief Validate GameContext structure
     * \param address GameContext address
     * \return true if valid
     */
    bool ValidateGameContext(QWORD address);

    /**
     * \brief Validate PlayerManager structure
     * \param address PlayerManager address
     * \return true if valid
     */
    bool ValidatePlayerManager(QWORD address);

    /**
     * \brief Validate ClientSoldierEntity structure
     * \param address ClientSoldierEntity address
     * \return true if valid
     */
    bool ValidateClientSoldier(QWORD address);

    /**
     * \brief Validate entire structure chain
     * \return true if all structures are valid
     */
    bool ValidateAllStructures();

    // ===== ENHANCED PLAYER LOOP =====

    /**
     * \brief Comprehensive player loop with all features
     * Includes visibility, PAC scan detection, weapon info, vehicles
     * \param maxPlayers Maximum players to process
     * \return number of valid players processed
     */
    int EnhancedPlayerLoop(int maxPlayers = 128);

    /**
     * \brief Get player by index with full validation
     * \param playerIndex Index in player array
     * \return Player address, 0 if invalid
     */
    QWORD GetPlayerByIndex(int playerIndex);

    /**
     * \brief Batch read player data for performance
     * \param playerIndices Vector of player indices
     * \param playerData Vector to store player data
     * \return number of successful reads
     */
    int BatchReadPlayers(const std::vector<int>& playerIndices, std::vector<QWORD>& playerData);

    // ===== FALLBACK COMPATIBILITY =====

    /**
     * \brief Set fallback addresses for compatibility with existing ESP
     * \param gameContext Hardcoded GameContext address
     * \param playerMgr Hardcoded PlayerManager address
     */
    void SetFallbackAddresses(QWORD gameContext, QWORD playerMgr);

    /**
     * \brief Check if signature scanning is working
     * \return true if signatures are resolving correctly
     */
    bool IsSignatureScanningWorking();

    /**
     * \brief Force refresh of all cached addresses
     */
    void RefreshAddresses();

    // ===== DIAGNOSTICS AND PERFORMANCE =====

    /**
     * \brief Print comprehensive diagnostics
     */
    void PrintDiagnostics();

    /**
     * \brief Get performance statistics
     * \return string with performance info
     */
    std::string GetPerformanceStats();

    /**
     * \brief Dump memory region for analysis
     * \param address Base address
     * \param size Size to dump
     * \param filePath Output file path
     * \return true if dump successful
     */
    bool DumpMemoryRegion(QWORD address, SIZE_T size, const char* filePath);
};

} // namespace DMAMem
