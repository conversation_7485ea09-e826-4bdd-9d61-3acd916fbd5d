#pragma once
#include "DMAMemoryManagement/includes.h"
#include "VehicleEntityData.h"
#include "HealthComponent.h"

namespace BF2042 {
    class ClientVehicleEntity : public DMAMem::MemoryObject {
    public:
        std::shared_ptr<VehicleEntityData> VehicleData;
        std::shared_ptr<HealthComponent> Health;
        
        ClientVehicleEntity() {
            VehicleData = std::shared_ptr<VehicleEntityData>(new VehicleEntityData());
            Health = std::shared_ptr<HealthComponent>(new HealthComponent());

            // BF2042 ClientVehicleEntity offsets - Forum confirmed
            this->registerPointer(0x0038, VehicleData.get()); // m_VehicleData - Forum: 0x0038
            this->registerPointer(0x100, Health.get());       // m_HealthComponent - Note: May need update to 0xE00 like soldier
        }
        
        // Helper methods
        bool isValid() const {
            return VehicleData && VehicleData->isValid();
        }

        std::string getVehicleName() const {
            if (VehicleData) {
                return VehicleData->getVehicleName();
            }
            return "";
        }

        bool isAlive() const {
            return Health && Health->isAlive();
        }

        float getHealthPercentage() const {
            if (Health) {
                return Health->getHealthPercentage();
            }
            return 0.0f;
        }
    };
}
