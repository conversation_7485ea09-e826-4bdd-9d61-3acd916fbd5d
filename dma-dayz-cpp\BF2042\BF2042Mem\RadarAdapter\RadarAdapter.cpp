#include "RadarAdapter.h"
#include <imgui.h>
#include <cmath>

BF2042::RadarAdapter::RadarAdapter(std::shared_ptr<BF2042::MemoryUpdater> memoryUpdater, std::shared_ptr<DMARender::RenderBridge> bridge)
    : memoryUpdater(memoryUpdater), bridge(bridge) {
}

void BF2042::RadarAdapter::DrawOverlay(DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform) {
    // Call our internal DrawRadar method
    DrawRadar();
}

void BF2042::RadarAdapter::DrawRadar() {
    if (!memoryUpdater || !bridge) {
        return;
    }

    // Get local player for radar center
    auto localPlayer = memoryUpdater->getLocalPlayer();
    if (!localPlayer || !localPlayer->SoldierEntity) {
        return;
    }

    DMARender::Vector3 localPos = localPlayer->SoldierEntity->getPosition();
    uint32_t localTeamId = localPlayer->TeamId;

    // Radar window setup
    ImGui::SetNextWindowPos(ImVec2(10, 10), ImGuiCond_FirstUseEver);
    ImGui::SetNextWindowSize(ImVec2(300, 300), ImGuiCond_FirstUseEver);
    
    bool showRadar = bridge->shouldShowRadar();
    if (ImGui::Begin("BF6 Radar", &showRadar)) {
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Radar area (excluding title bar)
        ImVec2 radarPos = ImVec2(windowPos.x + 10, windowPos.y + 30);
        ImVec2 radarSize = ImVec2(windowSize.x - 20, windowSize.y - 40);
        ImVec2 radarCenter = ImVec2(radarPos.x + radarSize.x / 2, radarPos.y + radarSize.y / 2);
        
        // Radar background
        drawList->AddRectFilled(radarPos, ImVec2(radarPos.x + radarSize.x, radarPos.y + radarSize.y), 
                               IM_COL32(0, 0, 0, 100));
        drawList->AddRect(radarPos, ImVec2(radarPos.x + radarSize.x, radarPos.y + radarSize.y), 
                         IM_COL32(255, 255, 255, 255));

        // Radar grid
        float gridSpacing = radarSize.x / 10.0f;
        for (int i = 1; i < 10; ++i) {
            float x = radarPos.x + i * gridSpacing;
            float y = radarPos.y + i * gridSpacing;
            
            drawList->AddLine(ImVec2(x, radarPos.y), ImVec2(x, radarPos.y + radarSize.y), 
                             IM_COL32(255, 255, 255, 50));
            drawList->AddLine(ImVec2(radarPos.x, y), ImVec2(radarPos.x + radarSize.x, y), 
                             IM_COL32(255, 255, 255, 50));
        }

        // Center crosshair
        drawList->AddLine(ImVec2(radarCenter.x - 5, radarCenter.y), ImVec2(radarCenter.x + 5, radarCenter.y), 
                         IM_COL32(255, 255, 255, 255), 2.0f);
        drawList->AddLine(ImVec2(radarCenter.x, radarCenter.y - 5), ImVec2(radarCenter.x, radarCenter.y + 5), 
                         IM_COL32(255, 255, 255, 255), 2.0f);

        // Map scale (meters per pixel)
        float mapScale = radarRange / (radarSize.x / 2.0f);

        // Draw local player
        if (showLocalPlayer) {
            drawList->AddCircleFilled(radarCenter, 4.0f, IM_COL32(0, 255, 255, 255)); // Cyan
        }

        // Draw other players
        if (showPlayers) {
            auto players = memoryUpdater->getAllPlayers();
            
            for (const auto& player : players) {
                if (!player || !player->isValid() || !player->SoldierEntity) {
                    continue;
                }

                // Skip local player
                if (player.get() == localPlayer.get()) {
                    continue;
                }

                DMARender::Vector3 playerPos = player->SoldierEntity->getPosition();
                float distance = sqrt(pow(playerPos.x - localPos.x, 2) + pow(playerPos.z - localPos.z, 2));
                
                // Skip if out of radar range
                if (distance > radarRange) {
                    continue;
                }

                // Convert world position to radar position
                DMARender::Vector2 radarPlayerPos = worldToRadar(playerPos, localPos, mapScale);
                ImVec2 screenPos = ImVec2(radarCenter.x + radarPlayerPos.x, radarCenter.y + radarPlayerPos.y);

                // Check if position is within radar bounds
                if (screenPos.x < radarPos.x || screenPos.x > radarPos.x + radarSize.x ||
                    screenPos.y < radarPos.y || screenPos.y > radarPos.y + radarSize.y) {
                    continue;
                }

                // Get player color based on team
                ImU32 playerColor = getPlayerColor(player, localTeamId);

                // Draw player dot
                drawList->AddCircleFilled(screenPos, 3.0f, playerColor);

                // Draw player name if close enough
                if (distance < radarRange / 4.0f) {
                    std::string playerName = player->getName();
                    if (!playerName.empty()) {
                        ImVec2 textSize = ImGui::CalcTextSize(playerName.c_str());
                        drawList->AddText(ImVec2(screenPos.x - textSize.x / 2, screenPos.y + 5), 
                                         IM_COL32(255, 255, 255, 255), playerName.c_str());
                    }
                }
            }
        }

        // Radar info
        ImGui::SetCursorPosY(ImGui::GetWindowHeight() - 60);
        ImGui::Text("Range: %.0fm", radarRange);
        ImGui::Text("Players: %zu", memoryUpdater->getPlayerCount());
        
        // Radar controls
        ImGui::SliderFloat("Range", &radarRange, 100.0f, 2000.0f, "%.0fm");
    }
    ImGui::End();
}

DMARender::Vector2 BF2042::RadarAdapter::worldToRadar(const DMARender::Vector3& worldPos, const DMARender::Vector3& localPos, float mapScale) const {
    // Convert world coordinates to radar coordinates
    float deltaX = worldPos.x - localPos.x;
    float deltaZ = worldPos.z - localPos.z;

    // Scale to radar size
    float radarX = deltaX / mapScale;
    float radarY = -deltaZ / mapScale; // Flip Z axis for radar display

    return DMARender::Vector2{radarX, radarY};
}

ImU32 BF2042::RadarAdapter::getPlayerColor(const std::shared_ptr<BF2042::ClientPlayer>& player, uint32_t localTeamId) const {
    if (player->TeamId == localTeamId) {
        return IM_COL32(0, 255, 0, 255); // Green for friendly
    } else if (player->TeamId != 0) {
        return IM_COL32(255, 0, 0, 255); // Red for enemy
    } else {
        return IM_COL32(255, 255, 0, 255); // Yellow for neutral
    }
}
