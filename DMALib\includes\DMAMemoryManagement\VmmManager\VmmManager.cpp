#include "./VmmManager.h"

VMM_HANDLE DMAMem::VmmManager::getVmm() {
	if (!hVMM) {
		hVMM = initialize();
	}
	return hVMM;
}


VMM_HANDLE DMAMem::VmmManager::initialize()
{
	VMMDLL_CloseAll();
	std::cout << " [ + ] Connecting to DMA Card..." << std::endl;

	// Updated arguments to include -norefresh as suggested in the forum post
	LPSTR args[] = { LPSTR(""), LPSTR("-device"), LPSTR("fpga://algo=0"), LPSTR("-norefresh") };

	VMM_HANDLE handle = VMMDLL_Initialize(4, args);
	if (!handle)
		std::cout << "[ ! ] Vmm Initialization Failed..." << std::endl;

	std::cout << " [ + ] Connected to DMA Card..." << std::endl;

	// Start the manual refresh loop to prevent 5-second freezes
	// But with performance-optimized settings for ESP applications
	if (manual_refresh) {
		// Set performance mode by default to avoid ESP lag
		setPerformanceMode(true);
		startRefreshLoop();
		std::cout << " [ + ] DMA Refresh Loop Started (Performance Mode)" << std::endl;
	}

	return handle;
}

// The critical refresh loop that prevents DMA freezes every 5 seconds
void DMAMem::VmmManager::RefreshLoop() {
	uint64_t last_slow = 0;
	uint64_t last_medium = 0;
	uint64_t last_fast = 0;
	uint64_t last_mem = 0;

	while (this->hVMM != NULL) {
		if (wants_no_refresh) {
			break;
		}

		auto now = GetCurrentTime();

		// Get current configuration (atomic variables)
		uint64_t currentFastUs = fastRefreshUs.load();
		uint64_t currentMediumUs = mediumRefreshUs.load();
		uint64_t currentSlowUs = slowRefreshUs.load();
		uint64_t currentMemUs = memRefreshUs.load();
		uint64_t currentSleepMs = sleepMs.load();

		if (!last_fast) {
			last_slow = now;
			last_medium = now;
			last_fast = now;
			last_mem = now;
			continue;
		}

		// Slow refresh (configurable interval)
		if (wants_full_refresh) {
			if (now - last_slow > currentSlowUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_SLOW, 1);
				last_slow = GetCurrentTime();
			}
		}
		else {
			last_slow = 0;
		}

		// Medium refresh (configurable interval)
		if (wants_refresh) {
			if (now - last_medium > currentMediumUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEDIUM, 1);
				last_medium = GetCurrentTime();
			}
		}
		else {
			last_medium = 0;
		}

		// Fast refresh (configurable interval) - TLB partial refresh
		if (now - last_fast > currentFastUs) {
			VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_TLB_PARTIAL, 1);
			last_fast = GetCurrentTime();
		}

		// Memory refresh (configurable interval)
		if (wants_full_refresh) {
			if (now - last_mem > currentMemUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEM, 1);
				last_mem = GetCurrentTime();
			}
		}

		// Configurable sleep interval
		Sleep(currentSleepMs);
	}
}

void DMAMem::VmmManager::startRefreshLoop() {
	if (!refreshThreadRunning) {
		refreshThreadRunning = true;
		refreshThread = std::thread(&VmmManager::RefreshLoop, this);
		refreshThread.detach(); // Detach the thread so it runs independently
		std::cout << " [ + ] DMA Refresh Loop Started..." << std::endl;
	}
}

void DMAMem::VmmManager::stopRefreshLoop() {
	if (refreshThreadRunning) {
		wants_no_refresh = true;
		refreshThreadRunning = false;
		// Give the thread a moment to exit gracefully
		Sleep(100);
		std::cout << " [ + ] DMA Refresh Loop Stopped..." << std::endl;
	}
}

DMAMem::VmmManager::~VmmManager() {
	// Stop the refresh loop before cleanup
	stopRefreshLoop();

	// Close the VMM handle
	if (hVMM) {
		VMMDLL_Close(hVMM);
		hVMM = nullptr;
	}
}

BOOL DMAMem::VmmManager::readMemory(DWORD pid, QWORD remoteAddress, void* destination, int size, ULONG64 flags)
{
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, flags);
}

// Optimized memory reading for ESP performance
BOOL DMAMem::VmmManager::readMemoryOptimized(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	// Use optimized flags for better ESP performance
	ULONG64 optimizedFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_FORCECACHE_READ;
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, optimizedFlags);
}

// Ultra-fast memory reading for critical ESP operations
BOOL DMAMem::VmmManager::readMemoryUltraFast(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	// Use ultra-fast flags - no cache, no paging, force cache read
	ULONG64 ultraFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_FORCECACHE_READ | VMMDLL_FLAG_NOPAGING;
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, ultraFlags);
}

// Explicitly override per-process DTB (CR3)
BOOL DMAMem::VmmManager::setProcessDTB(DWORD pid, QWORD dtb)
{
	if (!this->getVmm()) return FALSE;
	ULONG64 opt = VMMDLL_OPT_PROCESS_DTB | (ULONG64)pid; // low dword = pid
	BOOL ok = VMMDLL_ConfigSet(this->hVMM, opt, (ULONG64)dtb);
	std::cout << "[CR3 BYPASS] setProcessDTB pid=" << pid << " dtb=0x" << std::hex << dtb << " => " << (ok ? "OK" : "FAIL") << std::endl;
	return ok;
}

BOOL DMAMem::VmmManager::clearProcessDTB(DWORD pid)
{
	// Many MemProcFS builds clear override with DTB=0
	return setProcessDTB(pid, 0);
}
BOOL DMAMem::VmmManager::primeCachedPML4ForPid(DWORD pid)
{
    // Requires patched vmmdll providing the -666/333 handshake and cached PML4 path
    if (!this->getVmm()) return FALSE;

    // 1) Get DTB (physical) for pid
    VMMDLL_PROCESS_INFORMATION pi = { 0 };
    SIZE_T cb = sizeof(pi);
    if (!VMMDLL_ProcessGetInformation(this->getVmm(), pid, &pi, &cb)) {
        return FALSE;
    }
    QWORD dtb = (QWORD)pi.paDTB;
    if (!dtb) return FALSE;

    // 2) Read PML4 (512 entries) from physical DTB
    DWORD readsize = 0;
    ZeroMemory(m_cachedPml4, sizeof(m_cachedPml4));
    if (!VMMDLL_MemReadEx(this->getVmm(), (DWORD)-1, dtb, (PBYTE)m_cachedPml4, sizeof(m_cachedPml4), &readsize,
                          VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_NOPAGING | VMMDLL_FLAG_ZEROPAD_ON_FAIL | VMMDLL_FLAG_NOPAGING_IO)) {
        return FALSE;
    }

    // 3) Inject PML4 into vmmdll (-666/333 path)
    (void)VMMDLL_MemReadEx((VMM_HANDLE)-666, 333, (ULONG64)m_cachedPml4, 0, 0, 0, 0);
    // 4) Mark process DTB as 666 to signal cached translation usage
    VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_PROCESS_DTB | pid, 666);
    return TRUE;
}

BOOL DMAMem::VmmManager::primeCachedPML4ForPidWithDTB(DWORD pid, QWORD dtb)
{
    if (!this->getVmm() || !dtb) return FALSE;
    DWORD readsize = 0;
    ZeroMemory(m_cachedPml4, sizeof(m_cachedPml4));
    if (!VMMDLL_MemReadEx(this->getVmm(), (DWORD)-1, dtb, (PBYTE)m_cachedPml4, sizeof(m_cachedPml4), &readsize,
                          VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_NOPAGING | VMMDLL_FLAG_ZEROPAD_ON_FAIL | VMMDLL_FLAG_NOPAGING_IO)) {
        return FALSE;
    }
    (void)VMMDLL_MemReadEx((VMM_HANDLE)-666, 333, (ULONG64)m_cachedPml4, 0, 0, 0, 0);
    VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_PROCESS_DTB | pid, 666);
    return TRUE;
}

void DMAMem::VmmManager::refreshTLBFull() {
    if (this->getVmm()) {
        VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_TLB, 1);
    }
}

void DMAMem::VmmManager::refreshTLBPartial() {
    if (this->getVmm()) {
        VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_TLB_PARTIAL, 1);
    }
}



std::vector<QWORD> DMAMem::VmmManager::discoverDTBCandidatesFromPFN(DWORD pid, DWORD maxCandidates)
{
    std::vector<QWORD> candidates;
    if (!this->getVmm()) return candidates;

    // Retrieve physical memory map to bound PFN ranges
    PVMMDLL_MAP_PHYSMEM pPhys = nullptr;
    if (!VMMDLL_Map_GetPhysMem(this->getVmm(), &pPhys) || !pPhys) {
        return candidates;
    }

    auto tryBatch = [&](std::vector<DWORD>& batchPfns) {
        if (batchPfns.empty()) return;
        PVMMDLL_MAP_PFN pPfnMap = nullptr;
        if (VMMDLL_Map_GetPfnEx(this->getVmm(), batchPfns.data(), (DWORD)batchPfns.size(), &pPfnMap, VMMDLL_PFN_FLAG_EXTENDED) && pPfnMap) {
            for (DWORD i = 0; i < pPfnMap->cMap; ++i) {
                const auto& e = pPfnMap->pMap[i];
                if (e.tpExtended == VmmDll_PfnExType_PageTable) {
                    // 1) Use PML4 PFN from PTE chain if present (index 3 is PML4 on x64)
                    DWORD pml4Pfn = 0;
                    if (e.AddressInfo.dwPfnPte[3]) {
                        pml4Pfn = e.AddressInfo.dwPfnPte[3];
                    }
                    if (pml4Pfn) {
                        QWORD dtb = ((QWORD)pml4Pfn) << 12;
                        if (std::find(candidates.begin(), candidates.end(), dtb) == candidates.end()) {
                            candidates.push_back(dtb);
                            if (candidates.size() >= maxCandidates) { VMMDLL_MemFree(pPfnMap); return; }
                        }
                    }
                    // 2) Fallback: consider this PFN directly if it’s tied to our PID
                    if (e.AddressInfo.dwPid == pid) {
                        QWORD dtb2 = ((QWORD)e.dwPfn) << 12;
                        if (std::find(candidates.begin(), candidates.end(), dtb2) == candidates.end()) {
                            candidates.push_back(dtb2);
                            if (candidates.size() >= maxCandidates) { VMMDLL_MemFree(pPfnMap); return; }
                        }
                    }
                }
            }
            VMMDLL_MemFree(pPfnMap);
        }
        batchPfns.clear();
    };

    // Iterate physical ranges and sample PFNs sparsely to keep latency down
    const DWORD batchSize = 4096;
    std::vector<DWORD> batchPfns;
    batchPfns.reserve(batchSize);

    for (DWORD i = 0; i < pPhys->cMap && candidates.size() < maxCandidates; ++i) {
        const auto& r = pPhys->pMap[i];
        QWORD paStart = r.pa;
        QWORD paEnd = r.pa + r.cb;
        if (r.cb == 0) continue;
        DWORD pfnStart = (DWORD)(paStart >> 12);
        DWORD pfnEnd = (DWORD)((paEnd - 1) >> 12);
        if (pfnEnd <= pfnStart) continue;

        // stride to ~1 PFN per 0x100 pages in this range (tunable)
        DWORD cPages = pfnEnd - pfnStart + 1;
        DWORD stride = (cPages / 0x100);
        if (stride < 1) stride = 1;

        for (DWORD pfn = pfnEnd; pfn >= pfnStart; ) { // scan from high to low
            batchPfns.push_back(pfn);
            if (batchPfns.size() >= batchSize) {
                tryBatch(batchPfns);
                if (candidates.size() >= maxCandidates) break;
            }
            if (pfn < pfnStart + stride) break;
            pfn -= stride;
        }
        if (!batchPfns.empty() && candidates.size() < maxCandidates) {
            tryBatch(batchPfns);
        }
    }

    VMMDLL_MemFree(pPhys);
    return candidates;
}

BOOL DMAMem::VmmManager::getProcessDTB(DWORD pid, QWORD* paDTB, QWORD* paDTB_UserOpt)
{
    if (paDTB) *paDTB = 0;
    if (paDTB_UserOpt) *paDTB_UserOpt = 0;

    VMMDLL_PROCESS_INFORMATION pi = { 0 };
    SIZE_T cb = sizeof(pi);
    if (!VMMDLL_ProcessGetInformation(this->getVmm(), pid, &pi, &cb)) {
        return FALSE;
    }

    // MemProcFS exposes physical DTB addresses here if available
    if (paDTB) *paDTB = (QWORD)pi.paDTB;
    if (paDTB_UserOpt) *paDTB_UserOpt = (QWORD)pi.paDTB_UserOpt;
    return TRUE;
}

// Enhanced CR3 discovery for EA anti-cheat bypass
std::vector<QWORD> DMAMem::VmmManager::discoverAllCR3Candidates(DWORD pid, DWORD maxCandidates)
{
    std::vector<QWORD> allCandidates;
    std::cout << "[CR3-Enhanced] === COMPREHENSIVE CR3 DISCOVERY ===" << std::endl;
    std::cout << "[CR3-Enhanced] Target PID: " << pid << std::endl;
    std::cout << "[CR3-Enhanced] Implementing EA anti-cheat bypass strategy" << std::endl;

    // Method 1: Standard DTB discovery
    QWORD paDTB = 0, paDTB_UserOpt = 0;
    if (getProcessDTB(pid, &paDTB, &paDTB_UserOpt)) {
        if (paDTB != 0) {
            allCandidates.push_back(paDTB);
            std::cout << "[CR3-Enhanced] Standard DTB: 0x" << std::hex << paDTB << std::dec << std::endl;
        }
        if (paDTB_UserOpt != 0 && paDTB_UserOpt != paDTB) {
            allCandidates.push_back(paDTB_UserOpt);
            std::cout << "[CR3-Enhanced] User DTB: 0x" << std::hex << paDTB_UserOpt << std::dec << std::endl;
        }
    }

    // Method 2: PFN-based discovery (EA's fake CR3s)
    auto pfnCandidates = discoverDTBCandidatesFromPFN(pid, maxCandidates / 2);
    for (QWORD cr3 : pfnCandidates) {
        if (std::find(allCandidates.begin(), allCandidates.end(), cr3) == allCandidates.end()) {
            allCandidates.push_back(cr3);
        }
    }
    std::cout << "[CR3-Enhanced] PFN discovery found " << pfnCandidates.size() << " candidates" << std::endl;

    // Method 3: Physical memory scanning
    auto physicalCandidates = scanPhysicalMemoryForCR3s(pid, maxCandidates / 4);
    for (QWORD cr3 : physicalCandidates) {
        if (std::find(allCandidates.begin(), allCandidates.end(), cr3) == allCandidates.end()) {
            allCandidates.push_back(cr3);
        }
    }
    std::cout << "[CR3-Enhanced] Physical scan found " << physicalCandidates.size() << " candidates" << std::endl;

    // Method 4: Process enumeration approach
    auto processCandidates = enumerateProcessCR3s(pid);
    for (QWORD cr3 : processCandidates) {
        if (std::find(allCandidates.begin(), allCandidates.end(), cr3) == allCandidates.end()) {
            allCandidates.push_back(cr3);
        }
    }
    std::cout << "[CR3-Enhanced] Process enumeration found " << processCandidates.size() << " candidates" << std::endl;

    std::cout << "[CR3-Enhanced] Total unique CR3 candidates: " << allCandidates.size() << std::endl;
    return allCandidates;
}

std::vector<QWORD> DMAMem::VmmManager::scanPhysicalMemoryForCR3s(DWORD pid, DWORD maxCandidates)
{
    std::vector<QWORD> candidates;
    if (!this->getVmm()) return candidates;

    std::cout << "[CR3-Physical] Scanning physical memory for CR3 patterns..." << std::endl;

    // Get physical memory map
    PVMMDLL_MAP_PHYSMEM pPhys = nullptr;
    if (!VMMDLL_Map_GetPhysMem(this->getVmm(), &pPhys) || !pPhys) {
        std::cout << "[CR3-Physical] Failed to get physical memory map" << std::endl;
        return candidates;
    }

    // Scan physical memory regions for potential CR3 values
    for (DWORD i = 0; i < pPhys->cMap && candidates.size() < maxCandidates; i++) {
        QWORD paBase = pPhys->pMap[i].pa;
        QWORD paEnd = paBase + pPhys->pMap[i].cb;

        // Skip very small regions
        if (pPhys->pMap[i].cb < 0x1000) continue;

        // Scan in 4KB chunks (page size)
        for (QWORD pa = paBase; pa < paEnd && candidates.size() < maxCandidates; pa += 0x1000) {
            // Check if this could be a valid CR3 (page-aligned, reasonable range)
            if ((pa & 0xFFF) == 0 && pa >= 0x1000 && pa < 0x100000000ULL) {
                // Quick validation - try to read PML4 structure
                BYTE pml4[0x1000];
                DWORD bytesRead = 0;
                if (VMMDLL_MemReadEx(this->getVmm(), (DWORD)-1, pa, pml4, sizeof(pml4), &bytesRead,
                                   VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_NOPAGING)) {
                    // Check for valid PML4 patterns (non-zero entries, valid flags)
                    QWORD* entries = (QWORD*)pml4;
                    int validEntries = 0;
                    for (int j = 0; j < 512; j++) {
                        if (entries[j] != 0 && (entries[j] & 1)) { // Present bit set
                            validEntries++;
                        }
                    }

                    // If we have reasonable number of valid entries, consider it a candidate
                    if (validEntries >= 2 && validEntries <= 100) {
                        candidates.push_back(pa);
                        std::cout << "[CR3-Physical] Found candidate: 0x" << std::hex << pa
                                  << " (valid entries: " << validEntries << ")" << std::dec << std::endl;
                    }
                }
            }
        }
    }

    VMMDLL_MemFree(pPhys);
    std::cout << "[CR3-Physical] Physical scan completed, found " << candidates.size() << " candidates" << std::endl;
    return candidates;
}

std::vector<QWORD> DMAMem::VmmManager::enumerateProcessCR3s(DWORD targetPid)
{
    std::vector<QWORD> candidates;
    if (!this->getVmm()) return candidates;

    std::cout << "[CR3-Process] Enumerating all process CR3s for comparison..." << std::endl;

    // Use a simpler approach - just try some common CR3 patterns
    // Since we can't easily enumerate all processes, we'll generate some candidates

    // Method 1: Try the target process DTB variations
    VMMDLL_PROCESS_INFORMATION pi = { 0 };
    SIZE_T cb = sizeof(pi);
    if (VMMDLL_ProcessGetInformation(this->getVmm(), targetPid, &pi, &cb)) {
        if (pi.paDTB != 0) {
            candidates.push_back(pi.paDTB);
            std::cout << "[CR3-Process] Target process CR3: 0x" << std::hex << pi.paDTB << std::dec << std::endl;

            // Try some variations around the base CR3 (EA might use nearby values)
            for (int offset = -0x10000; offset <= 0x10000; offset += 0x1000) {
                QWORD candidateCR3 = pi.paDTB + offset;
                if (candidateCR3 >= 0x1000 && candidateCR3 != pi.paDTB) {
                    candidates.push_back(candidateCR3);
                }
            }
        }
    }

    // Method 2: Try some common CR3 patterns
    std::vector<QWORD> commonPatterns = {
        0x1000, 0x2000, 0x3000, 0x4000, 0x5000,
        0x10000, 0x20000, 0x30000, 0x40000, 0x50000,
        0x100000, 0x200000, 0x300000, 0x400000, 0x500000
    };

    for (QWORD pattern : commonPatterns) {
        if (std::find(candidates.begin(), candidates.end(), pattern) == candidates.end()) {
            candidates.push_back(pattern);
        }
    }

    std::cout << "[CR3-Process] Process enumeration found " << candidates.size() << " unique CR3s" << std::endl;
    return candidates;
}

BOOL DMAMem::VmmManager::validateCR3ForProcess(DWORD pid, QWORD cr3)
{
    if (!this->getVmm() || cr3 == 0) return FALSE;

    std::cout << "[CR3-Validate] Testing CR3 0x" << std::hex << cr3 << std::dec
              << " for PID " << pid << std::endl;

    // Temporarily set this CR3 for the process
    QWORD originalCR3 = 0;
    VMMDLL_PROCESS_INFORMATION pi = { 0 };
    SIZE_T cb = sizeof(pi);
    if (VMMDLL_ProcessGetInformation(this->getVmm(), pid, &pi, &cb)) {
        originalCR3 = pi.paDTB;
    }

    // Set the new CR3
    if (!VMMDLL_ConfigSet(this->getVmm(), VMMDLL_OPT_PROCESS_DTB | pid, cr3)) {
        std::cout << "[CR3-Validate] Failed to set CR3" << std::endl;
        return FALSE;
    }

    BOOL isValid = FALSE;

    // Test 1: Try to read process modules
    PVMMDLL_MAP_MODULE pModuleMap = nullptr;
    if (VMMDLL_Map_GetModule(this->getVmm(), pid, &pModuleMap, 0) && pModuleMap && pModuleMap->cMap > 0) {
        std::cout << "[CR3-Validate] ✅ Module enumeration successful (" << pModuleMap->cMap << " modules)" << std::endl;
        isValid = TRUE;
    } else {
        std::cout << "[CR3-Validate] ❌ Module enumeration failed" << std::endl;
    }

    if (pModuleMap) VMMDLL_MemFree(pModuleMap);

    // Test 2: Try to read PEB
    if (isValid) {
        QWORD peb = 0;
        if (VMMDLL_ProcessGetInformation(this->getVmm(), pid, &pi, &cb) && pi.win.vaPEB != 0) {
            BYTE pebData[0x100];
            DWORD bytesRead = 0;
            if (VMMDLL_MemReadEx(this->getVmm(), pid, pi.win.vaPEB, pebData, sizeof(pebData), &bytesRead, 0)) {
                std::cout << "[CR3-Validate] ✅ PEB read successful" << std::endl;
            } else {
                std::cout << "[CR3-Validate] ❌ PEB read failed" << std::endl;
                isValid = FALSE;
            }
        }
    }

    // Restore original CR3 if we had one
    if (originalCR3 != 0) {
        VMMDLL_ConfigSet(this->getVmm(), VMMDLL_OPT_PROCESS_DTB | pid, originalCR3);
    }

    std::cout << "[CR3-Validate] CR3 0x" << std::hex << cr3 << std::dec
              << " validation: " << (isValid ? "VALID" : "INVALID") << std::endl;
    return isValid;
}

// ===== CR3 BYPASS METHODS FOR ANTI-CHEAT EVASION =====

// Advanced anti-cheat evasion for BF6/EAC
BOOL DMAMem::VmmManager::readMemoryAntiCheat(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	std::cout << "[CR3 BYPASS] Attempting anti-cheat evasion read at 0x" << std::hex << remoteAddress << std::endl;

	// Try multiple bypass techniques in order of effectiveness

	// Method 1: CR3 bypass with advanced flags
	if (readMemoryWithCR3Bypass(pid, remoteAddress, destination, size)) {
		std::cout << "[CR3 BYPASS] CR3 bypass successful!" << std::endl;
		return TRUE;
	}

	// Method 2: Physical memory access bypass
	ULONG64 physicalFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_FORCECACHE_READ;
	if (VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, physicalFlags)) {
		std::cout << "[CR3 BYPASS] Physical memory bypass successful!" << std::endl;
		return TRUE;
	}

	// Method 3: Kernel memory access
	ULONG64 kernelFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_NOPAGING;
	if (VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, kernelFlags)) {
		std::cout << "[CR3 BYPASS] Kernel memory bypass successful!" << std::endl;
		return TRUE;
	}

	std::cout << "[CR3 BYPASS] All bypass methods failed!" << std::endl;
	return FALSE;
}

// CR3 bypass implementation
BOOL DMAMem::VmmManager::readMemoryWithCR3Bypass(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	std::cout << "[CR3 BYPASS] Attempting CR3 bypass read" << std::endl;

	// Use advanced flags for anti-cheat evasion
	ULONG64 bypassFlags = VMMDLL_FLAG_NOCACHE |
						  VMMDLL_FLAG_NOPAGING |
						  VMMDLL_FLAG_FORCECACHE_READ;

	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, bypassFlags);
}

// Detect CR3 shuffling and attempt bypass
BOOL DMAMem::VmmManager::detectAndBypassCR3Shuffle(DWORD pid)
{
	std::cout << "[CR3 BYPASS] Detecting CR3 shuffling for PID " << pid << std::endl;

	// Try to get process information using available VMM APIs
	VMMDLL_PROCESS_INFORMATION processInfo = {0};
	SIZE_T cbProcessInfo = sizeof(VMMDLL_PROCESS_INFORMATION);

	if (!VMMDLL_ProcessGetInformation(this->getVmm(), pid, &processInfo, &cbProcessInfo)) {
		std::cout << "[CR3 BYPASS] Failed to get process information, assuming CR3 shuffling" << std::endl;
		return TRUE; // Assume shuffling if we can't get info
	}

	// Check basic process information
	std::cout << "[CR3 BYPASS] Process information retrieved successfully" << std::endl;
	std::cout << "[CR3 BYPASS] Process PID: " << processInfo.dwPID << std::endl;
	std::cout << "[CR3 BYPASS] Process State: " << processInfo.dwState << std::endl;

	// If we can't get detailed CR3 info, assume shuffling for BF6/EAC
	std::cout << "[CR3 BYPASS] Assuming CR3 shuffling for BF6/EAC protection" << std::endl;
	return TRUE;
}

// Find correct CR3 value for anti-cheat protected process
QWORD DMAMem::VmmManager::findCorrectCR3(DWORD pid)
{
	std::cout << "[CR3 BYPASS] Searching for correct CR3 value..." << std::endl;

	// For BF6/EAC, we'll use a simplified approach
	// Try to get process information and extract what we can
	VMMDLL_PROCESS_INFORMATION processInfo = {0};
	SIZE_T cbProcessInfo = sizeof(VMMDLL_PROCESS_INFORMATION);

	if (VMMDLL_ProcessGetInformation(this->getVmm(), pid, &processInfo, &cbProcessInfo)) {
		std::cout << "[CR3 BYPASS] Process information available" << std::endl;
		// Return a placeholder CR3 value for now
		return 0x1000; // Standard page boundary
	}

	std::cout << "[CR3 BYPASS] Could not determine CR3, using default" << std::endl;
	return 0;
}

// Validate memory access for anti-cheat protected regions
BOOL DMAMem::VmmManager::validateMemoryAccess(DWORD pid, QWORD address)
{
	std::cout << "[CR3 BYPASS] Validating memory access at 0x" << std::hex << address << std::endl;

	// Try a small test read to validate access
	BYTE testBuffer[8] = {0};
	BOOL result = readMemoryAntiCheat(pid, address, testBuffer, sizeof(testBuffer));

	std::cout << "[CR3 BYPASS] Memory validation result: " << (result ? "SUCCESS" : "FAILED") << std::endl;
	return result;
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid)
{
	return initializeScatter(pid, VMMDLL_FLAG_NOCACHE);
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid, ULONG64 flags)
{
	return VMMDLL_Scatter_Initialize(this->getVmm(), pid, flags);
}

void DMAMem::VmmManager::addScatterRead(VMMDLL_SCATTER_HANDLE handle, QWORD remoteAddress, int size, void* destination)
{
	VMMDLL_Scatter_PrepareEx(handle, remoteAddress, size, (PBYTE)destination, nullptr);
}

void DMAMem::VmmManager::executeScatter(VMMDLL_SCATTER_HANDLE handle)
{
	VMMDLL_Scatter_ExecuteRead(handle);
	VMMDLL_Scatter_CloseHandle(handle);
}

// ===== CS2-STYLE SCATTER OPTIMIZATION IMPLEMENTATIONS =====

DMAMem::VmmManager::ScatterBatch DMAMem::VmmManager::createScatterBatch(DWORD pid, ULONG64 flags)
{
	ScatterBatch batch;
	batch.handle = initializeScatter(pid, flags);
	batch.isInitialized = (batch.handle != nullptr);

	if (batch.isInitialized) {
		std::cout << " [ + ] CS2-Style Scatter Batch Created (PID: " << pid << ")" << std::endl;
	}

	return batch;
}

void DMAMem::VmmManager::addScatterBatchRead(ScatterBatch& batch, QWORD remoteAddress, int size, void* destination)
{
	if (!batch.isInitialized) {
		std::cout << "[ ! ] Cannot add to uninitialized scatter batch" << std::endl;
		return;
	}

	// Add to scatter handle
	addScatterRead(batch.handle, remoteAddress, size, destination);
}

bool DMAMem::VmmManager::executeScatterBatch(ScatterBatch& batch)
{
	if (!batch.isInitialized) {
		std::cout << "[ ! ] Cannot execute uninitialized scatter batch" << std::endl;
		return false;
	}

	// Execute all reads in a single operation (CS2-style)
	executeScatter(batch.handle);

	std::cout << " [ + ] CS2-Style Scatter Batch Executed" << std::endl;

	return true;
}

void DMAMem::VmmManager::cleanupScatterBatch(ScatterBatch& batch)
{
	if (batch.isInitialized && batch.handle) {
		VMMDLL_Scatter_CloseHandle(batch.handle);
		batch.handle = nullptr;
		batch.isInitialized = false;
		batch.readResults.clear();
	}
}

// ===== SIGNATURE SCANNING IMPLEMENTATION =====

// Helper function to convert hex character to byte value
static uint8_t GetByteFromHex(const char* hex) {
		static const uint8_t hexdigits[] = {
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			0,   1,   2,   3,   4,   5,   6,   7,   8,   9,   255, 255, 255, 255, 255, 255,
			255, 10,  11,  12,  13,  14,  15,  255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 10,  11,  12,  13,  14,  15,  255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
			255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255
		};
		return static_cast<uint8_t>((hexdigits[hex[0]] << 4) | (hexdigits[hex[1]]));
	}

QWORD DMAMem::VmmManager::FindSignature(const char* signature, QWORD range_start, QWORD range_end, DWORD pid) {
		if (!signature || signature[0] == '\0' || range_start >= range_end) {
			return 0;
		}

		if (pid == 0) {
			// Use current process - you'll need to track this in your class
			// For now, we'll require explicit PID
			return 0;
		}

		// Calculate buffer size and allocate
		SIZE_T bufferSize = range_end - range_start;
		if (bufferSize > 0x10000000) { // Limit to 256MB for safety
			bufferSize = 0x10000000;
			range_end = range_start + bufferSize;
		}

		std::vector<uint8_t> buffer(bufferSize);
		if (!VMMDLL_MemReadEx(this->getVmm(), pid, range_start, buffer.data(), bufferSize, 0, VMMDLL_FLAG_NOCACHE)) {
			return 0;
		}

		const char* pat = signature;
		QWORD first_match = 0;

		for (QWORD i = 0; i < bufferSize; i++) {
			if (*pat == '?' || buffer[i] == GetByteFromHex(pat)) {
				if (!first_match) {
					first_match = range_start + i;
				}

				if (!pat[2]) { // End of pattern
					return first_match;
				}

				pat += (*pat == '?') ? 2 : 3; // Skip wildcard or hex byte + space
			} else {
				pat = signature;
				first_match = 0;
			}
		}

		return 0; // Pattern not found
	}

std::vector<QWORD> DMAMem::VmmManager::FindAllSignatures(const char* signature, QWORD range_start, QWORD range_end, DWORD maxResults, DWORD pid) {
		std::vector<QWORD> results;
		if (!signature || signature[0] == '\0' || range_start >= range_end || maxResults == 0) {
			return results;
		}

		QWORD currentStart = range_start;
		while (currentStart < range_end && results.size() < maxResults) {
			QWORD found = FindSignature(signature, currentStart, range_end, pid);
			if (found == 0) {
				break;
			}

			results.push_back(found);
			currentStart = found + 1; // Continue search after this match
		}

		return results;
	}

QWORD DMAMem::VmmManager::FindSignatureInModule(const char* signature, const char* moduleName, DWORD pid) {
		if (!signature || !moduleName || pid == 0) {
			return 0;
		}

		// Get module information
		PVMMDLL_MAP_MODULEENTRY moduleEntry = nullptr;
		BOOL result = VMMDLL_Map_GetModuleFromNameU(this->getVmm(), pid, const_cast<LPSTR>(moduleName), &moduleEntry, 0);
		if (!result || !moduleEntry) {
			return 0;
		}

		// Scan within module bounds
		QWORD moduleStart = moduleEntry->vaBase;
		QWORD moduleEnd = moduleEntry->vaBase + moduleEntry->cbImageSize;

		return FindSignature(signature, moduleStart, moduleEnd, pid);
	}

// ===== ENHANCED CR3/DTB BYPASS IMPLEMENTATION =====

struct DTBInfo {
	DWORD index;
	DWORD process_id;
	QWORD dtb;
	QWORD kernelAddr;
	std::string name;
};

// Global callback variables for DTB enumeration
static SIZE_T g_cbSize = 0;
static VOID cbAddFile(HANDLE h, LPSTR uszName, ULONG64 cb, PVMMDLL_VFS_FILELIST_EXINFO pExInfo) {
	if (strcmp(uszName, "dtb.txt") == 0) {
		g_cbSize = cb;
	}
}

BOOL DMAMem::VmmManager::FixCR3Advanced(DWORD pid) {
		// First check if we can already access the process
		PVMMDLL_MAP_MODULEENTRY moduleEntry = nullptr;
		BOOL result = VMMDLL_Map_GetModuleFromNameU(this->getVmm(), pid, const_cast<LPSTR>("ntdll.dll"), &moduleEntry, 0);
		if (result) {
			return TRUE; // Already working, no fix needed
		}

		// Initialize plugins for DTB enumeration
		if (!VMMDLL_InitializePlugins(this->getVmm())) {
			std::cout << "[-] Failed VMMDLL_InitializePlugins call" << std::endl;
			return FALSE;
		}

		// Wait for plugin initialization
		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		// Wait for procinfo plugin to complete
		while (true) {
			BYTE bytes[4] = { 0 };
			DWORD bytesRead = 0;
			auto status = VMMDLL_VfsReadW(this->getVmm(), const_cast<LPWSTR>(L"\\misc\\procinfo\\progress_percent.txt"), bytes, 3, &bytesRead, 0);
			if (status == VMMDLL_STATUS_SUCCESS && atoi(reinterpret_cast<LPSTR>(bytes)) == 100) {
				break;
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(100));
		}

		// Get DTB information
		VMMDLL_VFS_FILELIST2 VfsFileList = { 0 };
		VfsFileList.dwVersion = VMMDLL_VFS_FILELIST_VERSION;
		VfsFileList.pfnAddFile = cbAddFile;

		result = VMMDLL_VfsListU(this->getVmm(), const_cast<LPSTR>("\\misc\\procinfo\\"), &VfsFileList);
		if (!result) {
			std::cout << "[-] Failed to list VFS files" << std::endl;
			return FALSE;
		}

		// Read DTB data
		const size_t buffer_size = g_cbSize;
		if (buffer_size == 0) {
			std::cout << "[-] DTB file size is 0" << std::endl;
			return FALSE;
		}

		std::unique_ptr<BYTE[]> bytes(new BYTE[buffer_size]);
		DWORD bytesRead = 0;
		auto status = VMMDLL_VfsReadW(this->getVmm(), const_cast<LPWSTR>(L"\\misc\\procinfo\\dtb.txt"), bytes.get(), buffer_size - 1, &bytesRead, 0);
		if (status != VMMDLL_STATUS_SUCCESS) {
			std::cout << "[-] Failed to read DTB file" << std::endl;
			return FALSE;
		}

		// Parse DTB candidates
		std::vector<QWORD> possibleDTBs = GetOrphanedDTBs();

		// Try each DTB candidate
		for (const auto& dtb : possibleDTBs) {
			if (ValidateDTBForProcess(pid, dtb)) {
				std::cout << "[+] Successfully fixed CR3 with DTB: 0x" << std::hex << dtb << std::endl;
				return TRUE;
			}
		}

		std::cout << "[-] Failed to find working DTB" << std::endl;
		return FALSE;
	}

std::vector<QWORD> DMAMem::VmmManager::GetOrphanedDTBs() {
		std::vector<QWORD> orphanedDTBs;

		// This would need to be implemented based on the DTB enumeration
		// For now, return empty vector - full implementation would parse dtb.txt
		// and extract DTBs with PID = 0 or matching process names

		return orphanedDTBs;
	}

BOOL DMAMem::VmmManager::ValidateDTBForProcess(DWORD pid, QWORD dtb) {
		// Set the DTB for this process
		VMMDLL_ConfigSet(this->getVmm(), VMMDLL_OPT_PROCESS_DTB | pid, dtb);

		// Try to read a known structure (like ntdll.dll module)
		PVMMDLL_MAP_MODULEENTRY moduleEntry = nullptr;
		BOOL result = VMMDLL_Map_GetModuleFromNameU(this->getVmm(), pid, const_cast<LPSTR>("ntdll.dll"), &moduleEntry, 0);

		return result;
	}

BOOL DMAMem::VmmManager::DumpProcessMemory(DWORD pid, QWORD address, SIZE_T size, const char* filePath) {
	if (!filePath || size == 0) {
			return FALSE;
		}

		// Allocate buffer
		std::vector<BYTE> buffer(size);

		// Read memory
		if (!VMMDLL_MemReadEx(this->getVmm(), pid, address, buffer.data(), size, nullptr, VMMDLL_FLAG_NOCACHE)) {
			std::cout << "[-] Failed to read memory for dump" << std::endl;
			return FALSE;
		}

		// Write to file
		std::ofstream file(filePath, std::ios::binary);
		if (!file.is_open()) {
			std::cout << "[-] Failed to open dump file: " << filePath << std::endl;
			return FALSE;
		}

		file.write(reinterpret_cast<const char*>(buffer.data()), size);
		file.close();

		std::cout << "[+] Memory dumped to: " << filePath << std::endl;
		return TRUE;
	}


