#pragma once
#include "DMAMemoryManagement/includes.h"

namespace BF6 {
    class VehicleEntityData : public DMAMem::MemoryObject {
    public:
        QWORD NameSidPtr;

        VehicleEntityData() {
            // BF6 VehicleEntityData offsets from BF2042 analysis
            this->registerOffset(0x0280, &NameSidPtr, sizeof(QWORD)); // m_NameSid pointer
        }

        // Helper methods
        bool isValid() const {
            return NameSidPtr != 0;
        }

        std::string getVehicleName() const {
            // TODO: Implement BF2042-specific string reading when needed
            return "Vehicle";
        }
    };
}
